#!/bin/bash

echo "QuickPosters Firebase Upload Script"
echo "================================"
echo

echo "Checking for Node.js..."
if ! command -v node &> /dev/null; then
  echo "Node.js is not installed or not in PATH."
  echo "Please install Node.js from https://nodejs.org/"
  echo "Then run this script again."
  exit 1
fi

echo "Node.js found. Checking version..."
node -v
echo

echo "Checking for npm..."
if ! command -v npm &> /dev/null; then
  echo "npm is not installed or not in PATH."
  echo "Please install Node.js from https://nodejs.org/"
  echo "Then run this script again."
  exit 1
fi

echo "npm found. Checking version..."
npm -v
echo

echo "Checking for required files..."
if [ ! -f parties.json ]; then
  echo "parties.json not found in the current directory."
  echo "Please make sure parties.json is in the same directory as this script."
  exit 1
fi

if [ ! -f simplified_upload.js ]; then
  echo "simplified_upload.js not found in the current directory."
  echo "Please make sure simplified_upload.js is in the same directory as this script."
  exit 1
fi

if [ ! -f serviceAccountKey.json ]; then
  echo "serviceAccountKey.json not found in the current directory."
  echo "Please download your Firebase service account key and save it as serviceAccountKey.json"
  echo "in the same directory as this script."
  echo
  echo "To download your service account key:"
  echo "1. Go to the Firebase Console (https://console.firebase.google.com/)"
  echo "2. Select your project"
  echo "3. Go to Project Settings > Service Accounts"
  echo "4. Click \"Generate new private key\""
  echo "5. Save the downloaded file as serviceAccountKey.json in this directory"
  exit 1
fi

echo "All required files found."
echo

echo "Installing dependencies..."
npm install firebase-admin
if [ $? -ne 0 ]; then
  echo "Failed to install dependencies."
  exit 1
fi
echo "Dependencies installed successfully."
echo

echo "Running upload script..."
node simplified_upload.js
if [ $? -ne 0 ]; then
  echo "Upload script failed."
  exit 1
fi

echo
echo "Upload completed successfully!"
echo
echo "You can now check your Firebase Firestore database to verify the data."
echo

# Make the script executable
chmod +x upload_to_firebase.sh
