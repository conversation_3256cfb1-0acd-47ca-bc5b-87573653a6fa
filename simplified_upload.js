/**
 * Simplified script to upload parties and political parameters to Firebase Firestore
 * 
 * Prerequisites:
 * 1. Node.js installed
 * 2. Firebase Admin SDK installed: npm install firebase-admin
 * 3. Service account key downloaded as serviceAccountKey.json
 * 4. parties.json file in the same directory
 */

const admin = require('firebase-admin');
const fs = require('fs');

// Initialize Firebase Admin SDK
try {
  admin.initializeApp({
    credential: admin.credential.cert(require('./serviceAccountKey.json'))
  });
} catch (e) {
  console.error('Error initializing Firebase:', e);
  process.exit(1);
}

const db = admin.firestore();

// Read the parties.json file
let partiesData;
try {
  partiesData = JSON.parse(fs.readFileSync('./parties.json', 'utf8'));
  console.log(`Read ${partiesData.length} parties from parties.json`);
} catch (e) {
  console.error('Error reading parties.json:', e);
  process.exit(1);
}

// Define the political parameters
const politicalParameters = [
  {
    id: 'party_name',
    name: 'Party Name',
    description: 'Your political party affiliation',
    type: 'party_reference',
    isRequired: true,
    isActive: true,
    displayOrder: 1,
    collectionReference: 'politicalParties',
    displayField: 'shortName',
    valueField: 'id',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'position',
    name: 'Political Position',
    description: 'Your current political role or position',
    type: 'dropdown',
    isRequired: true,
    isActive: true,
    displayOrder: 2,
    dropdownOptions: [
      'MLA', 'MP', 'Corporator', 'Sarpanch', 'Party Worker', 
      'Party Leader', 'Minister', 'Chief Minister', 'Opposition Leader', 'Other'
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'constituency',
    name: 'Constituency',
    description: 'The constituency or area you represent',
    type: 'string',
    isRequired: true,
    isActive: true,
    displayOrder: 3,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'election_year',
    name: 'Election Year',
    description: 'Year of your last election or upcoming election',
    type: 'number',
    isRequired: false,
    isActive: true,
    displayOrder: 4,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'experience',
    name: 'Political Experience',
    description: 'Number of years in politics',
    type: 'number',
    isRequired: false,
    isActive: true,
    displayOrder: 5,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'campaign_slogan',
    name: 'Campaign Slogan',
    description: 'Your campaign slogan or tagline',
    type: 'string',
    isRequired: false,
    isActive: true,
    displayOrder: 6,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'achievements',
    name: 'Achievements',
    description: 'Your key political achievements or initiatives',
    type: 'string',
    isRequired: false,
    isActive: true,
    displayOrder: 7,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'campaign_office',
    name: 'Campaign Office Address',
    description: 'Address of your campaign or constituency office',
    type: 'string',
    isRequired: false,
    isActive: true,
    displayOrder: 8,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'campaign_contact',
    name: 'Campaign Contact',
    description: 'Contact number for campaign-related inquiries',
    type: 'string',
    isRequired: false,
    isActive: true,
    displayOrder: 9,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'political_photo',
    name: 'Political Photo',
    description: 'Your official political photograph',
    type: 'string',
    isRequired: false,
    isActive: true,
    displayOrder: 0,  // Setting to 0 so it appears at the top
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Function to upload parties to Firestore
async function uploadParties() {
  console.log('Starting upload of parties to Firestore...');
  
  for (const party of partiesData) {
    const documentId = party.documentId;
    const fields = party.fields;
    
    try {
      await db.collection('politicalParties').doc(documentId).set(fields);
      console.log(`Uploaded party: ${fields.name} (${documentId})`);
    } catch (error) {
      console.error(`Error uploading party ${documentId}:`, error);
    }
  }
  
  console.log('Finished uploading parties to Firestore.');
}

// Function to upload political parameters to Firestore
async function uploadPoliticalParameters() {
  console.log('Starting upload of political parameters to Firestore...');
  
  for (const parameter of politicalParameters) {
    const paramId = parameter.id;
    
    try {
      await db.collection('politicalParameters').doc(paramId).set(parameter);
      console.log(`Uploaded parameter: ${parameter.name} (${paramId})`);
    } catch (error) {
      console.error(`Error uploading parameter ${paramId}:`, error);
    }
  }
  
  console.log('Finished uploading political parameters to Firestore.');
}

// Main function to run the upload process
async function main() {
  try {
    await uploadParties();
    await uploadPoliticalParameters();
    console.log('Upload process completed successfully!');
  } catch (error) {
    console.error('Upload process failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the main function
main();
