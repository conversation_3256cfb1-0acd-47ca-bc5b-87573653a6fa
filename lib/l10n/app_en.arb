{"@@locale": "en", "@@last_modified": "2024-01-15T10:00:00.000Z", "appTitle": "Quick Posters", "@appTitle": {"description": "The title of the application"}, "welcome": "Welcome to Quick Posters", "@welcome": {"description": "Welcome message displayed to users"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "logout": "Logout", "@logout": {"description": "Logout menu item"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Phone number field label"}, "enterPhoneNumber": "Enter your phone number", "@enterPhoneNumber": {"description": "Phone number field hint"}, "sendOTP": "Send OTP", "@sendOTP": {"description": "Send OTP button text"}, "verifyOTP": "Verify OTP", "@verifyOTP": {"description": "Verify OTP button text"}, "enterOTP": "Enter the 6-digit OTP", "@enterOTP": {"description": "OTP input placeholder"}, "resendOTP": "Resend OTP", "@resendOTP": {"description": "Resend OTP button text"}, "home": "Home", "@home": {"description": "Home navigation item"}, "templates": "Templates", "@templates": {"description": "Templates section title"}, "categories": "Categories", "@categories": {"description": "Categories section title"}, "all": "All", "@all": {"description": "All categories option"}, "premium": "Premium", "@premium": {"description": "Premium features label"}, "profile": "Profile", "@profile": {"description": "Profile section title"}, "settings": "Settings", "@settings": {"description": "Settings section title"}, "language": "Language", "@language": {"description": "Language settings option"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Language selection screen title"}, "businessUser": "Business", "@businessUser": {"description": "Business user type"}, "politicalUser": "Politician", "@politicalUser": {"description": "Political user type"}, "individualUser": "Individual User", "@individualUser": {"description": "Individual user type label"}, "createPoster": "Create Poster", "@createPoster": {"description": "Create poster button text"}, "editTemplate": "Edit Template", "@editTemplate": {"description": "Edit template button text"}, "saveImage": "Save Image", "@saveImage": {"description": "Save image button text"}, "shareImage": "Share Image", "@shareImage": {"description": "Share image button"}, "premiumFeature": "Premium Feature", "@premiumFeature": {"description": "Premium feature indicator"}, "upgradeNow": "Upgrade Now", "@upgradeNow": {"description": "Upgrade to premium button text"}, "subscriptionPlans": "Subscription Plans", "@subscriptionPlans": {"description": "Subscription plans screen title"}, "trialPlan": "Trial Plan", "@trialPlan": {"description": "Trial subscription plan name"}, "threeMonthPlan": "3-Month Plan", "@threeMonthPlan": {"description": "3-month subscription plan name"}, "sixMonthPlan": "6-Month Plan", "@sixMonthPlan": {"description": "6-month subscription plan name"}, "annualPlan": "Annual Plan", "@annualPlan": {"description": "Annual subscription plan name"}, "rupees": "₹{amount}", "@rupees": {"description": "Currency format for Indian Rupees", "placeholders": {"amount": {"type": "String"}}}, "myProfile": "My Profile", "@myProfile": {"description": "My Profile menu item"}, "notifications": "Notifications", "@notifications": {"description": "Notifications menu item"}, "about": "About", "@about": {"description": "About section title"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Privacy Policy menu item"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Terms of Service title"}, "refundPolicy": "Refund Policy", "@refundPolicy": {"description": "Refund Policy title"}, "aiImageGenerator": "AI Image Generator", "@aiImageGenerator": {"description": "AI Image Generator menu item"}, "adminDashboard": "Admin Dashboard", "@adminDashboard": {"description": "Admin dashboard menu item"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "save": "Save", "@save": {"description": "Save button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Error message title"}, "success": "Success", "@success": {"description": "Success message title"}, "tryAgain": "Try Again", "@tryAgain": {"description": "Try again button text"}, "noInternetConnection": "No internet connection", "@noInternetConnection": {"description": "No internet connection error message"}, "somethingWentWrong": "Something went wrong", "@somethingWentWrong": {"description": "Generic error message"}, "phoneNumberValidation": "Please enter a valid 10-digit phone number", "@phoneNumberValidation": {"description": "Phone number validation error"}, "otpValidation": "Please enter a valid 6-digit OTP", "@otpValidation": {"description": "OTP validation error"}, "otpSent": "OTP sent successfully", "@otpSent": {"description": "OTP sent confirmation message"}, "otpExpired": "OTP has expired. Please request a new one", "@otpExpired": {"description": "OTP expired error message"}, "invalidOtp": "Invalid OTP. Please try again", "@invalidOtp": {"description": "Invalid OTP error message"}, "loginSuccessful": "Login successful", "@loginSuccessful": {"description": "Login success message"}, "welcomeBack": "Welcome back!", "@welcomeBack": {"description": "Welcome back message for returning users"}, "getStarted": "Get Started", "@getStarted": {"description": "Get started button text"}, "continueText": "Continue", "@continueText": {"description": "Continue button text"}, "skip": "<PERSON><PERSON>", "@skip": {"description": "Skip button text"}, "next": "Next", "@next": {"description": "Next button text"}, "previous": "Previous", "@previous": {"description": "Previous button text"}, "done": "Done", "@done": {"description": "Done button text"}, "close": "Close", "@close": {"description": "Close button text"}, "back": "Back", "@back": {"description": "Back button text"}, "selectUserType": "Select User Type", "@selectUserType": {"description": "User type selection hint"}, "chooseAccountType": "Choose your account type", "@chooseAccountType": {"description": "Account type selection instruction"}, "businessDescription": "Business Description", "@businessDescription": {"description": "Business description field label"}, "politicalDescription": "Ideal for political campaigns and leaders", "@politicalDescription": {"description": "Political user type description"}, "individualDescription": "Great for personal use and social media", "@individualDescription": {"description": "Individual user type description"}, "completeProfile": "Complete Your Profile", "@completeProfile": {"description": "Profile completion screen title"}, "profileCompletionDescription": "Help us personalize your experience", "@profileCompletionDescription": {"description": "Profile completion description"}, "businessName": "Business Name", "@businessName": {"description": "Business name field label"}, "enterBusinessName": "Enter your business name", "@enterBusinessName": {"description": "Business name input placeholder"}, "businessCategory": "Business Category", "@businessCategory": {"description": "Business category field label"}, "selectBusinessCategory": "Select your business category", "@selectBusinessCategory": {"description": "Business category selection placeholder"}, "politicalParty": "Political Party", "@politicalParty": {"description": "Political party selection label"}, "selectPoliticalParty": "Select your political party", "@selectPoliticalParty": {"description": "Political party selection placeholder"}, "designation": "Designation", "@designation": {"description": "Designation input label"}, "enterDesignation": "Enter your designation", "@enterDesignation": {"description": "Designation input placeholder"}, "fullName": "Full Name", "@fullName": {"description": "Full name input label"}, "enterFullName": "Enter your full name", "@enterFullName": {"description": "Full name input placeholder"}, "profileUpdated": "Profile updated successfully", "@profileUpdated": {"description": "Profile update success message"}, "exploreTemplates": "Explore Templates", "@exploreTemplates": {"description": "Explore templates button text"}, "featuredTemplates": "Featured Templates", "@featuredTemplates": {"description": "Featured templates section title"}, "recentTemplates": "Recent Templates", "@recentTemplates": {"description": "Recent templates section title"}, "popularTemplates": "Popular Templates", "@popularTemplates": {"description": "Popular templates section title"}, "loadingTemplates": "Loading templates...", "@loadingTemplates": {"description": "Loading templates message"}, "noTemplatesFound": "No templates found", "@noTemplatesFound": {"description": "No templates found message"}, "searchTemplates": "Search templates...", "@searchTemplates": {"description": "Search templates placeholder"}, "filterByCategory": "Filter by Category", "@filterByCategory": {"description": "Filter by category label"}, "viewAll": "View All", "@viewAll": {"description": "View all button text"}, "loadMore": "Load More", "@loadMore": {"description": "Load more button text"}, "refreshing": "Refreshing...", "@refreshing": {"description": "Refreshing indicator text"}, "pullToRefresh": "Pull to refresh", "@pullToRefresh": {"description": "Pull to refresh instruction"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button tooltip"}, "account": "Account", "@account": {"description": "Account section title"}, "appSettings": "App Settings", "@appSettings": {"description": "App Settings section title"}, "tools": "Tools", "@tools": {"description": "Tools section title"}, "premiumSubscription": "Premium Subscription", "@premiumSubscription": {"description": "Premium subscription menu item"}, "youArePremiumUser": "You are a premium user", "@youArePremiumUser": {"description": "Premium user status message"}, "upgradeToPremium": "Upgrade to premium", "@upgradeToPremium": {"description": "Upgrade to premium message"}, "createImagesWithAI": "Create images with AI", "@createImagesWithAI": {"description": "AI image generator description"}, "aboutQuickPosters": "About QuickPosters", "@aboutQuickPosters": {"description": "About QuickPosters menu item"}, "version": "Version 1.0.0", "@version": {"description": "App version"}, "readPrivacyPolicy": "Read our privacy policy", "@readPrivacyPolicy": {"description": "Privacy policy description"}, "viewEditProfile": "View and edit your profile", "@viewEditProfile": {"description": "Profile menu description"}, "changeAppLanguage": "Change app language", "@changeAppLanguage": {"description": "Language menu description"}, "failedToLoadUserData": "Failed to load user data", "@failedToLoadUserData": {"description": "Error message when user data fails to load"}, "profileUpdatedSuccessfully": "Profile updated successfully", "@profileUpdatedSuccessfully": {"description": "Success message when profile is updated"}, "failedToSaveProfile": "Failed to save profile", "@failedToSaveProfile": {"description": "Error message when profile save fails"}, "manageBusinessProfile": "Manage Business Profile", "@manageBusinessProfile": {"description": "Business profile management title"}, "managePoliticalProfile": "Manage Political Profile", "@managePoliticalProfile": {"description": "Political profile management title"}, "configurePoliticalDetails": "Configure your political details, party, and other parameters", "@configurePoliticalDetails": {"description": "Political configuration description"}, "businessParameters": "Business Parameters", "@businessParameters": {"description": "Business parameters page title"}, "politicalParameters": "Political Parameters", "@politicalParameters": {"description": "Political parameters page title"}, "contentRefreshedSuccessfully": "Content refreshed successfully", "@contentRefreshedSuccessfully": {"description": "Content refresh success message"}, "failedToRefreshContent": "Failed to refresh content", "@failedToRefreshContent": {"description": "Content refresh error message"}, "mySubscription": "My Subscription", "@mySubscription": {"description": "My subscription page title"}, "startByAddingBaseImage": "Start by adding a base image", "@startByAddingBaseImage": {"description": "Image editor initial instruction"}, "backgroundOfPoster": "This will be the background of your poster", "@backgroundOfPoster": {"description": "Base image description"}, "selectBaseImage": "Select Base Image", "@selectBaseImage": {"description": "Select base image button"}, "addBaseImage": "Add Base Image", "@addBaseImage": {"description": "Add base image button"}, "aboutPageComingSoon": "About page coming soon", "@aboutPageComingSoon": {"description": "About page placeholder message"}, "languageSettingsComingSoon": "Language settings coming soon", "@languageSettingsComingSoon": {"description": "Language settings placeholder message"}, "notificationSettingsComingSoon": "Notification settings coming soon", "@notificationSettingsComingSoon": {"description": "Notification settings placeholder message"}, "manageNotificationSettings": "Manage notification settings", "@manageNotificationSettings": {"description": "Notification settings description"}, "imageShape": "Image Shape", "@imageShape": {"description": "Image shape selection title"}, "businessLogoShape": "Business Logo Shape", "@businessLogoShape": {"description": "Business logo shape selection title"}, "profilePhoto": "Profile Photo", "@profilePhoto": {"description": "Profile photo option"}, "businessLogo": "Business Logo", "@businessLogo": {"description": "Business logo field label"}, "pleaseSelectUserType": "Please select a user type", "@pleaseSelectUserType": {"description": "User type selection validation message"}, "failedToLoadUserTypes": "Failed to load user types", "@failedToLoadUserTypes": {"description": "Error message when user types fail to load"}, "failedToSaveUserType": "Failed to save user type", "@failedToSaveUserType": {"description": "Error message when user type save fails"}, "profileCompletedSuccessfully": "Profile completed successfully", "@profileCompletedSuccessfully": {"description": "Success message when profile is completed"}, "viewEditProfileInfo": "View and edit your profile information", "@viewEditProfileInfo": {"description": "Profile settings description"}, "chooseImageSource": "Choose Image Source", "@chooseImageSource": {"description": "Image source selection dialog title"}, "customImage": "Custom Image", "@customImage": {"description": "Custom image option"}, "autoSelect": "Auto Select", "@autoSelect": {"description": "Auto select option"}, "imageSource": "Image Source", "@imageSource": {"description": "Image source selection title"}, "shapeOptions": "Shape Options", "@shapeOptions": {"description": "Shape options section title"}, "none": "None", "@none": {"description": "None option"}, "circle": "Circle", "@circle": {"description": "Circle shape option"}, "rectangle": "Rectangle", "@rectangle": {"description": "Rectangle shape option"}, "roundedRectangle": "Rounded Rectangle", "@roundedRectangle": {"description": "Rounded rectangle shape option"}, "diamond": "Diamond", "@diamond": {"description": "Diamond shape option"}, "hexagon": "Hexagon", "@hexagon": {"description": "Hexagon shape option"}, "quickPosters": "QuickPosters", "@quickPosters": {"description": "App name in drawer"}, "accountActions": "Account Actions", "@accountActions": {"description": "Account actions section title"}, "signOutFromAccount": "Sign out from your account", "@signOutFromAccount": {"description": "Logout description"}, "changingLanguage": "Changing language...", "@changingLanguage": {"description": "Language change loading message"}, "phoneNumberLabel": "Phone Number", "@phoneNumberLabel": {"description": "Phone number field label"}, "enterYourPhoneNumber": "Enter your phone number", "@enterYourPhoneNumber": {"description": "Phone number field hint"}, "userType": "User Type", "@userType": {"description": "User type field label"}, "saveToGallery": "Save to Gallery", "@saveToGallery": {"description": "Save to gallery button text"}, "share": "Share", "@share": {"description": "Share button text"}, "whatsApp": "WhatsApp", "@whatsApp": {"description": "WhatsApp share option"}, "invalidPhoneNumber": "The phone number format is incorrect. Please enter a valid phone number.", "@invalidPhoneNumber": {"description": "Invalid phone number error message"}, "quotaExceeded": "SMS quota exceeded. Please try again later.", "@quotaExceeded": {"description": "SMS quota exceeded error message"}, "userDisabled": "This user has been disabled. Please contact support.", "@userDisabled": {"description": "User disabled error message"}, "operationNotAllowed": "Phone authentication is not enabled. Please contact support.", "@operationNotAllowed": {"description": "Operation not allowed error message"}, "captchaCheckFailed": "reCAPTCHA verification failed. Please try again.", "@captchaCheckFailed": {"description": "Captcha check failed error message"}, "missingClientIdentifier": "The app verification process failed. Please try again or contact support.", "@missingClientIdentifier": {"description": "Missing client identifier error message"}, "tooManyRequests": "Too many requests from this device. We have temporarily blocked all requests from this device due to unusual activity. Please try again after some time (usually a few hours).", "@tooManyRequests": {"description": "Too many requests error message"}, "verificationFailed": "Verification failed. Please try again.", "@verificationFailed": {"description": "Generic verification failed message"}, "failedToGetUserData": "Failed to get user data", "@failedToGetUserData": {"description": "Failed to get user data error message"}, "userNotFound": "User not found", "@userNotFound": {"description": "User not found error message"}, "politician": "Politician", "@politician": {"description": "Politician user type"}, "businessman": "Businessman", "@businessman": {"description": "Businessman user type"}, "regularUser": "Regular", "@regularUser": {"description": "Regular user type"}, "forPoliticalCampaigns": "For political campaigns and promotions", "@forPoliticalCampaigns": {"description": "Politician user type description"}, "forBusinessPromotions": "For business promotions and advertisements", "@forBusinessPromotions": {"description": "Businessman user type description"}, "forPersonalUse": "For personal use and general purposes", "@forPersonalUse": {"description": "Regular user type description"}, "developmentLogin": "Development Login", "@developmentLogin": {"description": "Development login page title"}, "developmentMode": "Development Mode", "@developmentMode": {"description": "Development mode title"}, "loggedInAsDeveloper": "You are logged in as a developer.\nThis is a temporary mode while Firebase phone authentication is blocked.", "@loggedInAsDeveloper": {"description": "Development mode description"}, "noActiveSubscription": "No active subscription", "@noActiveSubscription": {"description": "No active subscription status"}, "expiresInDays": "Expires in {days} days", "@expiresInDays": {"description": "Subscription expires in X days", "placeholders": {"days": {"type": "int"}}}, "activeUntil": "Active until {date}", "@activeUntil": {"description": "Subscription active until date", "placeholders": {"date": {"type": "String"}}}, "downloadImage": "Download Image", "@downloadImage": {"description": "Download image button"}, "shareOnWhatsApp": "Share on WhatsApp", "@shareOnWhatsApp": {"description": "Share on WhatsApp button"}, "customImageSelected": "Custom image selected!", "@customImageSelected": {"description": "Success message when custom image is selected"}, "errorSelectingImage": "Error selecting image: {error}", "@errorSelectingImage": {"description": "Error message when selecting image fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "imageAddedSuccessfully": "Image added successfully!", "@imageAddedSuccessfully": {"description": "Image added success message"}, "errorAddingImage": "Error adding image: {error}", "@errorAddingImage": {"description": "Error adding image message", "placeholders": {"error": {"type": "String"}}}, "noImageAvailable": "No {source} image available", "@noImageAvailable": {"description": "No image available message", "placeholders": {"source": {"type": "String"}}}, "emailAddress": "Email Address", "@emailAddress": {"description": "Email address field label"}, "enterEmailAddress": "Enter your email address", "@enterEmailAddress": {"description": "Email address field hint"}, "optional": "Optional", "@optional": {"description": "Optional field indicator"}, "selectProfileImage": "Select Profile Image", "@selectProfileImage": {"description": "Select profile image button"}, "camera": "Camera", "@camera": {"description": "Camera option"}, "gallery": "Gallery", "@gallery": {"description": "Gallery option"}, "removeImage": "Remove Image", "@removeImage": {"description": "Remove image option"}, "saveProfile": "Save Profile", "@saveProfile": {"description": "Save profile button"}, "updateProfile": "Update Profile", "@updateProfile": {"description": "Update profile button"}, "profileImageUpdated": "Profile image updated successfully", "@profileImageUpdated": {"description": "Profile image update success message"}, "failedToUpdateProfileImage": "Failed to update profile image", "@failedToUpdateProfileImage": {"description": "Profile image update error message"}, "pleaseEnterValidEmail": "Please enter a valid email address", "@pleaseEnterValidEmail": {"description": "Email validation error message"}, "pleaseEnterName": "Please enter your name", "@pleaseEnterName": {"description": "Name validation error message"}, "nameTooShort": "Name must be at least 2 characters long", "@nameTooShort": {"description": "Name too short validation error"}, "invalidEmailFormat": "Please enter a valid email format", "@invalidEmailFormat": {"description": "Invalid email format error"}, "business": "Business", "@business": {"description": "Business category"}, "event": "Event", "@event": {"description": "Event category"}, "festival": "Festival", "@festival": {"description": "Festival category"}, "party": "Party", "@party": {"description": "Party category"}, "education": "Education", "@education": {"description": "Education category"}, "health": "Health", "@health": {"description": "Health category"}, "technology": "Technology", "@technology": {"description": "Technology category"}, "food": "Food", "@food": {"description": "Food category"}, "travel": "Travel", "@travel": {"description": "Travel category"}, "sports": "Sports", "@sports": {"description": "Sports category"}, "fashion": "Fashion", "@fashion": {"description": "Fashion category"}, "realEstate": "Real Estate", "@realEstate": {"description": "Real Estate category"}, "flyers": "Flyers", "@flyers": {"description": "Flyers subcategory"}, "brochures": "Brochures", "@brochures": {"description": "Brochures subcategory"}, "businessCards": "Business Cards", "@businessCards": {"description": "Business Cards subcategory"}, "presentations": "Presentations", "@presentations": {"description": "Presentations subcategory"}, "logos": "Logos", "@logos": {"description": "Logos subcategory"}, "invitations": "Invitations", "@invitations": {"description": "Invitations subcategory"}, "tickets": "Tickets", "@tickets": {"description": "Tickets subcategory"}, "programs": "Programs", "@programs": {"description": "Programs subcategory"}, "banners": "Banners", "@banners": {"description": "Banners subcategory"}, "posters": "Posters", "@posters": {"description": "Posters subcategory"}, "celebration": "Celebration", "@celebration": {"description": "Celebration subcategory"}, "religious": "Religious", "@religious": {"description": "Religious subcategory"}, "cultural": "Cultural", "@cultural": {"description": "Cultural subcategory"}, "seasonal": "Seasonal", "@seasonal": {"description": "Seasonal subcategory"}, "birthday": "Birthday", "@birthday": {"description": "Birthday subcategory"}, "wedding": "Wedding", "@wedding": {"description": "Wedding subcategory"}, "anniversary": "Anniversary", "@anniversary": {"description": "Anniversary subcategory"}, "graduation": "Graduation", "@graduation": {"description": "Graduation subcategory"}, "certificates": "Certificates", "@certificates": {"description": "Certificates subcategory"}, "worksheets": "Worksheets", "@worksheets": {"description": "Worksheets subcategory"}, "awareness": "Awareness", "@awareness": {"description": "Awareness subcategory"}, "medical": "Medical", "@medical": {"description": "Medical subcategory"}, "fitness": "Fitness", "@fitness": {"description": "Fitness subcategory"}, "nutrition": "Nutrition", "@nutrition": {"description": "Nutrition subcategory"}, "appPromotion": "App Promotion", "@appPromotion": {"description": "App Promotion subcategory"}, "software": "Software", "@software": {"description": "Software subcategory"}, "digitalServices": "Digital Services", "@digitalServices": {"description": "Digital Services subcategory"}, "menu": "<PERSON><PERSON>", "@menu": {"description": "Menu subcategory"}, "recipe": "Recipe", "@recipe": {"description": "Recipe subcategory"}, "restaurant": "Restaurant", "@restaurant": {"description": "Restaurant subcategory"}, "catering": "Catering", "@catering": {"description": "Catering subcategory"}, "tourism": "Tourism", "@tourism": {"description": "Tourism subcategory"}, "hotels": "Hotels", "@hotels": {"description": "Hotels subcategory"}, "transportation": "Transportation", "@transportation": {"description": "Transportation subcategory"}, "adventure": "Adventure", "@adventure": {"description": "Adventure subcategory"}, "team": "Team", "@team": {"description": "Team subcategory"}, "tournament": "Tournament", "@tournament": {"description": "Tournament subcategory"}, "equipment": "Equipment", "@equipment": {"description": "Equipment subcategory"}, "clothing": "Clothing", "@clothing": {"description": "Clothing subcategory"}, "accessories": "Accessories", "@accessories": {"description": "Accessories subcategory"}, "beauty": "Beauty", "@beauty": {"description": "Beauty subcategory"}, "style": "Style", "@style": {"description": "Style subcategory"}, "property": "Property", "@property": {"description": "Property subcategory"}, "rental": "Rental", "@rental": {"description": "Rental subcategory"}, "commercial": "Commercial", "@commercial": {"description": "Commercial subcategory"}, "residential": "Residential", "@residential": {"description": "Residential subcategory"}, "noShape": "No Shape", "@noShape": {"description": "No shape option for image"}, "rounded": "Rounded", "@rounded": {"description": "Rounded rectangle shape option"}, "loadMoreTemplates": "Load More Templates", "@loadMoreTemplates": {"description": "Load more templates button text"}, "horizontal": "Horizontal", "@horizontal": {"description": "Horizontal layout type"}, "vertical": "Vertical", "@vertical": {"description": "Vertical layout type"}, "square": "Square", "@square": {"description": "Square layout type"}, "smallBusiness": "Small Business", "@smallBusiness": {"description": "Small business target audience"}, "eventOrganizers": "Event Organizers", "@eventOrganizers": {"description": "Event organizers target audience"}, "educators": "Educators", "@educators": {"description": "Educators target audience"}, "marketers": "Marketers", "@marketers": {"description": "Marketers target audience"}, "students": "Students", "@students": {"description": "Students target audience"}, "professionals": "Professionals", "@professionals": {"description": "Professionals target audience"}, "generalPublic": "General Public", "@generalPublic": {"description": "General public target audience"}, "modern": "Modern", "@modern": {"description": "Modern design style"}, "minimalist": "Minimalist", "@minimalist": {"description": "Minimalist design style"}, "retro": "Retro", "@retro": {"description": "Retro design style"}, "elegant": "Elegant", "@elegant": {"description": "Elegant design style"}, "creative": "Creative", "@creative": {"description": "Creative design style"}, "professional": "Professional", "@professional": {"description": "Professional design style"}, "playful": "Playful", "@playful": {"description": "Playful design style"}, "print": "Print", "@print": {"description": "Print usage type"}, "socialMedia": "Social Media", "@socialMedia": {"description": "Social media usage type"}, "onlineAdvertising": "Online Advertising", "@onlineAdvertising": {"description": "Online advertising usage type"}, "email": "Email", "@email": {"description": "Email usage type"}, "web": "Web", "@web": {"description": "Web usage type"}, "high": "High", "@high": {"description": "High resolution"}, "medium": "Medium", "@medium": {"description": "Medium resolution"}, "low": "Low", "@low": {"description": "Low resolution"}, "freeForPersonalUse": "Free for personal use", "@freeForPersonalUse": {"description": "Free for personal use license"}, "commercialUseAllowed": "Commercial use allowed", "@commercialUseAllowed": {"description": "Commercial use allowed license"}, "premiumLicenseRequired": "Premium license required", "@premiumLicenseRequired": {"description": "Premium license required"}, "templateName": "Template Name", "@templateName": {"description": "Template name field label"}, "templateDescription": "Template Description", "@templateDescription": {"description": "Template description field label"}, "creator": "Creator", "@creator": {"description": "Creator field label"}, "dimensions": "Dimensions", "@dimensions": {"description": "Dimensions field label"}, "tags": "Tags", "@tags": {"description": "Tags field label"}, "colors": "Colors", "@colors": {"description": "Colors field label"}, "fontStyles": "<PERSON><PERSON>", "@fontStyles": {"description": "Font styles field label"}, "fileFormats": "File Formats", "@fileFormats": {"description": "File formats field label"}, "category": "Category", "@category": {"description": "Category field label"}, "subCategory": "Sub Category", "@subCategory": {"description": "Sub category field label"}, "layoutType": "Layout Type", "@layoutType": {"description": "Layout type field label"}, "targetAudience": "Target Audience", "@targetAudience": {"description": "Target audience field label"}, "designStyle": "Design Style", "@designStyle": {"description": "Design style field label"}, "usageType": "Usage Type", "@usageType": {"description": "Usage type field label"}, "resolution": "Resolution", "@resolution": {"description": "Resolution field label"}, "licenseType": "License Type", "@licenseType": {"description": "License type field label"}, "isPremium": "Is Premium", "@isPremium": {"description": "Is premium checkbox label"}, "isActive": "Is Active", "@isActive": {"description": "Is active checkbox label"}, "selectImage": "Select Image", "@selectImage": {"description": "Select image button text"}, "uploadTemplate": "Upload Template", "@uploadTemplate": {"description": "Upload template button text"}, "updateTemplate": "Update Template", "@updateTemplate": {"description": "Update template button text"}, "uploading": "Uploading...", "@uploading": {"description": "Uploading progress message"}, "templateUploadedSuccessfully": "Template uploaded successfully", "@templateUploadedSuccessfully": {"description": "Template upload success message"}, "templateUpdatedSuccessfully": "Template updated successfully", "@templateUpdatedSuccessfully": {"description": "Template update success message"}, "failedToUploadTemplate": "Failed to upload template", "@failedToUploadTemplate": {"description": "Template upload error message"}, "failedToUpdateTemplate": "Failed to update template", "@failedToUpdateTemplate": {"description": "Template update error message"}, "pleaseSelectImage": "Please select an image", "@pleaseSelectImage": {"description": "Please select image validation message"}, "pleaseEnterTemplateName": "Please enter template name", "@pleaseEnterTemplateName": {"description": "Please enter template name validation message"}, "pleaseSelectCategory": "Please select a category", "@pleaseSelectCategory": {"description": "Please select category validation message"}, "loadingBanners": "Loading banners...", "@loadingBanners": {"description": "Loading banners message"}, "loadingMoreBanners": "Loading more banners...", "@loadingMoreBanners": {"description": "Loading more banners message"}, "errorLoadingBanners": "Error loading banners", "@errorLoadingBanners": {"description": "Error loading banners message"}, "noBannersFound": "No banners found", "@noBannersFound": {"description": "No banners found message"}, "selectBanner": "Select Banner", "@selectBanner": {"description": "Select banner instruction"}, "customizeTemplate": "Customize Template", "@customizeTemplate": {"description": "Customize template title"}, "imageCustomization": "Image Customization", "@imageCustomization": {"description": "Image customization section title"}, "bannerCustomization": "Banner Customization", "@bannerCustomization": {"description": "Banner customization section title"}, "textCustomization": "Text Customization", "@textCustomization": {"description": "Text customization section title"}, "previewTemplate": "Preview Template", "@previewTemplate": {"description": "Preview template title"}, "saveTemplate": "Save Template", "@saveTemplate": {"description": "Save template button text"}, "downloadTemplate": "Download Template", "@downloadTemplate": {"description": "Download template button text"}, "shareTemplate": "Share Template", "@shareTemplate": {"description": "Share template button text"}, "templateSavedSuccessfully": "Template saved successfully", "@templateSavedSuccessfully": {"description": "Template save success message"}, "templateDownloadedSuccessfully": "Template downloaded successfully", "@templateDownloadedSuccessfully": {"description": "Template download success message"}, "failedToSaveTemplate": "Failed to save template", "@failedToSaveTemplate": {"description": "Template save error message"}, "failedToDownloadTemplate": "Failed to download template", "@failedToDownloadTemplate": {"description": "Template download error message"}, "processingImage": "Processing image...", "@processingImage": {"description": "Processing image message"}, "generatingPreview": "Generating preview...", "@generatingPreview": {"description": "Generating preview message"}, "preparingDownload": "Preparing download...", "@preparingDownload": {"description": "Preparing download message"}, "manageBusiness": "Manage Business", "@manageBusiness": {"description": "Manage business button text"}, "configureBusinessDetails": "Configure your business details, address, and other parameters", "@configureBusinessDetails": {"description": "Business configuration description"}, "managePolitical": "Manage Political", "@managePolitical": {"description": "Manage political button text"}, "failedToLoadBusinessProfile": "Failed to load business profile", "@failedToLoadBusinessProfile": {"description": "Business profile load error message"}, "failedToLoadPoliticalProfile": "Failed to load political profile", "@failedToLoadPoliticalProfile": {"description": "Political profile load error message"}, "noBusinessParametersAvailable": "No business parameters available", "@noBusinessParametersAvailable": {"description": "No business parameters message"}, "businessParametersConfiguredByAdmin": "Business parameters will be configured by the administrator", "@businessParametersConfiguredByAdmin": {"description": "Business parameters admin message"}, "noPoliticalParametersAvailable": "No political parameters available", "@noPoliticalParametersAvailable": {"description": "No political parameters message"}, "politicalParametersConfiguredByAdmin": "Political parameters will be configured by the administrator", "@politicalParametersConfiguredByAdmin": {"description": "Political parameters admin message"}, "saveBusinessParameters": "Save Business Parameters", "@saveBusinessParameters": {"description": "Save business parameters button text"}, "savePoliticalParameters": "Save Political Parameters", "@savePoliticalParameters": {"description": "Save political parameters button text"}, "businessParametersSavedSuccessfully": "Business parameters saved successfully", "@businessParametersSavedSuccessfully": {"description": "Business parameters save success message"}, "politicalParametersSavedSuccessfully": "Political parameters saved successfully", "@politicalParametersSavedSuccessfully": {"description": "Political parameters save success message"}, "failedToSaveBusinessParameters": "Failed to save business parameters", "@failedToSaveBusinessParameters": {"description": "Business parameters save error message"}, "failedToSavePoliticalParameters": "Failed to save political parameters", "@failedToSavePoliticalParameters": {"description": "Political parameters save error message"}, "businessType": "Business Type", "@businessType": {"description": "Business type field label"}, "businessAddress": "Business Address", "@businessAddress": {"description": "Business address field label"}, "businessLocation": "Business Location", "@businessLocation": {"description": "Business location field label"}, "businessMobile": "Business Mobile", "@businessMobile": {"description": "Business mobile field label"}, "businessWebsite": "Business Website", "@businessWebsite": {"description": "Business website field label"}, "businessEmail": "Business Email", "@businessEmail": {"description": "Business email field label"}, "selectBusinessLogo": "Select Business Logo", "@selectBusinessLogo": {"description": "Select business logo button text"}, "politicalPhoto": "Political photo", "@politicalPhoto": {"description": "Political photo description"}, "selectPoliticalPhoto": "Select Political Photo", "@selectPoliticalPhoto": {"description": "Select political photo button text"}, "partyName": "Party Name", "@partyName": {"description": "Party name field label"}, "politicalPosition": "Political Position", "@politicalPosition": {"description": "Political position field label"}, "constituency": "Constituency", "@constituency": {"description": "Constituency field label"}, "politicalExperience": "Political Experience", "@politicalExperience": {"description": "Political experience field label"}, "campaignSlogan": "<PERSON> Slogan", "@campaignSlogan": {"description": "Campaign slogan field label"}, "politicalAchievements": "Political Achievements", "@politicalAchievements": {"description": "Political achievements field label"}, "isRequired": "is required", "@isRequired": {"description": "Required field validation suffix"}, "pleaseEnterValidNumber": "Please enter a valid number", "@pleaseEnterValidNumber": {"description": "Number validation message"}, "allTypes": "All Types", "@allTypes": {"description": "All types filter option"}, "removePremium": "Remove Premium", "@removePremium": {"description": "Remove premium status action"}, "makePremium": "Make Premium", "@makePremium": {"description": "Make premium status action"}, "deleteUser": "Delete User", "@deleteUser": {"description": "Delete user action"}, "userManagement": "User Management", "@userManagement": {"description": "User management page title"}, "searchUsers": "Search users...", "@searchUsers": {"description": "Search users placeholder"}, "noUsersFound": "No users found", "@noUsersFound": {"description": "No users found message"}, "loadingUsers": "Loading users...", "@loadingUsers": {"description": "Loading users message"}, "failedToLoadUsers": "Failed to load users", "@failedToLoadUsers": {"description": "Failed to load users error message"}, "userDetails": "User Details", "@userDetails": {"description": "User details page title"}, "accountCreated": "Account Created", "@accountCreated": {"description": "Account created label"}, "lastUpdated": "Last Updated", "@lastUpdated": {"description": "Last updated label"}, "profileComplete": "Profile Complete", "@profileComplete": {"description": "Profile complete status"}, "profileIncomplete": "Profile Incomplete", "@profileIncomplete": {"description": "Profile incomplete status"}, "premiumStatus": "Premium Status", "@premiumStatus": {"description": "Premium status label"}, "active": "Active", "@active": {"description": "Active status"}, "inactive": "Inactive", "@inactive": {"description": "Inactive status"}, "subscriptionPlan": "Subscription Plan", "@subscriptionPlan": {"description": "Subscription plan label"}, "subscriptionEndDate": "Subscription End Date", "@subscriptionEndDate": {"description": "Subscription end date label"}, "languagePreference": "Language Preference", "@languagePreference": {"description": "Language preference label"}, "totalPosters": "Total Posters", "@totalPosters": {"description": "Total posters count label"}, "adminStatus": "Admin Status", "@adminStatus": {"description": "Admin status label"}, "isAdmin": "Admin", "@isAdmin": {"description": "Is admin status"}, "isNotAdmin": "Not Admin", "@isNotAdmin": {"description": "Is not admin status"}, "allUsers": "All Users", "@allUsers": {"description": "All users filter option"}, "free": "Free", "@free": {"description": "Free user status"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "tryAdjustingFilters": "Try adjusting your search or filters", "@tryAdjustingFilters": {"description": "Message when no users found with current filters"}, "notProvided": "Not provided", "@notProvided": {"description": "Text when information is not provided"}, "yes": "Yes", "@yes": {"description": "Yes response"}, "no": "No", "@no": {"description": "No response"}, "created": "Created", "@created": {"description": "Created date label"}, "name": "Name", "@name": {"description": "Name field label"}, "userPremiumStatusRemoved": "User premium status removed", "@userPremiumStatusRemoved": {"description": "Message when user premium status is removed"}, "userUpgradedToPremium": "User upgraded to premium", "@userUpgradedToPremium": {"description": "Message when user is upgraded to premium"}, "errorUpdatingUser": "Error updating user", "@errorUpdatingUser": {"description": "Error message when updating user fails"}, "areYouSureDeleteUser": "Are you sure you want to delete {userName}? This action cannot be undone.", "@areYouSureDeleteUser": {"description": "Confirmation message for deleting user", "placeholders": {"userName": {"type": "String", "description": "Name of the user to delete"}}}, "userDeletedSuccessfully": "User deleted successfully", "@userDeletedSuccessfully": {"description": "Success message when user is deleted"}, "errorDeletingUser": "Error deleting user", "@errorDeletingUser": {"description": "Error message when deleting user fails"}, "add": "Add", "@add": {"description": "Add button text"}, "extend": "Extend", "@extend": {"description": "Extend subscription button text"}, "subscriptionExtended": "Subscription extended by {duration}!", "@subscriptionExtended": {"description": "Success message when subscription is extended", "placeholders": {"duration": {"type": "String", "description": "Duration of extension"}}}, "failedToExtendSubscription": "Failed to extend subscription: {error}", "@failedToExtendSubscription": {"description": "Error message when subscription extension fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "failedToPurchaseSubscription": "Failed to purchase subscription: {error}", "@failedToPurchaseSubscription": {"description": "Error message when subscription purchase fails", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "failedToRestorePurchases": "Failed to restore purchases", "@failedToRestorePurchases": {"description": "Error message when restoring purchases fails"}, "trialPlanDescription": "Try Premium features for 1 week at minimal cost", "@trialPlanDescription": {"description": "Trial subscription plan description"}, "threeMonthPlanDescription": "Perfect for short-term projects", "@threeMonthPlanDescription": {"description": "3-month subscription plan description"}, "sixMonthPlanDescription": "Great value for medium-term use", "@sixMonthPlanDescription": {"description": "6-month subscription plan description"}, "annualPlanDescription": "Best value for regular users", "@annualPlanDescription": {"description": "Annual subscription plan description"}, "allPremiumFeatures": "All Premium features", "@allPremiumFeatures": {"description": "All premium features text"}, "autoRenewsAt": "Auto-renews at ₹199 for 3 months", "@autoRenewsAt": {"description": "Auto-renewal description"}, "cancelAnytime": "Cancel anytime", "@cancelAnytime": {"description": "Cancel anytime feature"}, "quarterlyBilling": "Quarterly billing", "@quarterlyBilling": {"description": "Quarterly billing feature"}, "save50Percent": "Save 50% compared to 3-month plan", "@save50Percent": {"description": "Save 50% feature"}, "prioritySupport": "Priority support", "@prioritySupport": {"description": "Priority support feature"}, "save58Percent": "Save 58% compared to 3-month plan", "@save58Percent": {"description": "Save 58% feature"}, "earlyAccessToNewFeatures": "Early access to new features", "@earlyAccessToNewFeatures": {"description": "Early access feature"}}