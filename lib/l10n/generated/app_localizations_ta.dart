// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Tamil (`ta`).
class AppLocalizationsTa extends AppLocalizations {
  AppLocalizationsTa([String locale = 'ta']) : super(locale);

  @override
  String get appTitle => 'குயிக் போஸ்டர்ஸ்';

  @override
  String get welcome => 'குயிக் போஸ்டர்ஸ்க்கு வரவேற்கிறோம்';

  @override
  String get login => 'உள்நுழைவு';

  @override
  String get logout => 'வெளியேறு';

  @override
  String get phoneNumber => 'தொலைபேசி எண்';

  @override
  String get enterPhoneNumber => 'உங்கள் தொலைபேசி எண்ணை உள்ளிடவும்';

  @override
  String get sendOTP => 'OTP அனுப்பவும்';

  @override
  String get verifyOTP => 'OTP சரிபார்க்கவும்';

  @override
  String get enterOTP => '6-இலக்க OTP உள்ளிடவும்';

  @override
  String get resendOTP => 'OTP மீண்டும் அனுப்பவும்';

  @override
  String get home => 'முகப்பு';

  @override
  String get templates => 'வார்ப்புருக்கள்';

  @override
  String get categories => 'வகைகள்';

  @override
  String get all => 'அனைத்தும்';

  @override
  String get premium => 'பிரீமியம்';

  @override
  String get profile => 'சுயவிவரம்';

  @override
  String get settings => 'அமைப்புகள்';

  @override
  String get language => 'மொழி';

  @override
  String get selectLanguage => 'மொழியைத் தேர்ந்தெடுக்கவும்';

  @override
  String get businessUser => 'வணிகம்';

  @override
  String get politicalUser => 'அரசியல்வாதி';

  @override
  String get individualUser => 'தனிப்பட்ட பயனர்';

  @override
  String get createPoster => 'போஸ்டர் உருவாக்கவும்';

  @override
  String get editTemplate => 'வார்ப்புருவைத் திருத்தவும்';

  @override
  String get saveImage => 'படத்தைச் சேமிக்கவும்';

  @override
  String get shareImage => 'படத்தைப் பகிரவும்';

  @override
  String get premiumFeature => 'பிரீமியம் அம்சம்';

  @override
  String get upgradeNow => 'இப்போது மேம்படுத்தவும்';

  @override
  String get subscriptionPlans => 'சந்தா திட்டங்கள்';

  @override
  String get trialPlan => 'சோதனை திட்டம்';

  @override
  String get threeMonthPlan => '3-மாத திட்டம்';

  @override
  String get sixMonthPlan => '6-மாத திட்டம்';

  @override
  String get annualPlan => 'வருடாந்திர திட்டம்';

  @override
  String rupees(String amount) {
    return '₹$amount';
  }

  @override
  String get myProfile => 'எனது சுயவிவரம்';

  @override
  String get notifications => 'அறிவிப்புகள்';

  @override
  String get about => 'பற்றி';

  @override
  String get privacyPolicy => 'தனியுரிமை கொள்கை';

  @override
  String get termsOfService => 'சேவை விதிமுறைகள்';

  @override
  String get refundPolicy => 'திரும்பப் பெறும் கொள்கை';

  @override
  String get aiImageGenerator => 'AI படம் உருவாக்கி';

  @override
  String get adminDashboard => 'நிர்வாக டாஷ்போர்டு';

  @override
  String get cancel => 'ரத்து செய்';

  @override
  String get save => 'சேமி';

  @override
  String get delete => 'நீக்கு';

  @override
  String get edit => 'திருத்து';

  @override
  String get loading => 'ஏற்றுகிறது...';

  @override
  String get error => 'பிழை';

  @override
  String get success => 'வெற்றி';

  @override
  String get tryAgain => 'மீண்டும் முயற்சிக்கவும்';

  @override
  String get noInternetConnection => 'இணைய இணைப்பு இல்லை';

  @override
  String get somethingWentWrong => 'ஏதோ தவறு நடந்தது';

  @override
  String get phoneNumberValidation => 'தயவுசெய்து சரியான 10-இலக்க தொலைபேசி எண்ணை உள்ளிடவும்';

  @override
  String get otpValidation => 'தயவுசெய்து சரியான 6-இலக்க OTP உள்ளிடவும்';

  @override
  String get otpSent => 'OTP வெற்றிகரமாக அனுப்பப்பட்டது';

  @override
  String get otpExpired => 'OTP காலாவதியாகிவிட்டது. தயவுசெய்து புதிய OTP கேட்கவும்';

  @override
  String get invalidOtp => 'தவறான OTP. தயவுசெய்து மீண்டும் முயற்சிக்கவும்';

  @override
  String get loginSuccessful => 'உள்நுழைவு வெற்றிகரமானது';

  @override
  String get welcomeBack => 'மீண்டும் வரவேற்கிறோம்!';

  @override
  String get getStarted => 'தொடங்குங்கள்';

  @override
  String get continueText => 'தொடரவும்';

  @override
  String get skip => 'தவிர்க்கவும்';

  @override
  String get next => 'அடுத்து';

  @override
  String get previous => 'முந்தைய';

  @override
  String get done => 'முடிந்தது';

  @override
  String get close => 'மூடு';

  @override
  String get back => 'பின்னால்';

  @override
  String get selectUserType => 'பயனர் வகையைத் தேர்ந்தெடுக்கவும்';

  @override
  String get chooseAccountType => 'உங்கள் கணக்கு வகையைத் தேர்வு செய்யவும்';

  @override
  String get businessDescription => 'வணிக விளக்கம்';

  @override
  String get politicalDescription => 'அரசியல் பிரச்சாரங்கள் மற்றும் தலைவர்களுக்கு ஏற்றது';

  @override
  String get individualDescription => 'தனிப்பட்ட பயன்பாடு மற்றும் சமூக ஊடகங்களுக்கு சிறந்தது';

  @override
  String get completeProfile => 'உங்கள் சுயவிவரத்தை முடிக்கவும்';

  @override
  String get profileCompletionDescription => 'உங்கள் அனுபவத்தை தனிப்பயனாக்க எங்களுக்கு உதவுங்கள்';

  @override
  String get businessName => 'வணிக பெயர்';

  @override
  String get enterBusinessName => 'உங்கள் வணிகப் பெயரை உள்ளிடவும்';

  @override
  String get businessCategory => 'வணிக வகை';

  @override
  String get selectBusinessCategory => 'உங்கள் வணிக வகையைத் தேர்ந்தெடுக்கவும்';

  @override
  String get politicalParty => 'அரசியல் கட்சி';

  @override
  String get selectPoliticalParty => 'உங்கள் அரசியல் கட்சியைத் தேர்ந்தெடுக்கவும்';

  @override
  String get designation => 'பதவி';

  @override
  String get enterDesignation => 'உங்கள் பதவியை உள்ளிடவும்';

  @override
  String get fullName => 'முழுப் பெயர்';

  @override
  String get enterFullName => 'உங்கள் முழுப் பெயரை உள்ளிடவும்';

  @override
  String get profileUpdated => 'சுயவிவரம் வெற்றிகரமாக புதுப்பிக்கப்பட்டது';

  @override
  String get exploreTemplates => 'வார்ப்புருக்களை ஆராயுங்கள்';

  @override
  String get featuredTemplates => 'சிறப்பு வார்ப்புருக்கள்';

  @override
  String get recentTemplates => 'சமீபத்திய வார்ப்புருக்கள்';

  @override
  String get popularTemplates => 'பிரபலமான வார்ப்புருக்கள்';

  @override
  String get loadingTemplates => 'வார்ப்புருக்கள் ஏற்றப்படுகின்றன...';

  @override
  String get noTemplatesFound => 'வார்ப்புருக்கள் எதுவும் கிடைக்கவில்லை';

  @override
  String get searchTemplates => 'வார்ப்புருக்களைத் தேடுங்கள்...';

  @override
  String get filterByCategory => 'வகை அடிப்படையில் வடிகட்டவும்';

  @override
  String get viewAll => 'அனைத்தையும் பார்க்கவும்';

  @override
  String get loadMore => 'மேலும் ஏற்றவும்';

  @override
  String get refreshing => 'புதுப்பிக்கப்படுகிறது...';

  @override
  String get pullToRefresh => 'புதுப்பிக்க இழுக்கவும்';

  @override
  String get refresh => 'புதுப்பிக்கவும்';

  @override
  String get account => 'கணக்கு';

  @override
  String get appSettings => 'ஆப் அமைப்புகள்';

  @override
  String get tools => 'கருவிகள்';

  @override
  String get premiumSubscription => 'பிரீமியம் சந்தா';

  @override
  String get youArePremiumUser => 'நீங்கள் ஒரு பிரீமியம் பயனர்';

  @override
  String get upgradeToPremium => 'பிரீமியத்திற்கு மேம்படுத்தவும்';

  @override
  String get createImagesWithAI => 'AI உடன் படங்களை உருவாக்கவும்';

  @override
  String get aboutQuickPosters => 'குயிக் போஸ்டர்ஸ் பற்றி';

  @override
  String get version => 'பதிப்பு 1.0.0';

  @override
  String get readPrivacyPolicy => 'எங்கள் தனியுரிமை கொள்கையைப் படிக்கவும்';

  @override
  String get viewEditProfile => 'உங்கள் சுயவிவரத்தைப் பார்க்கவும் மற்றும் திருத்தவும்';

  @override
  String get changeAppLanguage => 'ஆப் மொழியை மாற்றவும்';

  @override
  String get failedToLoadUserData => 'பயனர் தரவை ஏற்றுவதில் தோல்வி';

  @override
  String get profileUpdatedSuccessfully => 'சுயவிவரம் வெற்றிகரமாக புதுப்பிக்கப்பட்டது';

  @override
  String get failedToSaveProfile => 'சுயவிவரத்தை சேமிப்பதில் தோல்வி';

  @override
  String get manageBusinessProfile => 'வணிக சுயவிவரத்தை நிர்வகிக்கவும்';

  @override
  String get managePoliticalProfile => 'அரசியல் சுயவிவரத்தை நிர்வகிக்கவும்';

  @override
  String get configurePoliticalDetails => 'உங்கள் அரசியல் விவரங்கள், கட்சி மற்றும் பிற அளவுருக்களை கட்டமைக்கவும்';

  @override
  String get businessParameters => 'வணிக அளவுருக்கள்';

  @override
  String get politicalParameters => 'அரசியல் அளவுருக்கள்';

  @override
  String get contentRefreshedSuccessfully => 'உள்ளடக்கம் வெற்றிகரமாக புதுப்பிக்கப்பட்டது';

  @override
  String get failedToRefreshContent => 'உள்ளடக்கத்தை புதுப்பிக்கத் தவறியது';

  @override
  String get mySubscription => 'எனது சந்தா';

  @override
  String get startByAddingBaseImage => 'அடிப்படை படத்தைச் சேர்ப்பதன் மூலம் தொடங்கவும்';

  @override
  String get backgroundOfPoster => 'இது உங்கள் போஸ்டரின் பின்னணியாக இருக்கும்';

  @override
  String get selectBaseImage => 'அடிப்படை படத்தைத் தேர்ந்தெடுக்கவும்';

  @override
  String get addBaseImage => 'அடிப்படை படத்தைச் சேர்க்கவும்';

  @override
  String get aboutPageComingSoon => 'பற்றிய பக்கம் விரைவில் வருகிறது';

  @override
  String get languageSettingsComingSoon => 'மொழி அமைப்புகள் விரைவில் வருகின்றன';

  @override
  String get notificationSettingsComingSoon => 'அறிவிப்பு அமைப்புகள் விரைவில் வருகின்றன';

  @override
  String get manageNotificationSettings => 'அறிவிப்பு அமைப்புகளை நிர்வகிக்கவும்';

  @override
  String get imageShape => 'படத்தின் வடிவம்';

  @override
  String get businessLogoShape => 'வணிக லோகோ வடிவம்';

  @override
  String get profilePhoto => 'சுயவிவர புகைப்படம்';

  @override
  String get businessLogo => 'வணிக லோகோ';

  @override
  String get pleaseSelectUserType => 'தயவுசெய்து பயனர் வகையைத் தேர்ந்தெடுக்கவும்';

  @override
  String get failedToLoadUserTypes => 'பயனர் வகைகளை ஏற்றுவதில் தோல்வி';

  @override
  String get failedToSaveUserType => 'பயனர் வகையைச் சேமிப்பதில் தோல்வி';

  @override
  String get profileCompletedSuccessfully => 'சுயவிவரம் வெற்றிகரமாக நிறைவடைந்தது';

  @override
  String get viewEditProfileInfo => 'உங்கள் சுயவிவர தகவலைப் பார்க்கவும் மற்றும் திருத்தவும்';

  @override
  String get chooseImageSource => 'படத்தின் மூலத்தைத் தேர்ந்தெடுக்கவும்';

  @override
  String get customImage => 'தனிப்பயன் படம்';

  @override
  String get autoSelect => 'தானியங்கி தேர்வு';

  @override
  String get imageSource => 'படத்தின் மூலம்';

  @override
  String get shapeOptions => 'வடிவ விருப்பங்கள்';

  @override
  String get none => 'எதுவுமில்லை';

  @override
  String get circle => 'வட்டம்';

  @override
  String get rectangle => 'செவ்வகம்';

  @override
  String get roundedRectangle => 'வட்டமான செவ்வகம்';

  @override
  String get diamond => 'வைரம்';

  @override
  String get hexagon => 'அறுகோணம்';

  @override
  String get quickPosters => 'குயிக் போஸ்டர்ஸ்';

  @override
  String get accountActions => 'கணக்கு செயல்கள்';

  @override
  String get signOutFromAccount => 'உங்கள் கணக்கிலிருந்து வெளியேறவும்';

  @override
  String get changingLanguage => 'மொழி மாற்றப்படுகிறது...';

  @override
  String get phoneNumberLabel => 'தொலைபேசி எண்';

  @override
  String get enterYourPhoneNumber => 'உங்கள் தொலைபேசி எண்ணை உள்ளிடவும்';

  @override
  String get userType => 'பயனர் வகை';

  @override
  String get saveToGallery => 'கேலரியில் சேமிக்கவும்';

  @override
  String get share => 'பகிர்';

  @override
  String get whatsApp => 'வாட்ஸ்அப்';

  @override
  String get invalidPhoneNumber => 'தொலைபேசி எண் வடிவம் தவறானது. தயவுசெய்து சரியான தொலைபேசி எண்ணை உள்ளிடவும்.';

  @override
  String get quotaExceeded => 'SMS கோட்டா முடிந்துவிட்டது. தயவுசெய்து பின்னர் மீண்டும் முயற்சிக்கவும்.';

  @override
  String get userDisabled => 'இந்த பயனர் முடக்கப்பட்டுள்ளார். தயவுசெய்து ஆதரவைத் தொடர்பு கொள்ளவும்.';

  @override
  String get operationNotAllowed => 'தொலைபேசி அங்கீகாரம் இயக்கப்படவில்லை. தயவுசெய்து ஆதரவைத் தொடர்பு கொள்ளவும்.';

  @override
  String get captchaCheckFailed => 'reCAPTCHA சரிபார்ப்பு தோல்வியடைந்தது. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.';

  @override
  String get missingClientIdentifier => 'ஆப் சரிபார்ப்பு செயல்முறை தோல்வியடைந்தது. தயவுசெய்து மீண்டும் முயற்சிக்கவும் அல்லது ஆதரவைத் தொடர்பு கொள்ளவும்.';

  @override
  String get tooManyRequests => 'இந்த சாதனத்திலிருந்து பல கோரிக்கைகள். அசாதாரண செயல்பாட்டின் காரணமாக இந்த சாதனத்திலிருந்து அனைத்து கோரிக்கைகளையும் தற்காலிகமாக தடுத்துள்ளோம். தயவுசெய்து சிறிது நேரம் கழித்து மீண்டும் முயற்சிக்கவும் (பொதுவாக சில மணிநேரங்கள்).';

  @override
  String get verificationFailed => 'சரிபார்ப்பு தோல்வியடைந்தது. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.';

  @override
  String get failedToGetUserData => 'பயனர் தரவைப் பெறுவதில் தோல்வி';

  @override
  String get userNotFound => 'பயனர் கிடைக்கவில்லை';

  @override
  String get politician => 'அரசியல்வாதி';

  @override
  String get businessman => 'வணிகர்';

  @override
  String get regularUser => 'வழக்கமான';

  @override
  String get forPoliticalCampaigns => 'அரசியல் பிரச்சாரங்கள் மற்றும் விளம்பரங்களுக்காக';

  @override
  String get forBusinessPromotions => 'வணிக விளம்பரங்கள் மற்றும் விளம்பரங்களுக்காக';

  @override
  String get forPersonalUse => 'தனிப்பட்ட பயன்பாடு மற்றும் பொதுவான நோக்கங்களுக்காக';

  @override
  String get developmentLogin => 'டெவலப்மென்ட் லாகின்';

  @override
  String get developmentMode => 'மேம்பாட்டு முறை';

  @override
  String get loggedInAsDeveloper => 'நீங்கள் ஒரு டெவலப்பராக உள்நுழைந்துள்ளீர்கள்.\nFirebase தொலைபேசி அங்கீகாரம் தடுக்கப்பட்டிருக்கும் போது இது ஒரு தற்காலிக முறை.';

  @override
  String get noActiveSubscription => 'செயலில் உள்ள சந்தா இல்லை';

  @override
  String expiresInDays(int days) {
    return '$days நாட்களில் காலாவதியாகும்';
  }

  @override
  String activeUntil(String date) {
    return '$date வரை செயலில்';
  }

  @override
  String get downloadImage => 'படத்தைப் பதிவிறக்கவும்';

  @override
  String get shareOnWhatsApp => 'வாட்ஸ்அப்பில் பகிரவும்';

  @override
  String get customImageSelected => 'தனிப்பயன் படம் தேர்ந்தெடுக்கப்பட்டது!';

  @override
  String errorSelectingImage(String error) {
    return 'படம் தேர்ந்தெடுப்பதில் பிழை: $error';
  }

  @override
  String get imageAddedSuccessfully => 'படம் வெற்றிகரமாக சேர்க்கப்பட்டது!';

  @override
  String errorAddingImage(String error) {
    return 'படத்தைச் சேர்ப்பதில் பிழை: $error';
  }

  @override
  String noImageAvailable(String source) {
    return '$source படம் எதுவும் கிடைக்கவில்லை';
  }

  @override
  String get emailAddress => 'மின்னஞ்சல் முகவரி';

  @override
  String get enterEmailAddress => 'உங்கள் மின்னஞ்சல் முகவரியை உள்ளிடவும்';

  @override
  String get optional => 'விருப்பமானது';

  @override
  String get selectProfileImage => 'சுயவிவர படத்தைத் தேர்ந்தெடுக்கவும்';

  @override
  String get camera => 'கேமரா';

  @override
  String get gallery => 'கேலரி';

  @override
  String get removeImage => 'படத்தை அகற்று';

  @override
  String get saveProfile => 'சுயவிவரத்தைச் சேமிக்கவும்';

  @override
  String get updateProfile => 'சுயவிவரத்தைப் புதுப்பிக்கவும்';

  @override
  String get profileImageUpdated => 'சுயவிவர படம் வெற்றிகரமாக புதுப்பிக்கப்பட்டது';

  @override
  String get failedToUpdateProfileImage => 'சுயவிவர படத்தைப் புதுப்பிக்கத் தவறியது';

  @override
  String get pleaseEnterValidEmail => 'தயவுசெய்து சரியான மின்னஞ்சல் முகவரியை உள்ளிடவும்';

  @override
  String get pleaseEnterName => 'தயவுசெய்து உங்கள் பெயரை உள்ளிடவும்';

  @override
  String get nameTooShort => 'பெயர் குறைந்தது 2 எழுத்துகள் இருக்க வேண்டும்';

  @override
  String get invalidEmailFormat => 'தயவுசெய்து சரியான மின்னஞ்சல் வடிவத்தை உள்ளிடவும்';

  @override
  String get business => 'வணிகம்';

  @override
  String get event => 'நிகழ்வு';

  @override
  String get festival => 'திருவிழா';

  @override
  String get party => 'விருந்து';

  @override
  String get education => 'கல்வி';

  @override
  String get health => 'ஆரோக்கியம்';

  @override
  String get technology => 'தொழில்நுட்பம்';

  @override
  String get food => 'உணவு';

  @override
  String get travel => 'பயணம்';

  @override
  String get sports => 'விளையாட்டு';

  @override
  String get fashion => 'ஃபேஷன்';

  @override
  String get realEstate => 'ரியல் எஸ்டேட்';

  @override
  String get flyers => 'ஃப்ளையர்கள்';

  @override
  String get brochures => 'ப்ரோஷர்கள்';

  @override
  String get businessCards => 'வணிக அட்டைகள்';

  @override
  String get presentations => 'விளக்கக்காட்சிகள்';

  @override
  String get logos => 'லோகோக்கள்';

  @override
  String get invitations => 'அழைப்பிதழ்கள்';

  @override
  String get tickets => 'டிக்கெட்டுகள்';

  @override
  String get programs => 'நிகழ்ச்சிகள்';

  @override
  String get banners => 'பேனர்கள்';

  @override
  String get posters => 'போஸ்டர்கள்';

  @override
  String get celebration => 'கொண்டாட்டம்';

  @override
  String get religious => 'மத';

  @override
  String get cultural => 'கலாச்சார';

  @override
  String get seasonal => 'பருவகால';

  @override
  String get birthday => 'பிறந்தநாள்';

  @override
  String get wedding => 'திருமணம்';

  @override
  String get anniversary => 'ஆண்டுவிழா';

  @override
  String get graduation => 'பட்டமளிப்பு';

  @override
  String get certificates => 'சான்றிதழ்கள்';

  @override
  String get worksheets => 'பணித்தாள்கள்';

  @override
  String get awareness => 'விழிப்புணர்வு';

  @override
  String get medical => 'மருத்துவ';

  @override
  String get fitness => 'உடற்பயிற்சி';

  @override
  String get nutrition => 'ஊட்டச்சத்து';

  @override
  String get appPromotion => 'ஆப் விளம்பரம்';

  @override
  String get software => 'மென்பொருள்';

  @override
  String get digitalServices => 'டிஜிட்டல் சேவைகள்';

  @override
  String get menu => 'மெனு';

  @override
  String get recipe => 'செய்முறை';

  @override
  String get restaurant => 'உணவகம்';

  @override
  String get catering => 'கேட்டரிங்';

  @override
  String get tourism => 'சுற்றுலா';

  @override
  String get hotels => 'ஹோட்டல்கள்';

  @override
  String get transportation => 'போக்குவரத்து';

  @override
  String get adventure => 'சாகசம்';

  @override
  String get team => 'அணி';

  @override
  String get tournament => 'போட்டி';

  @override
  String get equipment => 'உபகரணங்கள்';

  @override
  String get clothing => 'ஆடை';

  @override
  String get accessories => 'துணைப்பொருட்கள்';

  @override
  String get beauty => 'அழகு';

  @override
  String get style => 'பாணி';

  @override
  String get property => 'சொத்து';

  @override
  String get rental => 'வாடகை';

  @override
  String get commercial => 'வணிக';

  @override
  String get residential => 'குடியிருப்பு';

  @override
  String get noShape => 'வடிவம் இல்லை';

  @override
  String get rounded => 'வட்டமான';

  @override
  String get loadMoreTemplates => 'மேலும் டெம்ப்ளேட்களை ஏற்றவும்';

  @override
  String get horizontal => 'கிடைமட்ட';

  @override
  String get vertical => 'செங்குத்து';

  @override
  String get square => 'சதுர';

  @override
  String get smallBusiness => 'சிறு வணிகம்';

  @override
  String get eventOrganizers => 'நிகழ்வு ஏற்பாட்டாளர்கள்';

  @override
  String get educators => 'கல்வியாளர்கள்';

  @override
  String get marketers => 'சந்தைப்படுத்துபவர்கள்';

  @override
  String get students => 'மாணவர்கள்';

  @override
  String get professionals => 'தொழில் வல்லுநர்கள்';

  @override
  String get generalPublic => 'பொது மக்கள்';

  @override
  String get modern => 'நவீன';

  @override
  String get minimalist => 'குறைந்தபட்ச';

  @override
  String get retro => 'பழைய பாணி';

  @override
  String get elegant => 'நேர்த்தியான';

  @override
  String get creative => 'படைப்பாற்றல்';

  @override
  String get professional => 'தொழில்முறை';

  @override
  String get playful => 'விளையாட்டுத்தனமான';

  @override
  String get print => 'அச்சு';

  @override
  String get socialMedia => 'சமூக ஊடகம்';

  @override
  String get onlineAdvertising => 'ஆன்லைன் விளம்பரம்';

  @override
  String get email => 'மின்னஞ்சல்';

  @override
  String get web => 'வலை';

  @override
  String get high => 'உயர்';

  @override
  String get medium => 'நடுத்தர';

  @override
  String get low => 'குறைந்த';

  @override
  String get freeForPersonalUse => 'தனிப்பட்ட பயன்பாட்டிற்கு இலவசம்';

  @override
  String get commercialUseAllowed => 'வணிக பயன்பாட்டிற்கு அனுமதி';

  @override
  String get premiumLicenseRequired => 'பிரீமியம் உரிமம் தேவை';

  @override
  String get templateName => 'டெம்ப்ளேட் பெயர்';

  @override
  String get templateDescription => 'டெம்ப்ளேட் விளக்கம்';

  @override
  String get creator => 'உருவாக்குபவர்';

  @override
  String get dimensions => 'பரிமாணங்கள்';

  @override
  String get tags => 'குறிச்சொற்கள்';

  @override
  String get colors => 'நிறங்கள்';

  @override
  String get fontStyles => 'எழுத்துரு பாணிகள்';

  @override
  String get fileFormats => 'கோப்பு வடிவங்கள்';

  @override
  String get category => 'வகை';

  @override
  String get subCategory => 'துணை வகை';

  @override
  String get layoutType => 'தளவமைப்பு வகை';

  @override
  String get targetAudience => 'இலக்கு பார்வையாளர்கள்';

  @override
  String get designStyle => 'வடிவமைப்பு பாணி';

  @override
  String get usageType => 'பயன்பாட்டு வகை';

  @override
  String get resolution => 'தெளிவுத்திறன்';

  @override
  String get licenseType => 'உரிமம் வகை';

  @override
  String get isPremium => 'பிரீமியம்';

  @override
  String get isActive => 'செயலில்';

  @override
  String get selectImage => 'படத்தைத் தேர்ந்தெடுக்கவும்';

  @override
  String get uploadTemplate => 'டெம்ப்ளேட்டை பதிவேற்றவும்';

  @override
  String get updateTemplate => 'டெம்ப்ளேட்டை புதுப்பிக்கவும்';

  @override
  String get uploading => 'பதிவேற்றப்படுகிறது...';

  @override
  String get templateUploadedSuccessfully => 'டெம்ப்ளேட் வெற்றிகரமாக பதிவேற்றப்பட்டது';

  @override
  String get templateUpdatedSuccessfully => 'டெம்ப்ளேட் வெற்றிகரமாக புதுப்பிக்கப்பட்டது';

  @override
  String get failedToUploadTemplate => 'டெம்ப்ளேட் பதிவேற்றத் தவறியது';

  @override
  String get failedToUpdateTemplate => 'டெம்ப்ளேட் புதுப்பிக்கத் தவறியது';

  @override
  String get pleaseSelectImage => 'தயவுசெய்து ஒரு படத்தைத் தேர்ந்தெடுக்கவும்';

  @override
  String get pleaseEnterTemplateName => 'தயவுசெய்து டெம்ப்ளேட் பெயரை உள்ளிடவும்';

  @override
  String get pleaseSelectCategory => 'தயவுசெய்து ஒரு வகையைத் தேர்ந்தெடுக்கவும்';

  @override
  String get loadingBanners => 'பேனர்கள் ஏற்றப்படுகின்றன...';

  @override
  String get loadingMoreBanners => 'மேலும் பேனர்கள் ஏற்றப்படுகின்றன...';

  @override
  String get errorLoadingBanners => 'பேனர்களை ஏற்றுவதில் பிழை';

  @override
  String get noBannersFound => 'பேனர்கள் எதுவும் கிடைக்கவில்லை';

  @override
  String get selectBanner => 'பேனரைத் தேர்ந்தெடுக்கவும்';

  @override
  String get customizeTemplate => 'டெம்ப்ளேட்டை தனிப்பயனாக்கவும்';

  @override
  String get imageCustomization => 'படத் தனிப்பயனாக்கம்';

  @override
  String get bannerCustomization => 'பேனர் தனிப்பயனாக்கம்';

  @override
  String get textCustomization => 'உரை தனிப்பயனாக்கம்';

  @override
  String get previewTemplate => 'டெம்ப்ளேட் முன்னோட்டம்';

  @override
  String get saveTemplate => 'டெம்ப்ளேட்டைச் சேமிக்கவும்';

  @override
  String get downloadTemplate => 'டெம்ப்ளேட்டைப் பதிவிறக்கவும்';

  @override
  String get shareTemplate => 'டெம்ப்ளேட்டைப் பகிரவும்';

  @override
  String get templateSavedSuccessfully => 'டெம்ப்ளேட் வெற்றிகரமாக சேமிக்கப்பட்டது';

  @override
  String get templateDownloadedSuccessfully => 'டெம்ப்ளேட் வெற்றிகரமாக பதிவிறக்கப்பட்டது';

  @override
  String get failedToSaveTemplate => 'டெம்ப்ளேட் சேமிக்கத் தவறியது';

  @override
  String get failedToDownloadTemplate => 'டெம்ப்ளேட் பதிவிறக்கத் தவறியது';

  @override
  String get processingImage => 'படம் செயலாக்கப்படுகிறது...';

  @override
  String get generatingPreview => 'முன்னோட்டம் உருவாக்கப்படுகிறது...';

  @override
  String get preparingDownload => 'பதிவிறக்கம் தயாராகிறது...';

  @override
  String get manageBusiness => 'வணிக மேலாண்மை';

  @override
  String get configureBusinessDetails => 'உங்கள் வணிக விவரங்கள், முகவரி மற்றும் பிற அளவுருக்களை கட்டமைக்கவும்';

  @override
  String get managePolitical => 'அரசியல் மேலாண்மை';

  @override
  String get failedToLoadBusinessProfile => 'வணிக சுயவிவரத்தை ஏற்றுவதில் தோல்வி';

  @override
  String get failedToLoadPoliticalProfile => 'அரசியல் சுயவிவரத்தை ஏற்றுவதில் தோல்வி';

  @override
  String get noBusinessParametersAvailable => 'வணிக அளவுருக்கள் எதுவும் கிடைக்கவில்லை';

  @override
  String get businessParametersConfiguredByAdmin => 'வணிக அளவுருக்கள் நிர்வாகியால் கட்டமைக்கப்படும்';

  @override
  String get noPoliticalParametersAvailable => 'அரசியல் அளவுருக்கள் எதுவும் கிடைக்கவில்லை';

  @override
  String get politicalParametersConfiguredByAdmin => 'அரசியல் அளவுருக்கள் நிர்வாகியால் கட்டமைக்கப்படும்';

  @override
  String get saveBusinessParameters => 'வணிக அளவுருக்களைச் சேமிக்கவும்';

  @override
  String get savePoliticalParameters => 'அரசியல் அளவுருக்களைச் சேமிக்கவும்';

  @override
  String get businessParametersSavedSuccessfully => 'வணிக அளவுருக்கள் வெற்றிகரமாக சேமிக்கப்பட்டன';

  @override
  String get politicalParametersSavedSuccessfully => 'அரசியல் அளவுருக்கள் வெற்றிகரமாக சேமிக்கப்பட்டன';

  @override
  String get failedToSaveBusinessParameters => 'வணிக அளவுருக்களைச் சேமிப்பதில் தோல்வி';

  @override
  String get failedToSavePoliticalParameters => 'அரசியல் அளவுருக்களைச் சேமிப்பதில் தோல்வி';

  @override
  String get businessType => 'வணிக வகை';

  @override
  String get businessAddress => 'வணிக முகவரி';

  @override
  String get businessLocation => 'வணிக இடம்';

  @override
  String get businessMobile => 'வணிக மொபைல்';

  @override
  String get businessWebsite => 'வணிக வலைத்தளம்';

  @override
  String get businessEmail => 'வணிக மின்னஞ்சல்';

  @override
  String get selectBusinessLogo => 'வணிக லோகோவைத் தேர்ந்தெடுக்கவும்';

  @override
  String get politicalPhoto => 'அரசியல் புகைப்படம்';

  @override
  String get selectPoliticalPhoto => 'அரசியல் புகைப்படத்தைத் தேர்ந்தெடுக்கவும்';

  @override
  String get partyName => 'கட்சி பெயர்';

  @override
  String get politicalPosition => 'அரசியல் பதவி';

  @override
  String get constituency => 'தொகுதி';

  @override
  String get politicalExperience => 'அரசியல் அனுபவம்';

  @override
  String get campaignSlogan => 'பிரச்சார முழக்கம்';

  @override
  String get politicalAchievements => 'அரசியல் சாதனைகள்';

  @override
  String get isRequired => 'தேவை';

  @override
  String get pleaseEnterValidNumber => 'தயவுசெய்து சரியான எண்ணை உள்ளிடவும்';

  @override
  String get allTypes => 'அனைத்து வகைகள்';

  @override
  String get removePremium => 'பிரீமியம் அகற்று';

  @override
  String get makePremium => 'பிரீமியம் ஆக்கு';

  @override
  String get deleteUser => 'பயனரை நீக்கு';

  @override
  String get userManagement => 'பயனர் மேலாண்மை';

  @override
  String get searchUsers => 'பயனர்களைத் தேடு...';

  @override
  String get noUsersFound => 'பயனர்கள் எவரும் கிடைக்கவில்லை';

  @override
  String get loadingUsers => 'பயனர்கள் ஏற்றப்படுகின்றனர்...';

  @override
  String get failedToLoadUsers => 'பயனர்களை ஏற்றுவதில் தோல்வி';

  @override
  String get userDetails => 'பயனர் விவரங்கள்';

  @override
  String get accountCreated => 'கணக்கு உருவாக்கப்பட்டது';

  @override
  String get lastUpdated => 'கடைசியாக புதுப்பிக்கப்பட்டது';

  @override
  String get profileComplete => 'சுயவிவரம் முழுமையானது';

  @override
  String get profileIncomplete => 'சுயவிவரம் முழுமையடையவில்லை';

  @override
  String get premiumStatus => 'பிரீமியம் நிலை';

  @override
  String get active => 'செயலில்';

  @override
  String get inactive => 'செயலில் இல்லை';

  @override
  String get subscriptionPlan => 'சந்தா திட்டம்';

  @override
  String get subscriptionEndDate => 'சந்தா முடிவு தேதி';

  @override
  String get languagePreference => 'மொழி விருப்பம்';

  @override
  String get totalPosters => 'மொத்த போஸ்டர்கள்';

  @override
  String get adminStatus => 'நிர்வாக நிலை';

  @override
  String get isAdmin => 'நிர்வாகி';

  @override
  String get isNotAdmin => 'நிர்வாகி அல்ல';

  @override
  String get allUsers => 'அனைத்து பயனர்கள்';

  @override
  String get free => 'இலவசம்';

  @override
  String get retry => 'மீண்டும் முயற்சிக்கவும்';

  @override
  String get tryAdjustingFilters => 'உங்கள் தேடல் அல்லது வடிப்பான்களை சரிசெய்ய முயற்சிக்கவும்';

  @override
  String get notProvided => 'வழங்கப்படவில்லை';

  @override
  String get yes => 'ஆம்';

  @override
  String get no => 'இல்லை';

  @override
  String get created => 'உருவாக்கப்பட்டது';

  @override
  String get name => 'பெயர்';

  @override
  String get userPremiumStatusRemoved => 'பயனர் பிரீமியம் நிலை அகற்றப்பட்டது';

  @override
  String get userUpgradedToPremium => 'பயனர் பிரீமியமாக மேம்படுத்தப்பட்டார்';

  @override
  String get errorUpdatingUser => 'பயனரை புதுப்பிப்பதில் பிழை';

  @override
  String areYouSureDeleteUser(String userName) {
    return 'நீங்கள் உண்மையில் $userName ஐ நீக்க விரும்புகிறீர்களா? இந்த செயலை மாற்ற முடியாது.';
  }

  @override
  String get userDeletedSuccessfully => 'பயனர் வெற்றிகரமாக நீக்கப்பட்டார்';

  @override
  String get errorDeletingUser => 'பயனரை நீக்குவதில் பிழை';

  @override
  String get add => 'சேர்க்கவும்';

  @override
  String get extend => 'நீட்டிக்கவும்';

  @override
  String subscriptionExtended(String duration) {
    return 'சந்தா $duration நீட்டிக்கப்பட்டது!';
  }

  @override
  String failedToExtendSubscription(String error) {
    return 'சந்தா நீட்டிக்க முடியவில்லை: $error';
  }

  @override
  String failedToPurchaseSubscription(String error) {
    return 'சந்தா வாங்க முடியவில்லை: $error';
  }

  @override
  String get failedToRestorePurchases => 'வாங்கியவற்றை மீட்டெடுக்க முடியவில்லை';

  @override
  String get trialPlanDescription => 'குறைந்த செலவில் 1 வாரத்திற்கு பிரீமியம் அம்சங்களை முயற்சிக்கவும்';

  @override
  String get threeMonthPlanDescription => 'குறுகிய கால திட்டங்களுக்கு ஏற்றது';

  @override
  String get sixMonthPlanDescription => 'நடுத்தர கால பயன்பாட்டிற்கு சிறந்த மதிப்பு';

  @override
  String get annualPlanDescription => 'வழக்கமான பயனர்களுக்கு சிறந்த மதிப்பு';

  @override
  String get allPremiumFeatures => 'அனைத்து பிரீமியம் அம்சங்கள்';

  @override
  String get autoRenewsAt => '3 மாதங்களுக்கு ₹199 இல் தானாக புதுப்பிக்கும்';

  @override
  String get cancelAnytime => 'எப்போது வேண்டுமானாலும் ரத்து செய்யவும்';

  @override
  String get quarterlyBilling => 'காலாண்டு பில்லிங்';

  @override
  String get save50Percent => '3-மாத திட்டத்துடன் ஒப்பிடும்போது 50% சேமிப்பு';

  @override
  String get prioritySupport => 'முன்னுரிமை ஆதரவு';

  @override
  String get save58Percent => '3-மாத திட்டத்துடன் ஒப்பிடும்போது 58% சேமிப்பு';

  @override
  String get earlyAccessToNewFeatures => 'புதிய அம்சங்களுக்கு முன்கூட்டிய அணுகல்';

  @override
  String activeUntilDate(String date) {
    return '$date வரை செயலில்';
  }
}
