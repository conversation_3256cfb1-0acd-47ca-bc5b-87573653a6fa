import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_bn.dart';
import 'app_localizations_en.dart';
import 'app_localizations_gu.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_mr.dart';
import 'app_localizations_ta.dart';
import 'app_localizations_te.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('bn'),
    Locale('en'),
    Locale('gu'),
    Locale('hi'),
    Locale('mr'),
    Locale('ta'),
    Locale('te')
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Quick Posters'**
  String get appTitle;

  /// Welcome message displayed to users
  ///
  /// In en, this message translates to:
  /// **'Welcome to Quick Posters'**
  String get welcome;

  /// Login button text
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// Logout menu item
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Phone number field label
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// Phone number field hint
  ///
  /// In en, this message translates to:
  /// **'Enter your phone number'**
  String get enterPhoneNumber;

  /// Send OTP button text
  ///
  /// In en, this message translates to:
  /// **'Send OTP'**
  String get sendOTP;

  /// Verify OTP button text
  ///
  /// In en, this message translates to:
  /// **'Verify OTP'**
  String get verifyOTP;

  /// OTP input placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter the 6-digit OTP'**
  String get enterOTP;

  /// Resend OTP button text
  ///
  /// In en, this message translates to:
  /// **'Resend OTP'**
  String get resendOTP;

  /// Home navigation item
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Templates section title
  ///
  /// In en, this message translates to:
  /// **'Templates'**
  String get templates;

  /// Categories section title
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get categories;

  /// All categories option
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Premium features label
  ///
  /// In en, this message translates to:
  /// **'Premium'**
  String get premium;

  /// Profile section title
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Settings section title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Language settings option
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Language selection screen title
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// Business user type
  ///
  /// In en, this message translates to:
  /// **'Business'**
  String get businessUser;

  /// Political user type
  ///
  /// In en, this message translates to:
  /// **'Politician'**
  String get politicalUser;

  /// Individual user type label
  ///
  /// In en, this message translates to:
  /// **'Individual User'**
  String get individualUser;

  /// Create poster button text
  ///
  /// In en, this message translates to:
  /// **'Create Poster'**
  String get createPoster;

  /// Edit template button text
  ///
  /// In en, this message translates to:
  /// **'Edit Template'**
  String get editTemplate;

  /// Save image button text
  ///
  /// In en, this message translates to:
  /// **'Save Image'**
  String get saveImage;

  /// Share image button
  ///
  /// In en, this message translates to:
  /// **'Share Image'**
  String get shareImage;

  /// Premium feature indicator
  ///
  /// In en, this message translates to:
  /// **'Premium Feature'**
  String get premiumFeature;

  /// Upgrade to premium button text
  ///
  /// In en, this message translates to:
  /// **'Upgrade Now'**
  String get upgradeNow;

  /// Subscription plans screen title
  ///
  /// In en, this message translates to:
  /// **'Subscription Plans'**
  String get subscriptionPlans;

  /// Trial subscription plan name
  ///
  /// In en, this message translates to:
  /// **'Trial Plan'**
  String get trialPlan;

  /// 3-month subscription plan name
  ///
  /// In en, this message translates to:
  /// **'3-Month Plan'**
  String get threeMonthPlan;

  /// 6-month subscription plan name
  ///
  /// In en, this message translates to:
  /// **'6-Month Plan'**
  String get sixMonthPlan;

  /// Annual subscription plan name
  ///
  /// In en, this message translates to:
  /// **'Annual Plan'**
  String get annualPlan;

  /// Currency format for Indian Rupees
  ///
  /// In en, this message translates to:
  /// **'₹{amount}'**
  String rupees(String amount);

  /// My Profile menu item
  ///
  /// In en, this message translates to:
  /// **'My Profile'**
  String get myProfile;

  /// Notifications menu item
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// About section title
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// Privacy Policy menu item
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Terms of Service title
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// Refund Policy title
  ///
  /// In en, this message translates to:
  /// **'Refund Policy'**
  String get refundPolicy;

  /// AI Image Generator menu item
  ///
  /// In en, this message translates to:
  /// **'AI Image Generator'**
  String get aiImageGenerator;

  /// Admin dashboard menu item
  ///
  /// In en, this message translates to:
  /// **'Admin Dashboard'**
  String get adminDashboard;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Error message title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Success message title
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Try again button text
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No internet connection error message
  ///
  /// In en, this message translates to:
  /// **'No internet connection'**
  String get noInternetConnection;

  /// Generic error message
  ///
  /// In en, this message translates to:
  /// **'Something went wrong'**
  String get somethingWentWrong;

  /// Phone number validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid 10-digit phone number'**
  String get phoneNumberValidation;

  /// OTP validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid 6-digit OTP'**
  String get otpValidation;

  /// OTP sent confirmation message
  ///
  /// In en, this message translates to:
  /// **'OTP sent successfully'**
  String get otpSent;

  /// OTP expired error message
  ///
  /// In en, this message translates to:
  /// **'OTP has expired. Please request a new one'**
  String get otpExpired;

  /// Invalid OTP error message
  ///
  /// In en, this message translates to:
  /// **'Invalid OTP. Please try again'**
  String get invalidOtp;

  /// Login success message
  ///
  /// In en, this message translates to:
  /// **'Login successful'**
  String get loginSuccessful;

  /// Welcome back message for returning users
  ///
  /// In en, this message translates to:
  /// **'Welcome back!'**
  String get welcomeBack;

  /// Get started button text
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get getStarted;

  /// Continue button text
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueText;

  /// Skip button text
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Previous button text
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// Done button text
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// User type selection hint
  ///
  /// In en, this message translates to:
  /// **'Select User Type'**
  String get selectUserType;

  /// Account type selection instruction
  ///
  /// In en, this message translates to:
  /// **'Choose your account type'**
  String get chooseAccountType;

  /// Business description field label
  ///
  /// In en, this message translates to:
  /// **'Business Description'**
  String get businessDescription;

  /// Political user type description
  ///
  /// In en, this message translates to:
  /// **'Ideal for political campaigns and leaders'**
  String get politicalDescription;

  /// Individual user type description
  ///
  /// In en, this message translates to:
  /// **'Great for personal use and social media'**
  String get individualDescription;

  /// Profile completion screen title
  ///
  /// In en, this message translates to:
  /// **'Complete Your Profile'**
  String get completeProfile;

  /// Profile completion description
  ///
  /// In en, this message translates to:
  /// **'Help us personalize your experience'**
  String get profileCompletionDescription;

  /// Business name field label
  ///
  /// In en, this message translates to:
  /// **'Business Name'**
  String get businessName;

  /// Business name input placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter your business name'**
  String get enterBusinessName;

  /// Business category field label
  ///
  /// In en, this message translates to:
  /// **'Business Category'**
  String get businessCategory;

  /// Business category selection placeholder
  ///
  /// In en, this message translates to:
  /// **'Select your business category'**
  String get selectBusinessCategory;

  /// Political party selection label
  ///
  /// In en, this message translates to:
  /// **'Political Party'**
  String get politicalParty;

  /// Political party selection placeholder
  ///
  /// In en, this message translates to:
  /// **'Select your political party'**
  String get selectPoliticalParty;

  /// Designation input label
  ///
  /// In en, this message translates to:
  /// **'Designation'**
  String get designation;

  /// Designation input placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter your designation'**
  String get enterDesignation;

  /// Full name input label
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullName;

  /// Full name input placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter your full name'**
  String get enterFullName;

  /// Profile update success message
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdated;

  /// Explore templates button text
  ///
  /// In en, this message translates to:
  /// **'Explore Templates'**
  String get exploreTemplates;

  /// Featured templates section title
  ///
  /// In en, this message translates to:
  /// **'Featured Templates'**
  String get featuredTemplates;

  /// Recent templates section title
  ///
  /// In en, this message translates to:
  /// **'Recent Templates'**
  String get recentTemplates;

  /// Popular templates section title
  ///
  /// In en, this message translates to:
  /// **'Popular Templates'**
  String get popularTemplates;

  /// Loading templates message
  ///
  /// In en, this message translates to:
  /// **'Loading templates...'**
  String get loadingTemplates;

  /// No templates found message
  ///
  /// In en, this message translates to:
  /// **'No templates found'**
  String get noTemplatesFound;

  /// Search templates placeholder
  ///
  /// In en, this message translates to:
  /// **'Search templates...'**
  String get searchTemplates;

  /// Filter by category label
  ///
  /// In en, this message translates to:
  /// **'Filter by Category'**
  String get filterByCategory;

  /// View all button text
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// Load more button text
  ///
  /// In en, this message translates to:
  /// **'Load More'**
  String get loadMore;

  /// Refreshing indicator text
  ///
  /// In en, this message translates to:
  /// **'Refreshing...'**
  String get refreshing;

  /// Pull to refresh instruction
  ///
  /// In en, this message translates to:
  /// **'Pull to refresh'**
  String get pullToRefresh;

  /// Refresh button tooltip
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// Account section title
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// App Settings section title
  ///
  /// In en, this message translates to:
  /// **'App Settings'**
  String get appSettings;

  /// Tools section title
  ///
  /// In en, this message translates to:
  /// **'Tools'**
  String get tools;

  /// Premium subscription menu item
  ///
  /// In en, this message translates to:
  /// **'Premium Subscription'**
  String get premiumSubscription;

  /// Premium user status message
  ///
  /// In en, this message translates to:
  /// **'You are a premium user'**
  String get youArePremiumUser;

  /// Upgrade to premium message
  ///
  /// In en, this message translates to:
  /// **'Upgrade to premium'**
  String get upgradeToPremium;

  /// AI image generator description
  ///
  /// In en, this message translates to:
  /// **'Create images with AI'**
  String get createImagesWithAI;

  /// About QuickPosters menu item
  ///
  /// In en, this message translates to:
  /// **'About QuickPosters'**
  String get aboutQuickPosters;

  /// App version
  ///
  /// In en, this message translates to:
  /// **'Version 1.0.0'**
  String get version;

  /// Privacy policy description
  ///
  /// In en, this message translates to:
  /// **'Read our privacy policy'**
  String get readPrivacyPolicy;

  /// Profile menu description
  ///
  /// In en, this message translates to:
  /// **'View and edit your profile'**
  String get viewEditProfile;

  /// Language menu description
  ///
  /// In en, this message translates to:
  /// **'Change app language'**
  String get changeAppLanguage;

  /// Error message when user data fails to load
  ///
  /// In en, this message translates to:
  /// **'Failed to load user data'**
  String get failedToLoadUserData;

  /// Success message when profile is updated
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdatedSuccessfully;

  /// Error message when profile save fails
  ///
  /// In en, this message translates to:
  /// **'Failed to save profile'**
  String get failedToSaveProfile;

  /// Business profile management title
  ///
  /// In en, this message translates to:
  /// **'Manage Business Profile'**
  String get manageBusinessProfile;

  /// Political profile management title
  ///
  /// In en, this message translates to:
  /// **'Manage Political Profile'**
  String get managePoliticalProfile;

  /// Political configuration description
  ///
  /// In en, this message translates to:
  /// **'Configure your political details, party, and other parameters'**
  String get configurePoliticalDetails;

  /// Business parameters page title
  ///
  /// In en, this message translates to:
  /// **'Business Parameters'**
  String get businessParameters;

  /// Political parameters page title
  ///
  /// In en, this message translates to:
  /// **'Political Parameters'**
  String get politicalParameters;

  /// Content refresh success message
  ///
  /// In en, this message translates to:
  /// **'Content refreshed successfully'**
  String get contentRefreshedSuccessfully;

  /// Content refresh error message
  ///
  /// In en, this message translates to:
  /// **'Failed to refresh content'**
  String get failedToRefreshContent;

  /// My subscription page title
  ///
  /// In en, this message translates to:
  /// **'My Subscription'**
  String get mySubscription;

  /// Image editor initial instruction
  ///
  /// In en, this message translates to:
  /// **'Start by adding a base image'**
  String get startByAddingBaseImage;

  /// Base image description
  ///
  /// In en, this message translates to:
  /// **'This will be the background of your poster'**
  String get backgroundOfPoster;

  /// Select base image button
  ///
  /// In en, this message translates to:
  /// **'Select Base Image'**
  String get selectBaseImage;

  /// Add base image button
  ///
  /// In en, this message translates to:
  /// **'Add Base Image'**
  String get addBaseImage;

  /// About page placeholder message
  ///
  /// In en, this message translates to:
  /// **'About page coming soon'**
  String get aboutPageComingSoon;

  /// Language settings placeholder message
  ///
  /// In en, this message translates to:
  /// **'Language settings coming soon'**
  String get languageSettingsComingSoon;

  /// Notification settings placeholder message
  ///
  /// In en, this message translates to:
  /// **'Notification settings coming soon'**
  String get notificationSettingsComingSoon;

  /// Notification settings description
  ///
  /// In en, this message translates to:
  /// **'Manage notification settings'**
  String get manageNotificationSettings;

  /// Image shape selection title
  ///
  /// In en, this message translates to:
  /// **'Image Shape'**
  String get imageShape;

  /// Business logo shape selection title
  ///
  /// In en, this message translates to:
  /// **'Business Logo Shape'**
  String get businessLogoShape;

  /// Profile photo option
  ///
  /// In en, this message translates to:
  /// **'Profile Photo'**
  String get profilePhoto;

  /// Business logo field label
  ///
  /// In en, this message translates to:
  /// **'Business Logo'**
  String get businessLogo;

  /// User type selection validation message
  ///
  /// In en, this message translates to:
  /// **'Please select a user type'**
  String get pleaseSelectUserType;

  /// Error message when user types fail to load
  ///
  /// In en, this message translates to:
  /// **'Failed to load user types'**
  String get failedToLoadUserTypes;

  /// Error message when user type save fails
  ///
  /// In en, this message translates to:
  /// **'Failed to save user type'**
  String get failedToSaveUserType;

  /// Success message when profile is completed
  ///
  /// In en, this message translates to:
  /// **'Profile completed successfully'**
  String get profileCompletedSuccessfully;

  /// Profile settings description
  ///
  /// In en, this message translates to:
  /// **'View and edit your profile information'**
  String get viewEditProfileInfo;

  /// Image source selection dialog title
  ///
  /// In en, this message translates to:
  /// **'Choose Image Source'**
  String get chooseImageSource;

  /// Custom image option
  ///
  /// In en, this message translates to:
  /// **'Custom Image'**
  String get customImage;

  /// Auto select option
  ///
  /// In en, this message translates to:
  /// **'Auto Select'**
  String get autoSelect;

  /// Image source selection title
  ///
  /// In en, this message translates to:
  /// **'Image Source'**
  String get imageSource;

  /// Shape options section title
  ///
  /// In en, this message translates to:
  /// **'Shape Options'**
  String get shapeOptions;

  /// None option
  ///
  /// In en, this message translates to:
  /// **'None'**
  String get none;

  /// Circle shape option
  ///
  /// In en, this message translates to:
  /// **'Circle'**
  String get circle;

  /// Rectangle shape option
  ///
  /// In en, this message translates to:
  /// **'Rectangle'**
  String get rectangle;

  /// Rounded rectangle shape option
  ///
  /// In en, this message translates to:
  /// **'Rounded Rectangle'**
  String get roundedRectangle;

  /// Diamond shape option
  ///
  /// In en, this message translates to:
  /// **'Diamond'**
  String get diamond;

  /// Hexagon shape option
  ///
  /// In en, this message translates to:
  /// **'Hexagon'**
  String get hexagon;

  /// App name in drawer
  ///
  /// In en, this message translates to:
  /// **'QuickPosters'**
  String get quickPosters;

  /// Account actions section title
  ///
  /// In en, this message translates to:
  /// **'Account Actions'**
  String get accountActions;

  /// Logout description
  ///
  /// In en, this message translates to:
  /// **'Sign out from your account'**
  String get signOutFromAccount;

  /// Language change loading message
  ///
  /// In en, this message translates to:
  /// **'Changing language...'**
  String get changingLanguage;

  /// Phone number field label
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumberLabel;

  /// Phone number field hint
  ///
  /// In en, this message translates to:
  /// **'Enter your phone number'**
  String get enterYourPhoneNumber;

  /// User type field label
  ///
  /// In en, this message translates to:
  /// **'User Type'**
  String get userType;

  /// Save to gallery button text
  ///
  /// In en, this message translates to:
  /// **'Save to Gallery'**
  String get saveToGallery;

  /// Share button text
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// WhatsApp share option
  ///
  /// In en, this message translates to:
  /// **'WhatsApp'**
  String get whatsApp;

  /// Invalid phone number error message
  ///
  /// In en, this message translates to:
  /// **'The phone number format is incorrect. Please enter a valid phone number.'**
  String get invalidPhoneNumber;

  /// SMS quota exceeded error message
  ///
  /// In en, this message translates to:
  /// **'SMS quota exceeded. Please try again later.'**
  String get quotaExceeded;

  /// User disabled error message
  ///
  /// In en, this message translates to:
  /// **'This user has been disabled. Please contact support.'**
  String get userDisabled;

  /// Operation not allowed error message
  ///
  /// In en, this message translates to:
  /// **'Phone authentication is not enabled. Please contact support.'**
  String get operationNotAllowed;

  /// Captcha check failed error message
  ///
  /// In en, this message translates to:
  /// **'reCAPTCHA verification failed. Please try again.'**
  String get captchaCheckFailed;

  /// Missing client identifier error message
  ///
  /// In en, this message translates to:
  /// **'The app verification process failed. Please try again or contact support.'**
  String get missingClientIdentifier;

  /// Too many requests error message
  ///
  /// In en, this message translates to:
  /// **'Too many requests from this device. We have temporarily blocked all requests from this device due to unusual activity. Please try again after some time (usually a few hours).'**
  String get tooManyRequests;

  /// Generic verification failed message
  ///
  /// In en, this message translates to:
  /// **'Verification failed. Please try again.'**
  String get verificationFailed;

  /// Failed to get user data error message
  ///
  /// In en, this message translates to:
  /// **'Failed to get user data'**
  String get failedToGetUserData;

  /// User not found error message
  ///
  /// In en, this message translates to:
  /// **'User not found'**
  String get userNotFound;

  /// Politician user type
  ///
  /// In en, this message translates to:
  /// **'Politician'**
  String get politician;

  /// Businessman user type
  ///
  /// In en, this message translates to:
  /// **'Businessman'**
  String get businessman;

  /// Regular user type
  ///
  /// In en, this message translates to:
  /// **'Regular'**
  String get regularUser;

  /// Politician user type description
  ///
  /// In en, this message translates to:
  /// **'For political campaigns and promotions'**
  String get forPoliticalCampaigns;

  /// Businessman user type description
  ///
  /// In en, this message translates to:
  /// **'For business promotions and advertisements'**
  String get forBusinessPromotions;

  /// Regular user type description
  ///
  /// In en, this message translates to:
  /// **'For personal use and general purposes'**
  String get forPersonalUse;

  /// Development login page title
  ///
  /// In en, this message translates to:
  /// **'Development Login'**
  String get developmentLogin;

  /// Development mode title
  ///
  /// In en, this message translates to:
  /// **'Development Mode'**
  String get developmentMode;

  /// Development mode description
  ///
  /// In en, this message translates to:
  /// **'You are logged in as a developer.\nThis is a temporary mode while Firebase phone authentication is blocked.'**
  String get loggedInAsDeveloper;

  /// No active subscription status
  ///
  /// In en, this message translates to:
  /// **'No active subscription'**
  String get noActiveSubscription;

  /// Subscription expires in X days
  ///
  /// In en, this message translates to:
  /// **'Expires in {days} days'**
  String expiresInDays(int days);

  /// Subscription active until date
  ///
  /// In en, this message translates to:
  /// **'Active until {date}'**
  String activeUntil(String date);

  /// Download image button
  ///
  /// In en, this message translates to:
  /// **'Download Image'**
  String get downloadImage;

  /// Share on WhatsApp button
  ///
  /// In en, this message translates to:
  /// **'Share on WhatsApp'**
  String get shareOnWhatsApp;

  /// Success message when custom image is selected
  ///
  /// In en, this message translates to:
  /// **'Custom image selected!'**
  String get customImageSelected;

  /// Error message when selecting image fails
  ///
  /// In en, this message translates to:
  /// **'Error selecting image: {error}'**
  String errorSelectingImage(String error);

  /// Image added success message
  ///
  /// In en, this message translates to:
  /// **'Image added successfully!'**
  String get imageAddedSuccessfully;

  /// Error adding image message
  ///
  /// In en, this message translates to:
  /// **'Error adding image: {error}'**
  String errorAddingImage(String error);

  /// No image available message
  ///
  /// In en, this message translates to:
  /// **'No {source} image available'**
  String noImageAvailable(String source);

  /// Email address field label
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// Email address field hint
  ///
  /// In en, this message translates to:
  /// **'Enter your email address'**
  String get enterEmailAddress;

  /// Optional field indicator
  ///
  /// In en, this message translates to:
  /// **'Optional'**
  String get optional;

  /// Select profile image button
  ///
  /// In en, this message translates to:
  /// **'Select Profile Image'**
  String get selectProfileImage;

  /// Camera option
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// Gallery option
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// Remove image option
  ///
  /// In en, this message translates to:
  /// **'Remove Image'**
  String get removeImage;

  /// Save profile button
  ///
  /// In en, this message translates to:
  /// **'Save Profile'**
  String get saveProfile;

  /// Update profile button
  ///
  /// In en, this message translates to:
  /// **'Update Profile'**
  String get updateProfile;

  /// Profile image update success message
  ///
  /// In en, this message translates to:
  /// **'Profile image updated successfully'**
  String get profileImageUpdated;

  /// Profile image update error message
  ///
  /// In en, this message translates to:
  /// **'Failed to update profile image'**
  String get failedToUpdateProfileImage;

  /// Email validation error message
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get pleaseEnterValidEmail;

  /// Name validation error message
  ///
  /// In en, this message translates to:
  /// **'Please enter your name'**
  String get pleaseEnterName;

  /// Name too short validation error
  ///
  /// In en, this message translates to:
  /// **'Name must be at least 2 characters long'**
  String get nameTooShort;

  /// Invalid email format error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email format'**
  String get invalidEmailFormat;

  /// Business category
  ///
  /// In en, this message translates to:
  /// **'Business'**
  String get business;

  /// Event category
  ///
  /// In en, this message translates to:
  /// **'Event'**
  String get event;

  /// Festival category
  ///
  /// In en, this message translates to:
  /// **'Festival'**
  String get festival;

  /// Party category
  ///
  /// In en, this message translates to:
  /// **'Party'**
  String get party;

  /// Education category
  ///
  /// In en, this message translates to:
  /// **'Education'**
  String get education;

  /// Health category
  ///
  /// In en, this message translates to:
  /// **'Health'**
  String get health;

  /// Technology category
  ///
  /// In en, this message translates to:
  /// **'Technology'**
  String get technology;

  /// Food category
  ///
  /// In en, this message translates to:
  /// **'Food'**
  String get food;

  /// Travel category
  ///
  /// In en, this message translates to:
  /// **'Travel'**
  String get travel;

  /// Sports category
  ///
  /// In en, this message translates to:
  /// **'Sports'**
  String get sports;

  /// Fashion category
  ///
  /// In en, this message translates to:
  /// **'Fashion'**
  String get fashion;

  /// Real Estate category
  ///
  /// In en, this message translates to:
  /// **'Real Estate'**
  String get realEstate;

  /// Flyers subcategory
  ///
  /// In en, this message translates to:
  /// **'Flyers'**
  String get flyers;

  /// Brochures subcategory
  ///
  /// In en, this message translates to:
  /// **'Brochures'**
  String get brochures;

  /// Business Cards subcategory
  ///
  /// In en, this message translates to:
  /// **'Business Cards'**
  String get businessCards;

  /// Presentations subcategory
  ///
  /// In en, this message translates to:
  /// **'Presentations'**
  String get presentations;

  /// Logos subcategory
  ///
  /// In en, this message translates to:
  /// **'Logos'**
  String get logos;

  /// Invitations subcategory
  ///
  /// In en, this message translates to:
  /// **'Invitations'**
  String get invitations;

  /// Tickets subcategory
  ///
  /// In en, this message translates to:
  /// **'Tickets'**
  String get tickets;

  /// Programs subcategory
  ///
  /// In en, this message translates to:
  /// **'Programs'**
  String get programs;

  /// Banners subcategory
  ///
  /// In en, this message translates to:
  /// **'Banners'**
  String get banners;

  /// Posters subcategory
  ///
  /// In en, this message translates to:
  /// **'Posters'**
  String get posters;

  /// Celebration subcategory
  ///
  /// In en, this message translates to:
  /// **'Celebration'**
  String get celebration;

  /// Religious subcategory
  ///
  /// In en, this message translates to:
  /// **'Religious'**
  String get religious;

  /// Cultural subcategory
  ///
  /// In en, this message translates to:
  /// **'Cultural'**
  String get cultural;

  /// Seasonal subcategory
  ///
  /// In en, this message translates to:
  /// **'Seasonal'**
  String get seasonal;

  /// Birthday subcategory
  ///
  /// In en, this message translates to:
  /// **'Birthday'**
  String get birthday;

  /// Wedding subcategory
  ///
  /// In en, this message translates to:
  /// **'Wedding'**
  String get wedding;

  /// Anniversary subcategory
  ///
  /// In en, this message translates to:
  /// **'Anniversary'**
  String get anniversary;

  /// Graduation subcategory
  ///
  /// In en, this message translates to:
  /// **'Graduation'**
  String get graduation;

  /// Certificates subcategory
  ///
  /// In en, this message translates to:
  /// **'Certificates'**
  String get certificates;

  /// Worksheets subcategory
  ///
  /// In en, this message translates to:
  /// **'Worksheets'**
  String get worksheets;

  /// Awareness subcategory
  ///
  /// In en, this message translates to:
  /// **'Awareness'**
  String get awareness;

  /// Medical subcategory
  ///
  /// In en, this message translates to:
  /// **'Medical'**
  String get medical;

  /// Fitness subcategory
  ///
  /// In en, this message translates to:
  /// **'Fitness'**
  String get fitness;

  /// Nutrition subcategory
  ///
  /// In en, this message translates to:
  /// **'Nutrition'**
  String get nutrition;

  /// App Promotion subcategory
  ///
  /// In en, this message translates to:
  /// **'App Promotion'**
  String get appPromotion;

  /// Software subcategory
  ///
  /// In en, this message translates to:
  /// **'Software'**
  String get software;

  /// Digital Services subcategory
  ///
  /// In en, this message translates to:
  /// **'Digital Services'**
  String get digitalServices;

  /// Menu subcategory
  ///
  /// In en, this message translates to:
  /// **'Menu'**
  String get menu;

  /// Recipe subcategory
  ///
  /// In en, this message translates to:
  /// **'Recipe'**
  String get recipe;

  /// Restaurant subcategory
  ///
  /// In en, this message translates to:
  /// **'Restaurant'**
  String get restaurant;

  /// Catering subcategory
  ///
  /// In en, this message translates to:
  /// **'Catering'**
  String get catering;

  /// Tourism subcategory
  ///
  /// In en, this message translates to:
  /// **'Tourism'**
  String get tourism;

  /// Hotels subcategory
  ///
  /// In en, this message translates to:
  /// **'Hotels'**
  String get hotels;

  /// Transportation subcategory
  ///
  /// In en, this message translates to:
  /// **'Transportation'**
  String get transportation;

  /// Adventure subcategory
  ///
  /// In en, this message translates to:
  /// **'Adventure'**
  String get adventure;

  /// Team subcategory
  ///
  /// In en, this message translates to:
  /// **'Team'**
  String get team;

  /// Tournament subcategory
  ///
  /// In en, this message translates to:
  /// **'Tournament'**
  String get tournament;

  /// Equipment subcategory
  ///
  /// In en, this message translates to:
  /// **'Equipment'**
  String get equipment;

  /// Clothing subcategory
  ///
  /// In en, this message translates to:
  /// **'Clothing'**
  String get clothing;

  /// Accessories subcategory
  ///
  /// In en, this message translates to:
  /// **'Accessories'**
  String get accessories;

  /// Beauty subcategory
  ///
  /// In en, this message translates to:
  /// **'Beauty'**
  String get beauty;

  /// Style subcategory
  ///
  /// In en, this message translates to:
  /// **'Style'**
  String get style;

  /// Property subcategory
  ///
  /// In en, this message translates to:
  /// **'Property'**
  String get property;

  /// Rental subcategory
  ///
  /// In en, this message translates to:
  /// **'Rental'**
  String get rental;

  /// Commercial subcategory
  ///
  /// In en, this message translates to:
  /// **'Commercial'**
  String get commercial;

  /// Residential subcategory
  ///
  /// In en, this message translates to:
  /// **'Residential'**
  String get residential;

  /// No shape option for image
  ///
  /// In en, this message translates to:
  /// **'No Shape'**
  String get noShape;

  /// Rounded rectangle shape option
  ///
  /// In en, this message translates to:
  /// **'Rounded'**
  String get rounded;

  /// Load more templates button text
  ///
  /// In en, this message translates to:
  /// **'Load More Templates'**
  String get loadMoreTemplates;

  /// Horizontal layout type
  ///
  /// In en, this message translates to:
  /// **'Horizontal'**
  String get horizontal;

  /// Vertical layout type
  ///
  /// In en, this message translates to:
  /// **'Vertical'**
  String get vertical;

  /// Square layout type
  ///
  /// In en, this message translates to:
  /// **'Square'**
  String get square;

  /// Small business target audience
  ///
  /// In en, this message translates to:
  /// **'Small Business'**
  String get smallBusiness;

  /// Event organizers target audience
  ///
  /// In en, this message translates to:
  /// **'Event Organizers'**
  String get eventOrganizers;

  /// Educators target audience
  ///
  /// In en, this message translates to:
  /// **'Educators'**
  String get educators;

  /// Marketers target audience
  ///
  /// In en, this message translates to:
  /// **'Marketers'**
  String get marketers;

  /// Students target audience
  ///
  /// In en, this message translates to:
  /// **'Students'**
  String get students;

  /// Professionals target audience
  ///
  /// In en, this message translates to:
  /// **'Professionals'**
  String get professionals;

  /// General public target audience
  ///
  /// In en, this message translates to:
  /// **'General Public'**
  String get generalPublic;

  /// Modern design style
  ///
  /// In en, this message translates to:
  /// **'Modern'**
  String get modern;

  /// Minimalist design style
  ///
  /// In en, this message translates to:
  /// **'Minimalist'**
  String get minimalist;

  /// Retro design style
  ///
  /// In en, this message translates to:
  /// **'Retro'**
  String get retro;

  /// Elegant design style
  ///
  /// In en, this message translates to:
  /// **'Elegant'**
  String get elegant;

  /// Creative design style
  ///
  /// In en, this message translates to:
  /// **'Creative'**
  String get creative;

  /// Professional design style
  ///
  /// In en, this message translates to:
  /// **'Professional'**
  String get professional;

  /// Playful design style
  ///
  /// In en, this message translates to:
  /// **'Playful'**
  String get playful;

  /// Print usage type
  ///
  /// In en, this message translates to:
  /// **'Print'**
  String get print;

  /// Social media usage type
  ///
  /// In en, this message translates to:
  /// **'Social Media'**
  String get socialMedia;

  /// Online advertising usage type
  ///
  /// In en, this message translates to:
  /// **'Online Advertising'**
  String get onlineAdvertising;

  /// Email usage type
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Web usage type
  ///
  /// In en, this message translates to:
  /// **'Web'**
  String get web;

  /// High resolution
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// Medium resolution
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// Low resolution
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// Free for personal use license
  ///
  /// In en, this message translates to:
  /// **'Free for personal use'**
  String get freeForPersonalUse;

  /// Commercial use allowed license
  ///
  /// In en, this message translates to:
  /// **'Commercial use allowed'**
  String get commercialUseAllowed;

  /// Premium license required
  ///
  /// In en, this message translates to:
  /// **'Premium license required'**
  String get premiumLicenseRequired;

  /// Template name field label
  ///
  /// In en, this message translates to:
  /// **'Template Name'**
  String get templateName;

  /// Template description field label
  ///
  /// In en, this message translates to:
  /// **'Template Description'**
  String get templateDescription;

  /// Creator field label
  ///
  /// In en, this message translates to:
  /// **'Creator'**
  String get creator;

  /// Dimensions field label
  ///
  /// In en, this message translates to:
  /// **'Dimensions'**
  String get dimensions;

  /// Tags field label
  ///
  /// In en, this message translates to:
  /// **'Tags'**
  String get tags;

  /// Colors field label
  ///
  /// In en, this message translates to:
  /// **'Colors'**
  String get colors;

  /// Font styles field label
  ///
  /// In en, this message translates to:
  /// **'Font Styles'**
  String get fontStyles;

  /// File formats field label
  ///
  /// In en, this message translates to:
  /// **'File Formats'**
  String get fileFormats;

  /// Category field label
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// Sub category field label
  ///
  /// In en, this message translates to:
  /// **'Sub Category'**
  String get subCategory;

  /// Layout type field label
  ///
  /// In en, this message translates to:
  /// **'Layout Type'**
  String get layoutType;

  /// Target audience field label
  ///
  /// In en, this message translates to:
  /// **'Target Audience'**
  String get targetAudience;

  /// Design style field label
  ///
  /// In en, this message translates to:
  /// **'Design Style'**
  String get designStyle;

  /// Usage type field label
  ///
  /// In en, this message translates to:
  /// **'Usage Type'**
  String get usageType;

  /// Resolution field label
  ///
  /// In en, this message translates to:
  /// **'Resolution'**
  String get resolution;

  /// License type field label
  ///
  /// In en, this message translates to:
  /// **'License Type'**
  String get licenseType;

  /// Is premium checkbox label
  ///
  /// In en, this message translates to:
  /// **'Is Premium'**
  String get isPremium;

  /// Is active checkbox label
  ///
  /// In en, this message translates to:
  /// **'Is Active'**
  String get isActive;

  /// Select image button text
  ///
  /// In en, this message translates to:
  /// **'Select Image'**
  String get selectImage;

  /// Upload template button text
  ///
  /// In en, this message translates to:
  /// **'Upload Template'**
  String get uploadTemplate;

  /// Update template button text
  ///
  /// In en, this message translates to:
  /// **'Update Template'**
  String get updateTemplate;

  /// Uploading progress message
  ///
  /// In en, this message translates to:
  /// **'Uploading...'**
  String get uploading;

  /// Template upload success message
  ///
  /// In en, this message translates to:
  /// **'Template uploaded successfully'**
  String get templateUploadedSuccessfully;

  /// Template update success message
  ///
  /// In en, this message translates to:
  /// **'Template updated successfully'**
  String get templateUpdatedSuccessfully;

  /// Template upload error message
  ///
  /// In en, this message translates to:
  /// **'Failed to upload template'**
  String get failedToUploadTemplate;

  /// Template update error message
  ///
  /// In en, this message translates to:
  /// **'Failed to update template'**
  String get failedToUpdateTemplate;

  /// Please select image validation message
  ///
  /// In en, this message translates to:
  /// **'Please select an image'**
  String get pleaseSelectImage;

  /// Please enter template name validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter template name'**
  String get pleaseEnterTemplateName;

  /// Please select category validation message
  ///
  /// In en, this message translates to:
  /// **'Please select a category'**
  String get pleaseSelectCategory;

  /// Loading banners message
  ///
  /// In en, this message translates to:
  /// **'Loading banners...'**
  String get loadingBanners;

  /// Loading more banners message
  ///
  /// In en, this message translates to:
  /// **'Loading more banners...'**
  String get loadingMoreBanners;

  /// Error loading banners message
  ///
  /// In en, this message translates to:
  /// **'Error loading banners'**
  String get errorLoadingBanners;

  /// No banners found message
  ///
  /// In en, this message translates to:
  /// **'No banners found'**
  String get noBannersFound;

  /// Select banner instruction
  ///
  /// In en, this message translates to:
  /// **'Select Banner'**
  String get selectBanner;

  /// Customize template title
  ///
  /// In en, this message translates to:
  /// **'Customize Template'**
  String get customizeTemplate;

  /// Image customization section title
  ///
  /// In en, this message translates to:
  /// **'Image Customization'**
  String get imageCustomization;

  /// Banner customization section title
  ///
  /// In en, this message translates to:
  /// **'Banner Customization'**
  String get bannerCustomization;

  /// Text customization section title
  ///
  /// In en, this message translates to:
  /// **'Text Customization'**
  String get textCustomization;

  /// Preview template title
  ///
  /// In en, this message translates to:
  /// **'Preview Template'**
  String get previewTemplate;

  /// Save template button text
  ///
  /// In en, this message translates to:
  /// **'Save Template'**
  String get saveTemplate;

  /// Download template button text
  ///
  /// In en, this message translates to:
  /// **'Download Template'**
  String get downloadTemplate;

  /// Share template button text
  ///
  /// In en, this message translates to:
  /// **'Share Template'**
  String get shareTemplate;

  /// Template save success message
  ///
  /// In en, this message translates to:
  /// **'Template saved successfully'**
  String get templateSavedSuccessfully;

  /// Template download success message
  ///
  /// In en, this message translates to:
  /// **'Template downloaded successfully'**
  String get templateDownloadedSuccessfully;

  /// Template save error message
  ///
  /// In en, this message translates to:
  /// **'Failed to save template'**
  String get failedToSaveTemplate;

  /// Template download error message
  ///
  /// In en, this message translates to:
  /// **'Failed to download template'**
  String get failedToDownloadTemplate;

  /// Processing image message
  ///
  /// In en, this message translates to:
  /// **'Processing image...'**
  String get processingImage;

  /// Generating preview message
  ///
  /// In en, this message translates to:
  /// **'Generating preview...'**
  String get generatingPreview;

  /// Preparing download message
  ///
  /// In en, this message translates to:
  /// **'Preparing download...'**
  String get preparingDownload;

  /// Manage business button text
  ///
  /// In en, this message translates to:
  /// **'Manage Business'**
  String get manageBusiness;

  /// Business configuration description
  ///
  /// In en, this message translates to:
  /// **'Configure your business details, address, and other parameters'**
  String get configureBusinessDetails;

  /// Manage political button text
  ///
  /// In en, this message translates to:
  /// **'Manage Political'**
  String get managePolitical;

  /// Business profile load error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load business profile'**
  String get failedToLoadBusinessProfile;

  /// Political profile load error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load political profile'**
  String get failedToLoadPoliticalProfile;

  /// No business parameters message
  ///
  /// In en, this message translates to:
  /// **'No business parameters available'**
  String get noBusinessParametersAvailable;

  /// Business parameters admin message
  ///
  /// In en, this message translates to:
  /// **'Business parameters will be configured by the administrator'**
  String get businessParametersConfiguredByAdmin;

  /// No political parameters message
  ///
  /// In en, this message translates to:
  /// **'No political parameters available'**
  String get noPoliticalParametersAvailable;

  /// Political parameters admin message
  ///
  /// In en, this message translates to:
  /// **'Political parameters will be configured by the administrator'**
  String get politicalParametersConfiguredByAdmin;

  /// Save business parameters button text
  ///
  /// In en, this message translates to:
  /// **'Save Business Parameters'**
  String get saveBusinessParameters;

  /// Save political parameters button text
  ///
  /// In en, this message translates to:
  /// **'Save Political Parameters'**
  String get savePoliticalParameters;

  /// Business parameters save success message
  ///
  /// In en, this message translates to:
  /// **'Business parameters saved successfully'**
  String get businessParametersSavedSuccessfully;

  /// Political parameters save success message
  ///
  /// In en, this message translates to:
  /// **'Political parameters saved successfully'**
  String get politicalParametersSavedSuccessfully;

  /// Business parameters save error message
  ///
  /// In en, this message translates to:
  /// **'Failed to save business parameters'**
  String get failedToSaveBusinessParameters;

  /// Political parameters save error message
  ///
  /// In en, this message translates to:
  /// **'Failed to save political parameters'**
  String get failedToSavePoliticalParameters;

  /// Business type field label
  ///
  /// In en, this message translates to:
  /// **'Business Type'**
  String get businessType;

  /// Business address field label
  ///
  /// In en, this message translates to:
  /// **'Business Address'**
  String get businessAddress;

  /// Business location field label
  ///
  /// In en, this message translates to:
  /// **'Business Location'**
  String get businessLocation;

  /// Business mobile field label
  ///
  /// In en, this message translates to:
  /// **'Business Mobile'**
  String get businessMobile;

  /// Business website field label
  ///
  /// In en, this message translates to:
  /// **'Business Website'**
  String get businessWebsite;

  /// Business email field label
  ///
  /// In en, this message translates to:
  /// **'Business Email'**
  String get businessEmail;

  /// Select business logo button text
  ///
  /// In en, this message translates to:
  /// **'Select Business Logo'**
  String get selectBusinessLogo;

  /// Political photo description
  ///
  /// In en, this message translates to:
  /// **'Political photo'**
  String get politicalPhoto;

  /// Select political photo button text
  ///
  /// In en, this message translates to:
  /// **'Select Political Photo'**
  String get selectPoliticalPhoto;

  /// Party name field label
  ///
  /// In en, this message translates to:
  /// **'Party Name'**
  String get partyName;

  /// Political position field label
  ///
  /// In en, this message translates to:
  /// **'Political Position'**
  String get politicalPosition;

  /// Constituency field label
  ///
  /// In en, this message translates to:
  /// **'Constituency'**
  String get constituency;

  /// Political experience field label
  ///
  /// In en, this message translates to:
  /// **'Political Experience'**
  String get politicalExperience;

  /// Campaign slogan field label
  ///
  /// In en, this message translates to:
  /// **'Campaign Slogan'**
  String get campaignSlogan;

  /// Political achievements field label
  ///
  /// In en, this message translates to:
  /// **'Political Achievements'**
  String get politicalAchievements;

  /// Required field validation suffix
  ///
  /// In en, this message translates to:
  /// **'is required'**
  String get isRequired;

  /// Number validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid number'**
  String get pleaseEnterValidNumber;

  /// All types filter option
  ///
  /// In en, this message translates to:
  /// **'All Types'**
  String get allTypes;

  /// Remove premium status action
  ///
  /// In en, this message translates to:
  /// **'Remove Premium'**
  String get removePremium;

  /// Make premium status action
  ///
  /// In en, this message translates to:
  /// **'Make Premium'**
  String get makePremium;

  /// Delete user action
  ///
  /// In en, this message translates to:
  /// **'Delete User'**
  String get deleteUser;

  /// User management page title
  ///
  /// In en, this message translates to:
  /// **'User Management'**
  String get userManagement;

  /// Search users placeholder
  ///
  /// In en, this message translates to:
  /// **'Search users...'**
  String get searchUsers;

  /// No users found message
  ///
  /// In en, this message translates to:
  /// **'No users found'**
  String get noUsersFound;

  /// Loading users message
  ///
  /// In en, this message translates to:
  /// **'Loading users...'**
  String get loadingUsers;

  /// Failed to load users error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load users'**
  String get failedToLoadUsers;

  /// User details page title
  ///
  /// In en, this message translates to:
  /// **'User Details'**
  String get userDetails;

  /// Account created label
  ///
  /// In en, this message translates to:
  /// **'Account Created'**
  String get accountCreated;

  /// Last updated label
  ///
  /// In en, this message translates to:
  /// **'Last Updated'**
  String get lastUpdated;

  /// Profile complete status
  ///
  /// In en, this message translates to:
  /// **'Profile Complete'**
  String get profileComplete;

  /// Profile incomplete status
  ///
  /// In en, this message translates to:
  /// **'Profile Incomplete'**
  String get profileIncomplete;

  /// Premium status label
  ///
  /// In en, this message translates to:
  /// **'Premium Status'**
  String get premiumStatus;

  /// Active status
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// Inactive status
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// Subscription plan label
  ///
  /// In en, this message translates to:
  /// **'Subscription Plan'**
  String get subscriptionPlan;

  /// Subscription end date label
  ///
  /// In en, this message translates to:
  /// **'Subscription End Date'**
  String get subscriptionEndDate;

  /// Language preference label
  ///
  /// In en, this message translates to:
  /// **'Language Preference'**
  String get languagePreference;

  /// Total posters count label
  ///
  /// In en, this message translates to:
  /// **'Total Posters'**
  String get totalPosters;

  /// Admin status label
  ///
  /// In en, this message translates to:
  /// **'Admin Status'**
  String get adminStatus;

  /// Is admin status
  ///
  /// In en, this message translates to:
  /// **'Admin'**
  String get isAdmin;

  /// Is not admin status
  ///
  /// In en, this message translates to:
  /// **'Not Admin'**
  String get isNotAdmin;

  /// All users filter option
  ///
  /// In en, this message translates to:
  /// **'All Users'**
  String get allUsers;

  /// Free user status
  ///
  /// In en, this message translates to:
  /// **'Free'**
  String get free;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Message when no users found with current filters
  ///
  /// In en, this message translates to:
  /// **'Try adjusting your search or filters'**
  String get tryAdjustingFilters;

  /// Text when information is not provided
  ///
  /// In en, this message translates to:
  /// **'Not provided'**
  String get notProvided;

  /// Yes response
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No response
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// Created date label
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get created;

  /// Name field label
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Message when user premium status is removed
  ///
  /// In en, this message translates to:
  /// **'User premium status removed'**
  String get userPremiumStatusRemoved;

  /// Message when user is upgraded to premium
  ///
  /// In en, this message translates to:
  /// **'User upgraded to premium'**
  String get userUpgradedToPremium;

  /// Error message when updating user fails
  ///
  /// In en, this message translates to:
  /// **'Error updating user'**
  String get errorUpdatingUser;

  /// Confirmation message for deleting user
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete {userName}? This action cannot be undone.'**
  String areYouSureDeleteUser(String userName);

  /// Success message when user is deleted
  ///
  /// In en, this message translates to:
  /// **'User deleted successfully'**
  String get userDeletedSuccessfully;

  /// Error message when deleting user fails
  ///
  /// In en, this message translates to:
  /// **'Error deleting user'**
  String get errorDeletingUser;

  /// Add button text
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Extend subscription button text
  ///
  /// In en, this message translates to:
  /// **'Extend'**
  String get extend;

  /// Success message when subscription is extended
  ///
  /// In en, this message translates to:
  /// **'Subscription extended by {duration}!'**
  String subscriptionExtended(String duration);

  /// Error message when subscription extension fails
  ///
  /// In en, this message translates to:
  /// **'Failed to extend subscription: {error}'**
  String failedToExtendSubscription(String error);

  /// Error message when subscription purchase fails
  ///
  /// In en, this message translates to:
  /// **'Failed to purchase subscription: {error}'**
  String failedToPurchaseSubscription(String error);

  /// Error message when restoring purchases fails
  ///
  /// In en, this message translates to:
  /// **'Failed to restore purchases'**
  String get failedToRestorePurchases;

  /// Trial subscription plan description
  ///
  /// In en, this message translates to:
  /// **'Try Premium features for 1 week at minimal cost'**
  String get trialPlanDescription;

  /// 3-month subscription plan description
  ///
  /// In en, this message translates to:
  /// **'Perfect for short-term projects'**
  String get threeMonthPlanDescription;

  /// 6-month subscription plan description
  ///
  /// In en, this message translates to:
  /// **'Great value for medium-term use'**
  String get sixMonthPlanDescription;

  /// Annual subscription plan description
  ///
  /// In en, this message translates to:
  /// **'Best value for regular users'**
  String get annualPlanDescription;

  /// All premium features text
  ///
  /// In en, this message translates to:
  /// **'All Premium features'**
  String get allPremiumFeatures;

  /// Auto-renewal description
  ///
  /// In en, this message translates to:
  /// **'Auto-renews at ₹199 for 3 months'**
  String get autoRenewsAt;

  /// Cancel anytime feature
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime'**
  String get cancelAnytime;

  /// Quarterly billing feature
  ///
  /// In en, this message translates to:
  /// **'Quarterly billing'**
  String get quarterlyBilling;

  /// Save 50% feature
  ///
  /// In en, this message translates to:
  /// **'Save 50% compared to 3-month plan'**
  String get save50Percent;

  /// Priority support feature
  ///
  /// In en, this message translates to:
  /// **'Priority support'**
  String get prioritySupport;

  /// Save 58% feature
  ///
  /// In en, this message translates to:
  /// **'Save 58% compared to 3-month plan'**
  String get save58Percent;

  /// Early access feature
  ///
  /// In en, this message translates to:
  /// **'Early access to new features'**
  String get earlyAccessToNewFeatures;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['bn', 'en', 'gu', 'hi', 'mr', 'ta', 'te'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'bn': return AppLocalizationsBn();
    case 'en': return AppLocalizationsEn();
    case 'gu': return AppLocalizationsGu();
    case 'hi': return AppLocalizationsHi();
    case 'mr': return AppLocalizationsMr();
    case 'ta': return AppLocalizationsTa();
    case 'te': return AppLocalizationsTe();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
