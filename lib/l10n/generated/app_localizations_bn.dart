// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Bengali Bangla (`bn`).
class AppLocalizationsBn extends AppLocalizations {
  AppLocalizationsBn([String locale = 'bn']) : super(locale);

  @override
  String get appTitle => 'কুইক পোস্টার্স';

  @override
  String get welcome => 'কুইক পোস্টার্সে আপনাকে স্বাগতম';

  @override
  String get login => 'লগইন';

  @override
  String get logout => 'লগআউট';

  @override
  String get phoneNumber => 'ফোন নম্বর';

  @override
  String get enterPhoneNumber => 'আপনার ফোন নম্বর লিখুন';

  @override
  String get sendOTP => 'OTP পাঠান';

  @override
  String get verifyOTP => 'OTP যাচাই করুন';

  @override
  String get enterOTP => '৬-সংখ্যার OTP লিখুন';

  @override
  String get resendOTP => 'OTP পুনরায় পাঠান';

  @override
  String get home => 'হোম';

  @override
  String get templates => 'টেমপ্লেট';

  @override
  String get categories => 'বিভাগ';

  @override
  String get all => 'সব';

  @override
  String get premium => 'প্রিমিয়াম';

  @override
  String get profile => 'প্রোফাইল';

  @override
  String get settings => 'সেটিংস';

  @override
  String get language => 'ভাষা';

  @override
  String get selectLanguage => 'ভাষা নির্বাচন করুন';

  @override
  String get businessUser => 'ব্যবসা';

  @override
  String get politicalUser => 'রাজনীতিবিদ';

  @override
  String get individualUser => 'ব্যক্তিগত ব্যবহারকারী';

  @override
  String get createPoster => 'পোস্টার তৈরি করুন';

  @override
  String get editTemplate => 'টেমপ্লেট সম্পাদনা করুন';

  @override
  String get saveImage => 'ছবি সংরক্ষণ করুন';

  @override
  String get shareImage => 'ইমেজ শেয়ার করুন';

  @override
  String get premiumFeature => 'প্রিমিয়াম বৈশিষ্ট্য';

  @override
  String get upgradeNow => 'এখনই আপগ্রেড করুন';

  @override
  String get subscriptionPlans => 'সাবস্ক্রিপশন প্ল্যান';

  @override
  String get trialPlan => 'ট্রায়াল প্ল্যান';

  @override
  String get threeMonthPlan => '৩-মাসের প্ল্যান';

  @override
  String get sixMonthPlan => '৬-মাসের প্ল্যান';

  @override
  String get annualPlan => 'বার্ষিক প্ল্যান';

  @override
  String rupees(String amount) {
    return '₹$amount';
  }

  @override
  String get myProfile => 'আমার প্রোফাইল';

  @override
  String get notifications => 'নোটিফিকেশন';

  @override
  String get about => 'সম্পর্কে';

  @override
  String get privacyPolicy => 'গোপনীয়তা নীতি';

  @override
  String get termsOfService => 'সেবার শর্তাবলী';

  @override
  String get refundPolicy => 'ফেরত নীতি';

  @override
  String get aiImageGenerator => 'AI ইমেজ জেনারেটর';

  @override
  String get adminDashboard => 'অ্যাডমিন ড্যাশবোর্ড';

  @override
  String get cancel => 'বাতিল';

  @override
  String get save => 'সংরক্ষণ';

  @override
  String get delete => 'মুছুন';

  @override
  String get edit => 'সম্পাদনা';

  @override
  String get loading => 'লোড হচ্ছে...';

  @override
  String get error => 'ত্রুটি';

  @override
  String get success => 'সফল';

  @override
  String get tryAgain => 'আবার চেষ্টা করুন';

  @override
  String get noInternetConnection => 'ইন্টারনেট সংযোগ নেই';

  @override
  String get somethingWentWrong => 'কিছু ভুল হয়েছে';

  @override
  String get phoneNumberValidation => 'অনুগ্রহ করে একটি বৈধ ১০-সংখ্যার ফোন নম্বর লিখুন';

  @override
  String get otpValidation => 'অনুগ্রহ করে একটি বৈধ ৬-সংখ্যার OTP লিখুন';

  @override
  String get otpSent => 'OTP সফলভাবে পাঠানো হয়েছে';

  @override
  String get otpExpired => 'OTP এর মেয়াদ শেষ হয়ে গেছে। অনুগ্রহ করে নতুন OTP চান';

  @override
  String get invalidOtp => 'ভুল OTP। অনুগ্রহ করে আবার চেষ্টা করুন';

  @override
  String get loginSuccessful => 'লগইন সফল';

  @override
  String get welcomeBack => 'আবার স্বাগতম!';

  @override
  String get getStarted => 'শুরু করুন';

  @override
  String get continueText => 'চালিয়ে যান';

  @override
  String get skip => 'এড়িয়ে যান';

  @override
  String get next => 'পরবর্তী';

  @override
  String get previous => 'পূর্ববর্তী';

  @override
  String get done => 'সম্পন্ন';

  @override
  String get close => 'বন্ধ';

  @override
  String get back => 'ফিরে যান';

  @override
  String get selectUserType => 'ব্যবহারকারীর ধরন নির্বাচন করুন';

  @override
  String get chooseAccountType => 'আপনার অ্যাকাউন্টের ধরন বেছে নিন';

  @override
  String get businessDescription => 'ব্যবসার বিবরণ';

  @override
  String get politicalDescription => 'রাজনৈতিক প্রচারণা এবং নেতাদের জন্য আদর্শ';

  @override
  String get individualDescription => 'ব্যক্তিগত ব্যবহার এবং সোশ্যাল মিডিয়ার জন্য দুর্দান্ত';

  @override
  String get completeProfile => 'আপনার প্রোফাইল সম্পূর্ণ করুন';

  @override
  String get profileCompletionDescription => 'আপনার অভিজ্ঞতা ব্যক্তিগতকরণে আমাদের সাহায্য করুন';

  @override
  String get businessName => 'ব্যবসার নাম';

  @override
  String get enterBusinessName => 'আপনার ব্যবসার নাম লিখুন';

  @override
  String get businessCategory => 'ব্যবসার বিভাগ';

  @override
  String get selectBusinessCategory => 'আপনার ব্যবসার বিভাগ নির্বাচন করুন';

  @override
  String get politicalParty => 'রাজনৈতিক দল';

  @override
  String get selectPoliticalParty => 'আপনার রাজনৈতিক দল নির্বাচন করুন';

  @override
  String get designation => 'পদবী';

  @override
  String get enterDesignation => 'আপনার পদবী লিখুন';

  @override
  String get fullName => 'পূর্ণ নাম';

  @override
  String get enterFullName => 'আপনার পূর্ণ নাম লিখুন';

  @override
  String get profileUpdated => 'প্রোফাইল সফলভাবে আপডেট হয়েছে';

  @override
  String get exploreTemplates => 'টেমপ্লেট অন্বেষণ করুন';

  @override
  String get featuredTemplates => 'বৈশিষ্ট্যযুক্ত টেমপ্লেট';

  @override
  String get recentTemplates => 'সাম্প্রতিক টেমপ্লেট';

  @override
  String get popularTemplates => 'জনপ্রিয় টেমপ্লেট';

  @override
  String get loadingTemplates => 'টেমপ্লেট লোড হচ্ছে...';

  @override
  String get noTemplatesFound => 'কোনো টেমপ্লেট পাওয়া যায়নি';

  @override
  String get searchTemplates => 'টেমপ্লেট খুঁজুন...';

  @override
  String get filterByCategory => 'বিভাগ অনুযায়ী ফিল্টার করুন';

  @override
  String get viewAll => 'সব দেখুন';

  @override
  String get loadMore => 'আরো লোড করুন';

  @override
  String get refreshing => 'রিফ্রেশ হচ্ছে...';

  @override
  String get pullToRefresh => 'রিফ্রেশ করতে টানুন';

  @override
  String get refresh => 'রিফ্রেশ';

  @override
  String get account => 'অ্যাকাউন্ট';

  @override
  String get appSettings => 'অ্যাপ সেটিংস';

  @override
  String get tools => 'সরঞ্জাম';

  @override
  String get premiumSubscription => 'প্রিমিয়াম সাবস্ক্রিপশন';

  @override
  String get youArePremiumUser => 'আপনি একজন প্রিমিয়াম ব্যবহারকারী';

  @override
  String get upgradeToPremium => 'প্রিমিয়ামে আপগ্রেড করুন';

  @override
  String get createImagesWithAI => 'AI দিয়ে ইমেজ তৈরি করুন';

  @override
  String get aboutQuickPosters => 'কুইক পোস্টার সম্পর্কে';

  @override
  String get version => 'সংস্করণ ১.০.০';

  @override
  String get readPrivacyPolicy => 'আমাদের গোপনীয়তা নীতি পড়ুন';

  @override
  String get viewEditProfile => 'আপনার প্রোফাইল দেখুন এবং সম্পাদনা করুন';

  @override
  String get changeAppLanguage => 'অ্যাপের ভাষা পরিবর্তন করুন';

  @override
  String get failedToLoadUserData => 'ব্যবহারকারীর ডেটা লোড করতে ব্যর্থ';

  @override
  String get profileUpdatedSuccessfully => 'প্রোফাইল সফলভাবে আপডেট হয়েছে';

  @override
  String get failedToSaveProfile => 'প্রোফাইল সংরক্ষণ করতে ব্যর্থ';

  @override
  String get manageBusinessProfile => 'ব্যবসায়িক প্রোফাইল পরিচালনা করুন';

  @override
  String get managePoliticalProfile => 'রাজনৈতিক প্রোফাইল পরিচালনা করুন';

  @override
  String get configurePoliticalDetails => 'আপনার রাজনৈতিক বিবরণ, দল এবং অন্যান্য প্যারামিটার কনফিগার করুন';

  @override
  String get businessParameters => 'ব্যবসায়িক প্যারামিটার';

  @override
  String get politicalParameters => 'রাজনৈতিক প্যারামিটার';

  @override
  String get contentRefreshedSuccessfully => 'বিষয়বস্তু সফলভাবে রিফ্রেশ হয়েছে';

  @override
  String get failedToRefreshContent => 'বিষয়বস্তু রিফ্রেশ করতে ব্যর্থ';

  @override
  String get mySubscription => 'আমার সাবস্ক্রিপশন';

  @override
  String get startByAddingBaseImage => 'একটি বেস ইমেজ যোগ করে শুরু করুন';

  @override
  String get backgroundOfPoster => 'এটি আপনার পোস্টারের পটভূমি হবে';

  @override
  String get selectBaseImage => 'বেস ইমেজ নির্বাচন করুন';

  @override
  String get addBaseImage => 'বেস ইমেজ যোগ করুন';

  @override
  String get aboutPageComingSoon => 'সম্পর্কে পেজ শীঘ্রই আসছে';

  @override
  String get languageSettingsComingSoon => 'ভাষা সেটিংস শীঘ্রই আসছে';

  @override
  String get notificationSettingsComingSoon => 'নোটিফিকেশন সেটিংস শীঘ্রই আসছে';

  @override
  String get manageNotificationSettings => 'নোটিফিকেশন সেটিংস পরিচালনা করুন';

  @override
  String get imageShape => 'ছবির আকার';

  @override
  String get businessLogoShape => 'ব্যবসায়িক লোগো আকার';

  @override
  String get profilePhoto => 'প্রোফাইল ফটো';

  @override
  String get businessLogo => 'ব্যবসার লোগো';

  @override
  String get pleaseSelectUserType => 'অনুগ্রহ করে একটি ব্যবহারকারীর ধরন নির্বাচন করুন';

  @override
  String get failedToLoadUserTypes => 'ব্যবহারকারীর ধরন লোড করতে ব্যর্থ';

  @override
  String get failedToSaveUserType => 'ব্যবহারকারীর ধরন সংরক্ষণ করতে ব্যর্থ';

  @override
  String get profileCompletedSuccessfully => 'প্রোফাইল সফলভাবে সম্পন্ন হয়েছে';

  @override
  String get viewEditProfileInfo => 'আপনার প্রোফাইল তথ্য দেখুন এবং সম্পাদনা করুন';

  @override
  String get chooseImageSource => 'ইমেজ উৎস নির্বাচন করুন';

  @override
  String get customImage => 'কাস্টম ছবি';

  @override
  String get autoSelect => 'স্বয়ংক্রিয় নির্বাচন';

  @override
  String get imageSource => 'ইমেজ উৎস';

  @override
  String get shapeOptions => 'আকার বিকল্প';

  @override
  String get none => 'কোনটি নয়';

  @override
  String get circle => 'বৃত্ত';

  @override
  String get rectangle => 'আয়তক্ষেত্র';

  @override
  String get roundedRectangle => 'গোলাকার আয়তক্ষেত্র';

  @override
  String get diamond => 'হীরক';

  @override
  String get hexagon => 'ষড়ভুজ';

  @override
  String get quickPosters => 'কুইক পোস্টার্স';

  @override
  String get accountActions => 'অ্যাকাউন্ট ক্রিয়া';

  @override
  String get signOutFromAccount => 'আপনার অ্যাকাউন্ট থেকে সাইন আউট করুন';

  @override
  String get changingLanguage => 'ভাষা পরিবর্তন করা হচ্ছে...';

  @override
  String get phoneNumberLabel => 'ফোন নম্বর';

  @override
  String get enterYourPhoneNumber => 'আপনার ফোন নম্বর লিখুন';

  @override
  String get userType => 'ব্যবহারকারীর ধরন';

  @override
  String get saveToGallery => 'গ্যালারিতে সংরক্ষণ করুন';

  @override
  String get share => 'শেয়ার';

  @override
  String get whatsApp => 'হোয়াটসঅ্যাপ';

  @override
  String get invalidPhoneNumber => 'ফোন নম্বরের বিন্যাস ভুল। অনুগ্রহ করে একটি বৈধ ফোন নম্বর লিখুন।';

  @override
  String get quotaExceeded => 'SMS কোটা শেষ হয়ে গেছে। অনুগ্রহ করে পরে আবার চেষ্টা করুন।';

  @override
  String get userDisabled => 'এই ব্যবহারকারী নিষ্ক্রিয় করা হয়েছে। অনুগ্রহ করে সহায়তার সাথে যোগাযোগ করুন।';

  @override
  String get operationNotAllowed => 'ফোন প্রমাণীকরণ সক্ষম নয়। অনুগ্রহ করে সহায়তার সাথে যোগাযোগ করুন।';

  @override
  String get captchaCheckFailed => 'reCAPTCHA যাচাইকরণ ব্যর্থ হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।';

  @override
  String get missingClientIdentifier => 'অ্যাপ যাচাইকরণ প্রক্রিয়া ব্যর্থ হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন বা সহায়তার সাথে যোগাযোগ করুন।';

  @override
  String get tooManyRequests => 'এই ডিভাইস থেকে অনেক অনুরোধ। অস্বাভাবিক কার্যকলাপের কারণে আমরা এই ডিভাইস থেকে সমস্ত অনুরোধ সাময়িকভাবে ব্লক করেছি। অনুগ্রহ করে কিছু সময় পরে আবার চেষ্টা করুন (সাধারণত কয়েক ঘন্টা)।';

  @override
  String get verificationFailed => 'যাচাইকরণ ব্যর্থ হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।';

  @override
  String get failedToGetUserData => 'ব্যবহারকারীর ডেটা পেতে ব্যর্থ';

  @override
  String get userNotFound => 'ব্যবহারকারী পাওয়া যায়নি';

  @override
  String get politician => 'রাজনীতিবিদ';

  @override
  String get businessman => 'ব্যবসায়ী';

  @override
  String get regularUser => 'নিয়মিত';

  @override
  String get forPoliticalCampaigns => 'রাজনৈতিক প্রচারণা এবং প্রচারের জন্য';

  @override
  String get forBusinessPromotions => 'ব্যবসায়িক প্রচার এবং বিজ্ঞাপনের জন্য';

  @override
  String get forPersonalUse => 'ব্যক্তিগত ব্যবহার এবং সাধারণ উদ্দেশ্যে';

  @override
  String get developmentLogin => 'ডেভেলপমেন্ট লগইন';

  @override
  String get developmentMode => 'ডেভেলপমেন্ট মোড';

  @override
  String get loggedInAsDeveloper => 'আপনি একজন ডেভেলপার হিসেবে লগইন করেছেন।\nFirebase ফোন প্রমাণীকরণ অবরুদ্ধ থাকাকালীন এটি একটি অস্থায়ী মোড।';

  @override
  String get noActiveSubscription => 'কোন সক্রিয় সাবস্ক্রিপশন নেই';

  @override
  String expiresInDays(int days) {
    return '$days দিনে মেয়াদ শেষ';
  }

  @override
  String activeUntil(String date) {
    return '$date পর্যন্ত সক্রিয়';
  }

  @override
  String get downloadImage => 'ইমেজ ডাউনলোড করুন';

  @override
  String get shareOnWhatsApp => 'হোয়াটসঅ্যাপে শেয়ার করুন';

  @override
  String get customImageSelected => 'কাস্টম ছবি নির্বাচিত!';

  @override
  String errorSelectingImage(String error) {
    return 'ছবি নির্বাচনে ত্রুটি: $error';
  }

  @override
  String get imageAddedSuccessfully => 'ইমেজ সফলভাবে যোগ করা হয়েছে!';

  @override
  String errorAddingImage(String error) {
    return 'ইমেজ যোগ করতে ত্রুটি: $error';
  }

  @override
  String noImageAvailable(String source) {
    return 'কোন $source ইমেজ উপলব্ধ নেই';
  }

  @override
  String get emailAddress => 'ইমেইল ঠিকানা';

  @override
  String get enterEmailAddress => 'আপনার ইমেইল ঠিকানা লিখুন';

  @override
  String get optional => 'ঐচ্ছিক';

  @override
  String get selectProfileImage => 'প্রোফাইল ইমেজ নির্বাচন করুন';

  @override
  String get camera => 'ক্যামেরা';

  @override
  String get gallery => 'গ্যালারি';

  @override
  String get removeImage => 'ইমেজ সরান';

  @override
  String get saveProfile => 'প্রোফাইল সংরক্ষণ করুন';

  @override
  String get updateProfile => 'প্রোফাইল আপডেট করুন';

  @override
  String get profileImageUpdated => 'প্রোফাইল ইমেজ সফলভাবে আপডেট হয়েছে';

  @override
  String get failedToUpdateProfileImage => 'প্রোফাইল ইমেজ আপডেট করতে ব্যর্থ';

  @override
  String get pleaseEnterValidEmail => 'অনুগ্রহ করে একটি বৈধ ইমেইল ঠিকানা লিখুন';

  @override
  String get pleaseEnterName => 'অনুগ্রহ করে আপনার নাম লিখুন';

  @override
  String get nameTooShort => 'নাম কমপক্ষে ২ অক্ষরের হতে হবে';

  @override
  String get invalidEmailFormat => 'অনুগ্রহ করে একটি বৈধ ইমেইল বিন্যাস লিখুন';

  @override
  String get business => 'ব্যবসা';

  @override
  String get event => 'ইভেন্ট';

  @override
  String get festival => 'উৎসব';

  @override
  String get party => 'পার্টি';

  @override
  String get education => 'শিক্ষা';

  @override
  String get health => 'স্বাস্থ্য';

  @override
  String get technology => 'প্রযুক্তি';

  @override
  String get food => 'খাবার';

  @override
  String get travel => 'ভ্রমণ';

  @override
  String get sports => 'খেলাধুলা';

  @override
  String get fashion => 'ফ্যাশন';

  @override
  String get realEstate => 'রিয়েল এস্টেট';

  @override
  String get flyers => 'ফ্লায়ার';

  @override
  String get brochures => 'ব্রোশার';

  @override
  String get businessCards => 'বিজনেস কার্ড';

  @override
  String get presentations => 'উপস্থাপনা';

  @override
  String get logos => 'লোগো';

  @override
  String get invitations => 'আমন্ত্রণ';

  @override
  String get tickets => 'টিকিট';

  @override
  String get programs => 'প্রোগ্রাম';

  @override
  String get banners => 'ব্যানার';

  @override
  String get posters => 'পোস্টার';

  @override
  String get celebration => 'উদযাপন';

  @override
  String get religious => 'ধর্মীয়';

  @override
  String get cultural => 'সাংস্কৃতিক';

  @override
  String get seasonal => 'মৌসুমী';

  @override
  String get birthday => 'জন্মদিন';

  @override
  String get wedding => 'বিবাহ';

  @override
  String get anniversary => 'বার্ষিকী';

  @override
  String get graduation => 'স্নাতক';

  @override
  String get certificates => 'সার্টিফিকেট';

  @override
  String get worksheets => 'ওয়ার্কশীট';

  @override
  String get awareness => 'সচেতনতা';

  @override
  String get medical => 'চিকিৎসা';

  @override
  String get fitness => 'ফিটনেস';

  @override
  String get nutrition => 'পুষ্টি';

  @override
  String get appPromotion => 'অ্যাপ প্রমোশন';

  @override
  String get software => 'সফটওয়্যার';

  @override
  String get digitalServices => 'ডিজিটাল সেবা';

  @override
  String get menu => 'মেনু';

  @override
  String get recipe => 'রেসিপি';

  @override
  String get restaurant => 'রেস্তোরাঁ';

  @override
  String get catering => 'ক্যাটারিং';

  @override
  String get tourism => 'পর্যটন';

  @override
  String get hotels => 'হোটেল';

  @override
  String get transportation => 'পরিবহন';

  @override
  String get adventure => 'অ্যাডভেঞ্চার';

  @override
  String get team => 'দল';

  @override
  String get tournament => 'টুর্নামেন্ট';

  @override
  String get equipment => 'সরঞ্জাম';

  @override
  String get clothing => 'পোশাক';

  @override
  String get accessories => 'আনুষাঙ্গিক';

  @override
  String get beauty => 'সৌন্দর্য';

  @override
  String get style => 'স্টাইল';

  @override
  String get property => 'সম্পত্তি';

  @override
  String get rental => 'ভাড়া';

  @override
  String get commercial => 'বাণিজ্যিক';

  @override
  String get residential => 'আবাসিক';

  @override
  String get noShape => 'কোন আকার নেই';

  @override
  String get rounded => 'গোলাকার';

  @override
  String get loadMoreTemplates => 'আরো টেমপ্লেট লোড করুন';

  @override
  String get horizontal => 'অনুভূমিক';

  @override
  String get vertical => 'উল্লম্ব';

  @override
  String get square => 'বর্গাকার';

  @override
  String get smallBusiness => 'ছোট ব্যবসা';

  @override
  String get eventOrganizers => 'ইভেন্ট আয়োজক';

  @override
  String get educators => 'শিক্ষাবিদ';

  @override
  String get marketers => 'বিপণনকারী';

  @override
  String get students => 'ছাত্র';

  @override
  String get professionals => 'পেশাদার';

  @override
  String get generalPublic => 'সাধারণ জনগণ';

  @override
  String get modern => 'আধুনিক';

  @override
  String get minimalist => 'ন্যূনতম';

  @override
  String get retro => 'রেট্রো';

  @override
  String get elegant => 'মার্জিত';

  @override
  String get creative => 'সৃজনশীল';

  @override
  String get professional => 'পেশাদার';

  @override
  String get playful => 'খেলাধুলাপূর্ণ';

  @override
  String get print => 'প্রিন্ট';

  @override
  String get socialMedia => 'সোশ্যাল মিডিয়া';

  @override
  String get onlineAdvertising => 'অনলাইন বিজ্ঞাপন';

  @override
  String get email => 'ইমেইল';

  @override
  String get web => 'ওয়েব';

  @override
  String get high => 'উচ্চ';

  @override
  String get medium => 'মাঝারি';

  @override
  String get low => 'নিম্ন';

  @override
  String get freeForPersonalUse => 'ব্যক্তিগত ব্যবহারের জন্য বিনামূল্যে';

  @override
  String get commercialUseAllowed => 'বাণিজ্যিক ব্যবহারের অনুমতি';

  @override
  String get premiumLicenseRequired => 'প্রিমিয়াম লাইসেন্স প্রয়োজন';

  @override
  String get templateName => 'টেমপ্লেট নাম';

  @override
  String get templateDescription => 'টেমপ্লেট বিবরণ';

  @override
  String get creator => 'নির্মাতা';

  @override
  String get dimensions => 'মাত্রা';

  @override
  String get tags => 'ট্যাগ';

  @override
  String get colors => 'রং';

  @override
  String get fontStyles => 'ফন্ট স্টাইল';

  @override
  String get fileFormats => 'ফাইল ফরম্যাট';

  @override
  String get category => 'বিভাগ';

  @override
  String get subCategory => 'উপ বিভাগ';

  @override
  String get layoutType => 'লেআউট ধরন';

  @override
  String get targetAudience => 'লক্ষ্য দর্শক';

  @override
  String get designStyle => 'ডিজাইন স্টাইল';

  @override
  String get usageType => 'ব্যবহারের ধরন';

  @override
  String get resolution => 'রেজোলিউশন';

  @override
  String get licenseType => 'লাইসেন্স ধরন';

  @override
  String get isPremium => 'প্রিমিয়াম';

  @override
  String get isActive => 'সক্রিয়';

  @override
  String get selectImage => 'ছবি নির্বাচন করুন';

  @override
  String get uploadTemplate => 'টেমপ্লেট আপলোড করুন';

  @override
  String get updateTemplate => 'টেমপ্লেট আপডেট করুন';

  @override
  String get uploading => 'আপলোড হচ্ছে...';

  @override
  String get templateUploadedSuccessfully => 'টেমপ্লেট সফলভাবে আপলোড হয়েছে';

  @override
  String get templateUpdatedSuccessfully => 'টেমপ্লেট সফলভাবে আপডেট হয়েছে';

  @override
  String get failedToUploadTemplate => 'টেমপ্লেট আপলোড করতে ব্যর্থ';

  @override
  String get failedToUpdateTemplate => 'টেমপ্লেট আপডেট করতে ব্যর্থ';

  @override
  String get pleaseSelectImage => 'অনুগ্রহ করে একটি ছবি নির্বাচন করুন';

  @override
  String get pleaseEnterTemplateName => 'অনুগ্রহ করে টেমপ্লেট নাম লিখুন';

  @override
  String get pleaseSelectCategory => 'অনুগ্রহ করে একটি বিভাগ নির্বাচন করুন';

  @override
  String get loadingBanners => 'ব্যানার লোড হচ্ছে...';

  @override
  String get loadingMoreBanners => 'আরো ব্যানার লোড হচ্ছে...';

  @override
  String get errorLoadingBanners => 'ব্যানার লোড করতে ত্রুটি';

  @override
  String get noBannersFound => 'কোন ব্যানার পাওয়া যায়নি';

  @override
  String get selectBanner => 'ব্যানার নির্বাচন করুন';

  @override
  String get customizeTemplate => 'টেমপ্লেট কাস্টমাইজ করুন';

  @override
  String get imageCustomization => 'ছবি কাস্টমাইজেশন';

  @override
  String get bannerCustomization => 'ব্যানার কাস্টমাইজেশন';

  @override
  String get textCustomization => 'টেক্সট কাস্টমাইজেশন';

  @override
  String get previewTemplate => 'টেমপ্লেট প্রিভিউ';

  @override
  String get saveTemplate => 'টেমপ্লেট সংরক্ষণ করুন';

  @override
  String get downloadTemplate => 'টেমপ্লেট ডাউনলোড করুন';

  @override
  String get shareTemplate => 'টেমপ্লেট শেয়ার করুন';

  @override
  String get templateSavedSuccessfully => 'টেমপ্লেট সফলভাবে সংরক্ষিত হয়েছে';

  @override
  String get templateDownloadedSuccessfully => 'টেমপ্লেট সফলভাবে ডাউনলোড হয়েছে';

  @override
  String get failedToSaveTemplate => 'টেমপ্লেট সংরক্ষণ করতে ব্যর্থ';

  @override
  String get failedToDownloadTemplate => 'টেমপ্লেট ডাউনলোড করতে ব্যর্থ';

  @override
  String get processingImage => 'ছবি প্রক্রিয়াকরণ হচ্ছে...';

  @override
  String get generatingPreview => 'প্রিভিউ তৈরি হচ্ছে...';

  @override
  String get preparingDownload => 'ডাউনলোড প্রস্তুত হচ্ছে...';

  @override
  String get manageBusiness => 'ব্যবসা পরিচালনা';

  @override
  String get configureBusinessDetails => 'আপনার ব্যবসার বিবরণ, ঠিকানা এবং অন্যান্য প্যারামিটার কনফিগার করুন';

  @override
  String get managePolitical => 'রাজনৈতিক পরিচালনা';

  @override
  String get failedToLoadBusinessProfile => 'ব্যবসায়িক প্রোফাইল লোড করতে ব্যর্থ';

  @override
  String get failedToLoadPoliticalProfile => 'রাজনৈতিক প্রোফাইল লোড করতে ব্যর্থ';

  @override
  String get noBusinessParametersAvailable => 'কোন ব্যবসায়িক প্যারামিটার উপলব্ধ নেই';

  @override
  String get businessParametersConfiguredByAdmin => 'ব্যবসায়িক প্যারামিটার প্রশাসক দ্বারা কনফিগার করা হবে';

  @override
  String get noPoliticalParametersAvailable => 'কোন রাজনৈতিক প্যারামিটার উপলব্ধ নেই';

  @override
  String get politicalParametersConfiguredByAdmin => 'রাজনৈতিক প্যারামিটার প্রশাসক দ্বারা কনফিগার করা হবে';

  @override
  String get saveBusinessParameters => 'ব্যবসায়িক প্যারামিটার সংরক্ষণ করুন';

  @override
  String get savePoliticalParameters => 'রাজনৈতিক প্যারামিটার সংরক্ষণ করুন';

  @override
  String get businessParametersSavedSuccessfully => 'ব্যবসায়িক প্যারামিটার সফলভাবে সংরক্ষিত হয়েছে';

  @override
  String get politicalParametersSavedSuccessfully => 'রাজনৈতিক প্যারামিটার সফলভাবে সংরক্ষিত হয়েছে';

  @override
  String get failedToSaveBusinessParameters => 'ব্যবসায়িক প্যারামিটার সংরক্ষণ করতে ব্যর্থ';

  @override
  String get failedToSavePoliticalParameters => 'রাজনৈতিক প্যারামিটার সংরক্ষণ করতে ব্যর্থ';

  @override
  String get businessType => 'ব্যবসার ধরন';

  @override
  String get businessAddress => 'ব্যবসার ঠিকানা';

  @override
  String get businessLocation => 'ব্যবসার অবস্থান';

  @override
  String get businessMobile => 'ব্যবসার মোবাইল';

  @override
  String get businessWebsite => 'ব্যবসার ওয়েবসাইট';

  @override
  String get businessEmail => 'ব্যবসার ইমেইল';

  @override
  String get selectBusinessLogo => 'ব্যবসার লোগো নির্বাচন করুন';

  @override
  String get politicalPhoto => 'রাজনৈতিক ছবি';

  @override
  String get selectPoliticalPhoto => 'রাজনৈতিক ছবি নির্বাচন করুন';

  @override
  String get partyName => 'দলের নাম';

  @override
  String get politicalPosition => 'রাজনৈতিক পদ';

  @override
  String get constituency => 'নির্বাচনী এলাকা';

  @override
  String get politicalExperience => 'রাজনৈতিক অভিজ্ঞতা';

  @override
  String get campaignSlogan => 'প্রচারণার স্লোগান';

  @override
  String get politicalAchievements => 'রাজনৈতিক অর্জন';

  @override
  String get isRequired => 'প্রয়োজনীয়';

  @override
  String get pleaseEnterValidNumber => 'অনুগ্রহ করে একটি বৈধ সংখ্যা লিখুন';

  @override
  String get allTypes => 'সব ধরনের';

  @override
  String get removePremium => 'প্রিমিয়াম সরান';

  @override
  String get makePremium => 'প্রিমিয়াম করুন';

  @override
  String get deleteUser => 'ব্যবহারকারী মুছুন';

  @override
  String get userManagement => 'ব্যবহারকারী ব্যবস্থাপনা';

  @override
  String get searchUsers => 'ব্যবহারকারী খুঁজুন...';

  @override
  String get noUsersFound => 'কোন ব্যবহারকারী পাওয়া যায়নি';

  @override
  String get loadingUsers => 'ব্যবহারকারী লোড হচ্ছে...';

  @override
  String get failedToLoadUsers => 'ব্যবহারকারী লোড করতে ব্যর্থ';

  @override
  String get userDetails => 'ব্যবহারকারীর বিবরণ';

  @override
  String get accountCreated => 'অ্যাকাউন্ট তৈরি হয়েছে';

  @override
  String get lastUpdated => 'সর্বশেষ আপডেট';

  @override
  String get profileComplete => 'প্রোফাইল সম্পূর্ণ';

  @override
  String get profileIncomplete => 'প্রোফাইল অসম্পূর্ণ';

  @override
  String get premiumStatus => 'প্রিমিয়াম অবস্থা';

  @override
  String get active => 'সক্রিয়';

  @override
  String get inactive => 'নিষ্ক্রিয়';

  @override
  String get subscriptionPlan => 'সাবস্ক্রিপশন পরিকল্পনা';

  @override
  String get subscriptionEndDate => 'সাবস্ক্রিপশন শেষের তারিখ';

  @override
  String get languagePreference => 'ভাষার পছন্দ';

  @override
  String get totalPosters => 'মোট পোস্টার';

  @override
  String get adminStatus => 'প্রশাসক অবস্থা';

  @override
  String get isAdmin => 'প্রশাসক';

  @override
  String get isNotAdmin => 'প্রশাসক নয়';

  @override
  String get allUsers => 'সব ব্যবহারকারী';

  @override
  String get free => 'বিনামূল্যে';

  @override
  String get retry => 'আবার চেষ্টা করুন';

  @override
  String get tryAdjustingFilters => 'আপনার অনুসন্ধান বা ফিল্টার সামঞ্জস্য করার চেষ্টা করুন';

  @override
  String get notProvided => 'প্রদান করা হয়নি';

  @override
  String get yes => 'হ্যাঁ';

  @override
  String get no => 'না';

  @override
  String get created => 'তৈরি';

  @override
  String get name => 'নাম';

  @override
  String get userPremiumStatusRemoved => 'ব্যবহারকারীর প্রিমিয়াম স্থিতি সরানো হয়েছে';

  @override
  String get userUpgradedToPremium => 'ব্যবহারকারী প্রিমিয়ামে আপগ্রেড হয়েছে';

  @override
  String get errorUpdatingUser => 'ব্যবহারকারী আপডেট করতে ত্রুটি';

  @override
  String areYouSureDeleteUser(String userName) {
    return 'আপনি কি সত্যিই $userName কে মুছে ফেলতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।';
  }

  @override
  String get userDeletedSuccessfully => 'ব্যবহারকারী সফলভাবে মুছে ফেলা হয়েছে';

  @override
  String get errorDeletingUser => 'ব্যবহারকারী মুছে ফেলতে ত্রুটি';

  @override
  String get add => 'যোগ করুন';

  @override
  String get extend => 'বাড়ান';

  @override
  String subscriptionExtended(String duration) {
    return 'সাবস্ক্রিপশন $duration বাড়ানো হয়েছে!';
  }

  @override
  String failedToExtendSubscription(String error) {
    return 'সাবস্ক্রিপশন বাড়াতে ব্যর্থ: $error';
  }

  @override
  String failedToPurchaseSubscription(String error) {
    return 'সাবস্ক্রিপশন কিনতে ব্যর্থ: $error';
  }

  @override
  String get failedToRestorePurchases => 'ক্রয় পুনরুদ্ধার করতে ব্যর্থ';

  @override
  String get trialPlanDescription => 'ন্যূনতম খরচে ১ সপ্তাহের জন্য প্রিমিয়াম বৈশিষ্ট্য ব্যবহার করে দেখুন';

  @override
  String get threeMonthPlanDescription => 'স্বল্পমেয়াদী প্রকল্পের জন্য উপযুক্ত';

  @override
  String get sixMonthPlanDescription => 'মধ্যমেয়াদী ব্যবহারের জন্য দুর্দান্ত মূল্য';

  @override
  String get annualPlanDescription => 'নিয়মিত ব্যবহারকারীদের জন্য সেরা মূল্য';

  @override
  String get allPremiumFeatures => 'সমস্ত প্রিমিয়াম বৈশিষ্ট্য';

  @override
  String get autoRenewsAt => '৩ মাসের জন্য ₹১৯৯ এ অটো-রিনিউ';

  @override
  String get cancelAnytime => 'যেকোনো সময় বাতিল করুন';

  @override
  String get quarterlyBilling => 'ত্রৈমাসিক বিলিং';

  @override
  String get save50Percent => '৩-মাসের প্ল্যানের তুলনায় ৫০% সাশ্রয়';

  @override
  String get prioritySupport => 'অগ্রাধিকার সহায়তা';

  @override
  String get save58Percent => '৩-মাসের প্ল্যানের তুলনায় ৫৮% সাশ্রয়';

  @override
  String get earlyAccessToNewFeatures => 'নতুন বৈশিষ্ট্যে প্রাথমিক অ্যাক্সেস';
}
