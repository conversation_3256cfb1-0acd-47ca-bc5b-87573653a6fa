// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Marathi (`mr`).
class AppLocalizationsMr extends AppLocalizations {
  AppLocalizationsMr([String locale = 'mr']) : super(locale);

  @override
  String get appTitle => 'क्विक पोस्टर्स';

  @override
  String get welcome => 'क्विक पोस्टर्स मध्ये आपले स्वागत आहे';

  @override
  String get login => 'लॉगिन';

  @override
  String get logout => 'लॉगआउट';

  @override
  String get phoneNumber => 'फोन नंबर';

  @override
  String get enterPhoneNumber => 'तुमचा फोन नंबर टाका';

  @override
  String get sendOTP => 'OTP पाठवा';

  @override
  String get verifyOTP => 'OTP सत्यापित करा';

  @override
  String get enterOTP => '6-अंकी OTP टाका';

  @override
  String get resendOTP => 'OTP पुन्हा पाठवा';

  @override
  String get home => 'होम';

  @override
  String get templates => 'टेम्प्लेट्स';

  @override
  String get categories => 'श्रेणी';

  @override
  String get all => 'सर्व';

  @override
  String get premium => 'प्रीमियम';

  @override
  String get profile => 'प्रोफाइल';

  @override
  String get settings => 'सेटिंग्ज';

  @override
  String get language => 'भाषा';

  @override
  String get selectLanguage => 'भाषा निवडा';

  @override
  String get businessUser => 'व्यापार';

  @override
  String get politicalUser => 'राजकारणी';

  @override
  String get individualUser => 'वैयक्तिक वापरकर्ता';

  @override
  String get createPoster => 'पोस्टर तयार करा';

  @override
  String get editTemplate => 'टेम्प्लेट संपादित करा';

  @override
  String get saveImage => 'प्रतिमा जतन करा';

  @override
  String get shareImage => 'इमेज शेअर करा';

  @override
  String get premiumFeature => 'प्रीमियम वैशिष्ट्य';

  @override
  String get upgradeNow => 'आता अपग्रेड करा';

  @override
  String get subscriptionPlans => 'सबस्क्रिप्शन प्लान';

  @override
  String get trialPlan => 'ट्रायल प्लान';

  @override
  String get threeMonthPlan => '3-महिन्यांचा प्लान';

  @override
  String get sixMonthPlan => '6-महिन्यांचा प्लान';

  @override
  String get annualPlan => 'वार्षिक प्लान';

  @override
  String rupees(String amount) {
    return '₹$amount';
  }

  @override
  String get myProfile => 'माझी प्रोफाइल';

  @override
  String get notifications => 'नोटिफिकेशन';

  @override
  String get about => 'बद्दल';

  @override
  String get privacyPolicy => 'गोपनीयता धोरण';

  @override
  String get termsOfService => 'सेवेच्या अटी';

  @override
  String get refundPolicy => 'परतावा धोरण';

  @override
  String get aiImageGenerator => 'AI इमेज जेनरेटर';

  @override
  String get adminDashboard => 'अॅडमिन डॅशबोर्ड';

  @override
  String get cancel => 'रद्द करा';

  @override
  String get save => 'जतन करा';

  @override
  String get delete => 'हटवा';

  @override
  String get edit => 'संपादित करा';

  @override
  String get loading => 'लोड होत आहे...';

  @override
  String get error => 'त्रुटी';

  @override
  String get success => 'यश';

  @override
  String get tryAgain => 'पुन्हा प्रयत्न करा';

  @override
  String get noInternetConnection => 'इंटरनेट कनेक्शन नाही';

  @override
  String get somethingWentWrong => 'काहीतरी चूक झाली';

  @override
  String get phoneNumberValidation => 'कृपया वैध 10-अंकी फोन नंबर टाका';

  @override
  String get otpValidation => 'कृपया वैध 6-अंकी OTP टाका';

  @override
  String get otpSent => 'OTP यशस्वीरित्या पाठवला';

  @override
  String get otpExpired => 'OTP ची मुदत संपली आहे. कृपया नवीन OTP मागा';

  @override
  String get invalidOtp => 'चुकीचा OTP. कृपया पुन्हा प्रयत्न करा';

  @override
  String get loginSuccessful => 'लॉगिन यशस्वी';

  @override
  String get welcomeBack => 'परत स्वागत आहे!';

  @override
  String get getStarted => 'सुरुवात करा';

  @override
  String get continueText => 'सुरू ठेवा';

  @override
  String get skip => 'वगळा';

  @override
  String get next => 'पुढे';

  @override
  String get previous => 'मागे';

  @override
  String get done => 'पूर्ण';

  @override
  String get close => 'बंद करा';

  @override
  String get back => 'परत';

  @override
  String get selectUserType => 'वापरकर्ता प्रकार निवडा';

  @override
  String get chooseAccountType => 'तुमचा खाता प्रकार निवडा';

  @override
  String get businessDescription => 'व्यापार वर्णन';

  @override
  String get politicalDescription => 'राजकीय मोहिमा आणि नेत्यांसाठी आदर्श';

  @override
  String get individualDescription => 'वैयक्तिक वापर आणि सोशल मीडियासाठी उत्कृष्ट';

  @override
  String get completeProfile => 'तुमची प्रोफाइल पूर्ण करा';

  @override
  String get profileCompletionDescription => 'तुमचा अनुभव वैयक्तिक करण्यात आम्हाला मदत करा';

  @override
  String get businessName => 'व्यापार नाव';

  @override
  String get enterBusinessName => 'तुमच्या व्यापाराचे नाव टाका';

  @override
  String get businessCategory => 'व्यापार श्रेणी';

  @override
  String get selectBusinessCategory => 'तुमची व्यापार श्रेणी निवडा';

  @override
  String get politicalParty => 'राजकीय पक्ष';

  @override
  String get selectPoliticalParty => 'तुमचा राजकीय पक्ष निवडा';

  @override
  String get designation => 'पदनाम';

  @override
  String get enterDesignation => 'तुमचे पदनाम टाका';

  @override
  String get fullName => 'पूर्ण नाव';

  @override
  String get enterFullName => 'तुमचे पूर्ण नाव टाका';

  @override
  String get profileUpdated => 'प्रोफाइल यशस्वीरित्या अपडेट केली';

  @override
  String get exploreTemplates => 'टेम्प्लेट्स एक्सप्लोर करा';

  @override
  String get featuredTemplates => 'वैशिष्ट्यीकृत टेम्प्लेट्स';

  @override
  String get recentTemplates => 'अलीकडील टेम्प्लेट्स';

  @override
  String get popularTemplates => 'लोकप्रिय टेम्प्लेट्स';

  @override
  String get loadingTemplates => 'टेम्प्लेट्स लोड होत आहेत...';

  @override
  String get noTemplatesFound => 'कोणतेही टेम्प्लेट्स सापडले नाहीत';

  @override
  String get searchTemplates => 'टेम्प्लेट्स शोधा...';

  @override
  String get filterByCategory => 'श्रेणीनुसार फिल्टर करा';

  @override
  String get viewAll => 'सर्व पहा';

  @override
  String get loadMore => 'अधिक लोड करा';

  @override
  String get refreshing => 'रिफ्रेश होत आहे...';

  @override
  String get pullToRefresh => 'रिफ्रेश करण्यासाठी खेचा';

  @override
  String get refresh => 'रीफ्रेश';

  @override
  String get account => 'खाते';

  @override
  String get appSettings => 'अॅप सेटिंग्ज';

  @override
  String get tools => 'साधने';

  @override
  String get premiumSubscription => 'प्रीमियम सब्सक्रिप्शन';

  @override
  String get youArePremiumUser => 'तुम्ही प्रीमियम वापरकर्ता आहात';

  @override
  String get upgradeToPremium => 'प्रीमियममध्ये अपग्रेड करा';

  @override
  String get createImagesWithAI => 'AI सह इमेज तयार करा';

  @override
  String get aboutQuickPosters => 'क्विक पोस्टर्स बद्दल';

  @override
  String get version => 'आवृत्ती 1.0.0';

  @override
  String get readPrivacyPolicy => 'आमचे गोपनीयता धोरण वाचा';

  @override
  String get viewEditProfile => 'तुमची प्रोफाइल पहा आणि संपादित करा';

  @override
  String get changeAppLanguage => 'अॅपची भाषा बदला';

  @override
  String get failedToLoadUserData => 'वापरकर्ता डेटा लोड करण्यात अयशस्वी';

  @override
  String get profileUpdatedSuccessfully => 'प्रोफाइल यशस्वीरित्या अपडेट केली';

  @override
  String get failedToSaveProfile => 'प्रोफाइल जतन करण्यात अयशस्वी';

  @override
  String get manageBusinessProfile => 'व्यापार प्रोफाइल व्यवस्थापित करा';

  @override
  String get managePoliticalProfile => 'राजकीय प्रोफाइल व्यवस्थापित करा';

  @override
  String get configurePoliticalDetails => 'तुमचे राजकीय तपशील, पक्ष आणि इतर पॅरामीटर कॉन्फिगर करा';

  @override
  String get businessParameters => 'व्यापारिक पॅरामीटर';

  @override
  String get politicalParameters => 'राजकीय पॅरामीटर';

  @override
  String get contentRefreshedSuccessfully => 'सामग्री यशस्वीरित्या रिफ्रेश केली';

  @override
  String get failedToRefreshContent => 'सामग्री रिफ्रेश करण्यात अयशस्वी';

  @override
  String get mySubscription => 'माझे सब्सक्रिप्शन';

  @override
  String get startByAddingBaseImage => 'बेस इमेज जोडून सुरुवात करा';

  @override
  String get backgroundOfPoster => 'हे तुमच्या पोस्टरची पार्श्वभूमी असेल';

  @override
  String get selectBaseImage => 'बेस इमेज निवडा';

  @override
  String get addBaseImage => 'बेस इमेज जोडा';

  @override
  String get aboutPageComingSoon => 'बद्दल पेज लवकरच येत आहे';

  @override
  String get languageSettingsComingSoon => 'भाषा सेटिंग्ज लवकरच येत आहेत';

  @override
  String get notificationSettingsComingSoon => 'नोटिफिकेशन सेटिंग्ज लवकरच येत आहेत';

  @override
  String get manageNotificationSettings => 'नोटिफिकेशन सेटिंग्ज व्यवस्थापित करा';

  @override
  String get imageShape => 'इमेज आकार';

  @override
  String get businessLogoShape => 'व्यापार लोगो आकार';

  @override
  String get profilePhoto => 'प्रोफाइल फोटो';

  @override
  String get businessLogo => 'व्यापार लोगो';

  @override
  String get pleaseSelectUserType => 'कृपया वापरकर्ता प्रकार निवडा';

  @override
  String get failedToLoadUserTypes => 'वापरकर्ता प्रकार लोड करण्यात अयशस्वी';

  @override
  String get failedToSaveUserType => 'वापरकर्ता प्रकार जतन करण्यात अयशस्वी';

  @override
  String get profileCompletedSuccessfully => 'प्रोफाइल यशस्वीरित्या पूर्ण केली';

  @override
  String get viewEditProfileInfo => 'तुमची प्रोफाइल माहिती पहा आणि संपादित करा';

  @override
  String get chooseImageSource => 'इमेज स्रोत निवडा';

  @override
  String get customImage => 'कस्टम इमेज';

  @override
  String get autoSelect => 'स्वयंचलित निवड';

  @override
  String get imageSource => 'इमेज स्रोत';

  @override
  String get shapeOptions => 'आकार पर्याय';

  @override
  String get none => 'काहीही नाही';

  @override
  String get circle => 'वर्तुळ';

  @override
  String get rectangle => 'आयत';

  @override
  String get roundedRectangle => 'गोलाकार आयत';

  @override
  String get diamond => 'हिरा';

  @override
  String get hexagon => 'षटकोन';

  @override
  String get quickPosters => 'क्विकपोस्टर्स';

  @override
  String get accountActions => 'खाते क्रिया';

  @override
  String get signOutFromAccount => 'तुमच्या खात्यातून साइन आउट करा';

  @override
  String get changingLanguage => 'भाषा बदलली जात आहे...';

  @override
  String get phoneNumberLabel => 'फोन नंबर';

  @override
  String get enterYourPhoneNumber => 'तुमचा फोन नंबर टाका';

  @override
  String get userType => 'वापरकर्ता प्रकार';

  @override
  String get saveToGallery => 'गॅलरीमध्ये सेव्ह करा';

  @override
  String get share => 'शेअर करा';

  @override
  String get whatsApp => 'व्हाट्सअॅप';

  @override
  String get invalidPhoneNumber => 'फोन नंबरचे स्वरूप चुकीचे आहे. कृपया वैध फोन नंबर टाका.';

  @override
  String get quotaExceeded => 'SMS कोटा संपला आहे. कृपया नंतर पुन्हा प्रयत्न करा.';

  @override
  String get userDisabled => 'हा वापरकर्ता अक्षम केला गेला आहे. कृपया सहाय्याशी संपर्क साधा.';

  @override
  String get operationNotAllowed => 'फोन प्रमाणीकरण सक्षम नाही. कृपया सहाय्याशी संपर्क साधा.';

  @override
  String get captchaCheckFailed => 'reCAPTCHA सत्यापन अयशस्वी झाले. कृपया पुन्हा प्रयत्न करा.';

  @override
  String get missingClientIdentifier => 'अॅप सत्यापन प्रक्रिया अयशस्वी झाली. कृपया पुन्हा प्रयत्न करा किंवा सहाय्याशी संपर्क साधा.';

  @override
  String get tooManyRequests => 'या डिव्हाइसवरून बरेच विनंत्या. असामान्य क्रियाकलापामुळे आम्ही या डिव्हाइसवरील सर्व विनंत्या तात्पुरत्या अवरोधित केल्या आहेत. कृपया काही वेळानंतर पुन्हा प्रयत्न करा (सामान्यतः काही तास).';

  @override
  String get verificationFailed => 'सत्यापन अयशस्वी झाले. कृपया पुन्हा प्रयत्न करा.';

  @override
  String get failedToGetUserData => 'वापरकर्ता डेटा मिळवण्यात अयशस्वी';

  @override
  String get userNotFound => 'वापरकर्ता सापडला नाही';

  @override
  String get politician => 'राजकारणी';

  @override
  String get businessman => 'व्यापारी';

  @override
  String get regularUser => 'नियमित';

  @override
  String get forPoliticalCampaigns => 'राजकीय मोहिमा आणि प्रचारासाठी';

  @override
  String get forBusinessPromotions => 'व्यापारिक प्रचार आणि जाहिरातींसाठी';

  @override
  String get forPersonalUse => 'वैयक्तिक वापर आणि सामान्य हेतूंसाठी';

  @override
  String get developmentLogin => 'डेव्हलपमेंट लॉगिन';

  @override
  String get developmentMode => 'विकास मोड';

  @override
  String get loggedInAsDeveloper => 'तुम्ही डेव्हलपर म्हणून लॉगिन केले आहे.\nFirebase फोन प्रमाणीकरण अवरोधित असताना हा तात्पुरता मोड आहे.';

  @override
  String get noActiveSubscription => 'कोणतेही सक्रिय सबस्क्रिप्शन नाही';

  @override
  String expiresInDays(int days) {
    return '$days दिवसांत संपते';
  }

  @override
  String activeUntil(String date) {
    return '$date पर्यंत सक्रिय';
  }

  @override
  String get downloadImage => 'इमेज डाउनलोड करा';

  @override
  String get shareOnWhatsApp => 'व्हाट्सअॅपवर शेअर करा';

  @override
  String get customImageSelected => 'कस्टम इमेज निवडली!';

  @override
  String errorSelectingImage(String error) {
    return 'इमेज निवडण्यात त्रुटी: $error';
  }

  @override
  String get imageAddedSuccessfully => 'इमेज यशस्वीरित्या जोडली!';

  @override
  String errorAddingImage(String error) {
    return 'इमेज जोडण्यात त्रुटी: $error';
  }

  @override
  String noImageAvailable(String source) {
    return 'कोणतीही $source इमेज उपलब्ध नाही';
  }

  @override
  String get emailAddress => 'ईमेल पत्ता';

  @override
  String get enterEmailAddress => 'तुमचा ईमेल पत्ता टाका';

  @override
  String get optional => 'पर्यायी';

  @override
  String get selectProfileImage => 'प्रोफाइल इमेज निवडा';

  @override
  String get camera => 'कॅमेरा';

  @override
  String get gallery => 'गॅलरी';

  @override
  String get removeImage => 'इमेज काढा';

  @override
  String get saveProfile => 'प्रोफाइल सेव्ह करा';

  @override
  String get updateProfile => 'प्रोफाइल अपडेट करा';

  @override
  String get profileImageUpdated => 'प्रोफाइल इमेज यशस्वीरित्या अपडेट केली';

  @override
  String get failedToUpdateProfileImage => 'प्रोफाइल इमेज अपडेट करण्यात अयशस्वी';

  @override
  String get pleaseEnterValidEmail => 'कृपया वैध ईमेल पत्ता टाका';

  @override
  String get pleaseEnterName => 'कृपया तुमचे नाव टाका';

  @override
  String get nameTooShort => 'नाव किमान 2 अक्षरांचे असावे';

  @override
  String get invalidEmailFormat => 'कृपया वैध ईमेल स्वरूप टाका';

  @override
  String get business => 'व्यापार';

  @override
  String get event => 'कार्यक्रम';

  @override
  String get festival => 'सण';

  @override
  String get party => 'पार्टी';

  @override
  String get education => 'शिक्षण';

  @override
  String get health => 'आरोग्य';

  @override
  String get technology => 'तंत्रज्ञान';

  @override
  String get food => 'अन्न';

  @override
  String get travel => 'प्रवास';

  @override
  String get sports => 'खेळ';

  @override
  String get fashion => 'फॅशन';

  @override
  String get realEstate => 'रिअल इस्टेट';

  @override
  String get flyers => 'फ्लायर्स';

  @override
  String get brochures => 'ब्रोशर';

  @override
  String get businessCards => 'बिझनेस कार्ड';

  @override
  String get presentations => 'सादरीकरण';

  @override
  String get logos => 'लोगो';

  @override
  String get invitations => 'आमंत्रण';

  @override
  String get tickets => 'तिकीट';

  @override
  String get programs => 'कार्यक्रम';

  @override
  String get banners => 'बॅनर';

  @override
  String get posters => 'पोस्टर';

  @override
  String get celebration => 'उत्सव';

  @override
  String get religious => 'धार्मिक';

  @override
  String get cultural => 'सांस्कृतिक';

  @override
  String get seasonal => 'हंगामी';

  @override
  String get birthday => 'वाढदिवस';

  @override
  String get wedding => 'लग्न';

  @override
  String get anniversary => 'वर्धापनदिन';

  @override
  String get graduation => 'पदवी';

  @override
  String get certificates => 'प्रमाणपत्र';

  @override
  String get worksheets => 'वर्कशीट';

  @override
  String get awareness => 'जागरूकता';

  @override
  String get medical => 'वैद्यकीय';

  @override
  String get fitness => 'फिटनेस';

  @override
  String get nutrition => 'पोषण';

  @override
  String get appPromotion => 'अॅप प्रमोशन';

  @override
  String get software => 'सॉफ्टवेअर';

  @override
  String get digitalServices => 'डिजिटल सेवा';

  @override
  String get menu => 'मेनू';

  @override
  String get recipe => 'रेसिपी';

  @override
  String get restaurant => 'रेस्टॉरंट';

  @override
  String get catering => 'कॅटरिंग';

  @override
  String get tourism => 'पर्यटन';

  @override
  String get hotels => 'हॉटेल';

  @override
  String get transportation => 'वाहतूक';

  @override
  String get adventure => 'साहसी';

  @override
  String get team => 'संघ';

  @override
  String get tournament => 'स्पर्धा';

  @override
  String get equipment => 'उपकरणे';

  @override
  String get clothing => 'कपडे';

  @override
  String get accessories => 'अॅक्सेसरीज';

  @override
  String get beauty => 'सौंदर्य';

  @override
  String get style => 'शैली';

  @override
  String get property => 'मालमत्ता';

  @override
  String get rental => 'भाडे';

  @override
  String get commercial => 'व्यावसायिक';

  @override
  String get residential => 'निवासी';

  @override
  String get noShape => 'कोणताही आकार नाही';

  @override
  String get rounded => 'गोलाकार';

  @override
  String get loadMoreTemplates => 'आणखी टेम्प्लेट्स लोड करा';

  @override
  String get horizontal => 'क्षैतिज';

  @override
  String get vertical => 'उभा';

  @override
  String get square => 'चौरस';

  @override
  String get smallBusiness => 'छोटा व्यापार';

  @override
  String get eventOrganizers => 'कार्यक्रम आयोजक';

  @override
  String get educators => 'शिक्षक';

  @override
  String get marketers => 'मार्केटर्स';

  @override
  String get students => 'विद्यार्थी';

  @override
  String get professionals => 'व्यावसायिक';

  @override
  String get generalPublic => 'सामान्य जनता';

  @override
  String get modern => 'आधुनिक';

  @override
  String get minimalist => 'किमान';

  @override
  String get retro => 'रेट्रो';

  @override
  String get elegant => 'सुंदर';

  @override
  String get creative => 'सर्जनशील';

  @override
  String get professional => 'व्यावसायिक';

  @override
  String get playful => 'खेळकर';

  @override
  String get print => 'प्रिंट';

  @override
  String get socialMedia => 'सोशल मीडिया';

  @override
  String get onlineAdvertising => 'ऑनलाइन जाहिरात';

  @override
  String get email => 'ईमेल';

  @override
  String get web => 'वेब';

  @override
  String get high => 'उच्च';

  @override
  String get medium => 'मध्यम';

  @override
  String get low => 'कमी';

  @override
  String get freeForPersonalUse => 'वैयक्तिक वापरासाठी मोफत';

  @override
  String get commercialUseAllowed => 'व्यावसायिक वापराची परवानगी';

  @override
  String get premiumLicenseRequired => 'प्रीमियम परवाना आवश्यक';

  @override
  String get templateName => 'टेम्प्लेट नाव';

  @override
  String get templateDescription => 'टेम्प्लेट वर्णन';

  @override
  String get creator => 'निर्माता';

  @override
  String get dimensions => 'परिमाण';

  @override
  String get tags => 'टॅग';

  @override
  String get colors => 'रंग';

  @override
  String get fontStyles => 'फॉन्ट शैली';

  @override
  String get fileFormats => 'फाइल स्वरूप';

  @override
  String get category => 'श्रेणी';

  @override
  String get subCategory => 'उप श्रेणी';

  @override
  String get layoutType => 'लेआउट प्रकार';

  @override
  String get targetAudience => 'लक्ष्यित प्रेक्षक';

  @override
  String get designStyle => 'डिझाइन शैली';

  @override
  String get usageType => 'वापर प्रकार';

  @override
  String get resolution => 'रिझोल्यूशन';

  @override
  String get licenseType => 'परवाना प्रकार';

  @override
  String get isPremium => 'प्रीमियम आहे';

  @override
  String get isActive => 'सक्रिय आहे';

  @override
  String get selectImage => 'इमेज निवडा';

  @override
  String get uploadTemplate => 'टेम्प्लेट अपलोड करा';

  @override
  String get updateTemplate => 'टेम्प्लेट अपडेट करा';

  @override
  String get uploading => 'अपलोड होत आहे...';

  @override
  String get templateUploadedSuccessfully => 'टेम्प्लेट यशस्वीरित्या अपलोड केले';

  @override
  String get templateUpdatedSuccessfully => 'टेम्प्लेट यशस्वीरित्या अपडेट केले';

  @override
  String get failedToUploadTemplate => 'टेम्प्लेट अपलोड करण्यात अयशस्वी';

  @override
  String get failedToUpdateTemplate => 'टेम्प्लेट अपडेट करण्यात अयशस्वी';

  @override
  String get pleaseSelectImage => 'कृपया एक इमेज निवडा';

  @override
  String get pleaseEnterTemplateName => 'कृपया टेम्प्लेट नाव टाका';

  @override
  String get pleaseSelectCategory => 'कृपया एक श्रेणी निवडा';

  @override
  String get loadingBanners => 'बॅनर लोड होत आहेत...';

  @override
  String get loadingMoreBanners => 'आणखी बॅनर लोड होत आहेत...';

  @override
  String get errorLoadingBanners => 'बॅनर लोड करण्यात त्रुटी';

  @override
  String get noBannersFound => 'कोणतेही बॅनर सापडले नाहीत';

  @override
  String get selectBanner => 'बॅनर निवडा';

  @override
  String get customizeTemplate => 'टेम्प्लेट कस्टमाइझ करा';

  @override
  String get imageCustomization => 'इमेज कस्टमायझेशन';

  @override
  String get bannerCustomization => 'बॅनर कस्टमायझेशन';

  @override
  String get textCustomization => 'टेक्स्ट कस्टमायझेशन';

  @override
  String get previewTemplate => 'टेम्प्लेट पूर्वावलोकन';

  @override
  String get saveTemplate => 'टेम्प्लेट सेव्ह करा';

  @override
  String get downloadTemplate => 'टेम्प्लेट डाउनलोड करा';

  @override
  String get shareTemplate => 'टेम्प्लेट शेअर करा';

  @override
  String get templateSavedSuccessfully => 'टेम्प्लेट यशस्वीरित्या सेव्ह केले';

  @override
  String get templateDownloadedSuccessfully => 'टेम्प्लेट यशस्वीरित्या डाउनलोड केले';

  @override
  String get failedToSaveTemplate => 'टेम्प्लेट सेव्ह करण्यात अयशस्वी';

  @override
  String get failedToDownloadTemplate => 'टेम्प्लेट डाउनलोड करण्यात अयशस्वी';

  @override
  String get processingImage => 'इमेज प्रक्रिया होत आहे...';

  @override
  String get generatingPreview => 'पूर्वावलोकन तयार होत आहे...';

  @override
  String get preparingDownload => 'डाउनलोड तयार होत आहे...';

  @override
  String get manageBusiness => 'व्यापार व्यवस्थापन';

  @override
  String get configureBusinessDetails => 'तुमचे व्यापार तपशील, पत्ता आणि इतर पॅरामीटर कॉन्फिगर करा';

  @override
  String get managePolitical => 'राजकीय व्यवस्थापन';

  @override
  String get failedToLoadBusinessProfile => 'व्यापारिक प्रोफाइल लोड करण्यात अयशस्वी';

  @override
  String get failedToLoadPoliticalProfile => 'राजकीय प्रोफाइल लोड करण्यात अयशस्वी';

  @override
  String get noBusinessParametersAvailable => 'कोणतेही व्यापारिक पॅरामीटर उपलब्ध नाहीत';

  @override
  String get businessParametersConfiguredByAdmin => 'व्यापारिक पॅरामीटर प्रशासकाद्वारे कॉन्फिगर केले जातील';

  @override
  String get noPoliticalParametersAvailable => 'कोणतेही राजकीय पॅरामीटर उपलब्ध नाहीत';

  @override
  String get politicalParametersConfiguredByAdmin => 'राजकीय पॅरामीटर प्रशासकाद्वारे कॉन्फिगर केले जातील';

  @override
  String get saveBusinessParameters => 'व्यापारिक पॅरामीटर सेव्ह करा';

  @override
  String get savePoliticalParameters => 'राजकीय पॅरामीटर सेव्ह करा';

  @override
  String get businessParametersSavedSuccessfully => 'व्यापारिक पॅरामीटर यशस्वीरित्या सेव्ह केले';

  @override
  String get politicalParametersSavedSuccessfully => 'राजकीय पॅरामीटर यशस्वीरित्या सेव्ह केले';

  @override
  String get failedToSaveBusinessParameters => 'व्यापारिक पॅरामीटर सेव्ह करण्यात अयशस्वी';

  @override
  String get failedToSavePoliticalParameters => 'राजकीय पॅरामीटर सेव्ह करण्यात अयशस्वी';

  @override
  String get businessType => 'व्यापार प्रकार';

  @override
  String get businessAddress => 'व्यापार पत्ता';

  @override
  String get businessLocation => 'व्यापार स्थान';

  @override
  String get businessMobile => 'व्यापार मोबाइल';

  @override
  String get businessWebsite => 'व्यापार वेबसाइट';

  @override
  String get businessEmail => 'व्यापार ईमेल';

  @override
  String get selectBusinessLogo => 'व्यापार लोगो निवडा';

  @override
  String get politicalPhoto => 'राजकीय फोटो';

  @override
  String get selectPoliticalPhoto => 'राजकीय फोटो निवडा';

  @override
  String get partyName => 'पक्षाचे नाव';

  @override
  String get politicalPosition => 'राजकीय पद';

  @override
  String get constituency => 'मतदारसंघ';

  @override
  String get politicalExperience => 'राजकीय अनुभव';

  @override
  String get campaignSlogan => 'प्रचार घोषवाक्य';

  @override
  String get politicalAchievements => 'राजकीय यश';

  @override
  String get isRequired => 'आवश्यक आहे';

  @override
  String get pleaseEnterValidNumber => 'कृपया वैध संख्या टाका';

  @override
  String get allTypes => 'सर्व प्रकार';

  @override
  String get removePremium => 'प्रीमियम काढा';

  @override
  String get makePremium => 'प्रीमियम बनवा';

  @override
  String get deleteUser => 'वापरकर्ता हटवा';

  @override
  String get userManagement => 'वापरकर्ता व्यवस्थापन';

  @override
  String get searchUsers => 'वापरकर्ते शोधा...';

  @override
  String get noUsersFound => 'कोणतेही वापरकर्ते सापडले नाहीत';

  @override
  String get loadingUsers => 'वापरकर्ते लोड होत आहेत...';

  @override
  String get failedToLoadUsers => 'वापरकर्ते लोड करण्यात अयशस्वी';

  @override
  String get userDetails => 'वापरकर्ता तपशील';

  @override
  String get accountCreated => 'खाते तयार केले';

  @override
  String get lastUpdated => 'शेवटचे अपडेट';

  @override
  String get profileComplete => 'प्रोफाइल पूर्ण';

  @override
  String get profileIncomplete => 'प्रोफाइल अपूर्ण';

  @override
  String get premiumStatus => 'प्रीमियम स्थिती';

  @override
  String get active => 'सक्रिय';

  @override
  String get inactive => 'निष्क्रिय';

  @override
  String get subscriptionPlan => 'सबस्क्रिप्शन योजना';

  @override
  String get subscriptionEndDate => 'सबस्क्रिप्शन समाप्ती तारीख';

  @override
  String get languagePreference => 'भाषा प्राधान्य';

  @override
  String get totalPosters => 'एकूण पोस्टर';

  @override
  String get adminStatus => 'प्रशासक स्थिती';

  @override
  String get isAdmin => 'प्रशासक';

  @override
  String get isNotAdmin => 'प्रशासक नाही';

  @override
  String get allUsers => 'सर्व वापरकर्ते';

  @override
  String get free => 'मोफत';

  @override
  String get retry => 'पुन्हा प्रयत्न करा';

  @override
  String get tryAdjustingFilters => 'तुमचा शोध किंवा फिल्टर समायोजित करण्याचा प्रयत्न करा';

  @override
  String get notProvided => 'प्रदान केले नाही';

  @override
  String get yes => 'होय';

  @override
  String get no => 'नाही';

  @override
  String get created => 'तयार केले';

  @override
  String get name => 'नाव';

  @override
  String get userPremiumStatusRemoved => 'वापरकर्ता प्रीमियम स्थिती काढली';

  @override
  String get userUpgradedToPremium => 'वापरकर्ता प्रीमियममध्ये अपग्रेड केला';

  @override
  String get errorUpdatingUser => 'वापरकर्ता अपडेट करण्यात त्रुटी';

  @override
  String areYouSureDeleteUser(String userName) {
    return 'तुम्हाला खरोखर $userName हटवायचे आहे का? ही क्रिया पूर्ववत केली जाऊ शकत नाही.';
  }

  @override
  String get userDeletedSuccessfully => 'वापरकर्ता यशस्वीरित्या हटवला';

  @override
  String get errorDeletingUser => 'वापरकर्ता हटवण्यात त्रुटी';

  @override
  String get add => 'जोडा';

  @override
  String get extend => 'वाढवा';

  @override
  String subscriptionExtended(String duration) {
    return 'सबस्क्रिप्शन $duration ने वाढवले!';
  }

  @override
  String failedToExtendSubscription(String error) {
    return 'सबस्क्रिप्शन वाढवण्यात अयशस्वी: $error';
  }

  @override
  String failedToPurchaseSubscription(String error) {
    return 'सबस्क्रिप्शन खरेदी करण्यात अयशस्वी: $error';
  }

  @override
  String get failedToRestorePurchases => 'खरेदी पुनर्संचयित करण्यात अयशस्वी';

  @override
  String get trialPlanDescription => 'कमीत कमी खर्चात 1 आठवड्यासाठी प्रीमियम वैशिष्ट्ये वापरून पहा';

  @override
  String get threeMonthPlanDescription => 'अल्पकालीन प्रकल्पांसाठी योग्य';

  @override
  String get sixMonthPlanDescription => 'मध्यम मुदतीच्या वापरासाठी उत्तम मूल्य';

  @override
  String get annualPlanDescription => 'नियमित वापरकर्त्यांसाठी सर्वोत्तम मूल्य';

  @override
  String get allPremiumFeatures => 'सर्व प्रीमियम वैशिष्ट्ये';

  @override
  String get autoRenewsAt => '3 महिन्यांसाठी ₹199 वर ऑटो-रिन्यू';

  @override
  String get cancelAnytime => 'कधीही रद्द करा';

  @override
  String get quarterlyBilling => 'त्रैमासिक बिलिंग';

  @override
  String get save50Percent => '3-महिन्याच्या प्लानच्या तुलनेत 50% बचत';

  @override
  String get prioritySupport => 'प्राधान्य समर्थन';

  @override
  String get save58Percent => '3-महिन्याच्या प्लानच्या तुलनेत 58% बचत';

  @override
  String get earlyAccessToNewFeatures => 'नवीन वैशिष्ट्यांमध्ये लवकर प्रवेश';

  @override
  String activeUntilDate(String date) {
    return '$date पर्यंत सक्रिय';
  }
}
