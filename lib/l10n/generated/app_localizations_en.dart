// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Quick Posters';

  @override
  String get welcome => 'Welcome to Quick Posters';

  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get enterPhoneNumber => 'Enter your phone number';

  @override
  String get sendOTP => 'Send OTP';

  @override
  String get verifyOTP => 'Verify OTP';

  @override
  String get enterOTP => 'Enter the 6-digit OTP';

  @override
  String get resendOTP => 'Resend OTP';

  @override
  String get home => 'Home';

  @override
  String get templates => 'Templates';

  @override
  String get categories => 'Categories';

  @override
  String get all => 'All';

  @override
  String get premium => 'Premium';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get businessUser => 'Business';

  @override
  String get politicalUser => 'Politician';

  @override
  String get individualUser => 'Individual User';

  @override
  String get createPoster => 'Create Poster';

  @override
  String get editTemplate => 'Edit Template';

  @override
  String get saveImage => 'Save Image';

  @override
  String get shareImage => 'Share Image';

  @override
  String get premiumFeature => 'Premium Feature';

  @override
  String get upgradeNow => 'Upgrade Now';

  @override
  String get subscriptionPlans => 'Subscription Plans';

  @override
  String get trialPlan => 'Trial Plan';

  @override
  String get threeMonthPlan => '3-Month Plan';

  @override
  String get sixMonthPlan => '6-Month Plan';

  @override
  String get annualPlan => 'Annual Plan';

  @override
  String rupees(String amount) {
    return '₹$amount';
  }

  @override
  String get myProfile => 'My Profile';

  @override
  String get notifications => 'Notifications';

  @override
  String get about => 'About';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get refundPolicy => 'Refund Policy';

  @override
  String get aiImageGenerator => 'AI Image Generator';

  @override
  String get adminDashboard => 'Admin Dashboard';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get noInternetConnection => 'No internet connection';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get phoneNumberValidation => 'Please enter a valid 10-digit phone number';

  @override
  String get otpValidation => 'Please enter a valid 6-digit OTP';

  @override
  String get otpSent => 'OTP sent successfully';

  @override
  String get otpExpired => 'OTP has expired. Please request a new one';

  @override
  String get invalidOtp => 'Invalid OTP. Please try again';

  @override
  String get loginSuccessful => 'Login successful';

  @override
  String get welcomeBack => 'Welcome back!';

  @override
  String get getStarted => 'Get Started';

  @override
  String get continueText => 'Continue';

  @override
  String get skip => 'Skip';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get done => 'Done';

  @override
  String get close => 'Close';

  @override
  String get back => 'Back';

  @override
  String get selectUserType => 'Select User Type';

  @override
  String get chooseAccountType => 'Choose your account type';

  @override
  String get businessDescription => 'Business Description';

  @override
  String get politicalDescription => 'Ideal for political campaigns and leaders';

  @override
  String get individualDescription => 'Great for personal use and social media';

  @override
  String get completeProfile => 'Complete Your Profile';

  @override
  String get profileCompletionDescription => 'Help us personalize your experience';

  @override
  String get businessName => 'Business Name';

  @override
  String get enterBusinessName => 'Enter your business name';

  @override
  String get businessCategory => 'Business Category';

  @override
  String get selectBusinessCategory => 'Select your business category';

  @override
  String get politicalParty => 'Political Party';

  @override
  String get selectPoliticalParty => 'Select your political party';

  @override
  String get designation => 'Designation';

  @override
  String get enterDesignation => 'Enter your designation';

  @override
  String get fullName => 'Full Name';

  @override
  String get enterFullName => 'Enter your full name';

  @override
  String get profileUpdated => 'Profile updated successfully';

  @override
  String get exploreTemplates => 'Explore Templates';

  @override
  String get featuredTemplates => 'Featured Templates';

  @override
  String get recentTemplates => 'Recent Templates';

  @override
  String get popularTemplates => 'Popular Templates';

  @override
  String get loadingTemplates => 'Loading templates...';

  @override
  String get noTemplatesFound => 'No templates found';

  @override
  String get searchTemplates => 'Search templates...';

  @override
  String get filterByCategory => 'Filter by Category';

  @override
  String get viewAll => 'View All';

  @override
  String get loadMore => 'Load More';

  @override
  String get refreshing => 'Refreshing...';

  @override
  String get pullToRefresh => 'Pull to refresh';

  @override
  String get refresh => 'Refresh';

  @override
  String get account => 'Account';

  @override
  String get appSettings => 'App Settings';

  @override
  String get tools => 'Tools';

  @override
  String get premiumSubscription => 'Premium Subscription';

  @override
  String get youArePremiumUser => 'You are a premium user';

  @override
  String get upgradeToPremium => 'Upgrade to premium';

  @override
  String get createImagesWithAI => 'Create images with AI';

  @override
  String get aboutQuickPosters => 'About QuickPosters';

  @override
  String get version => 'Version 1.0.0';

  @override
  String get readPrivacyPolicy => 'Read our privacy policy';

  @override
  String get viewEditProfile => 'View and edit your profile';

  @override
  String get changeAppLanguage => 'Change app language';

  @override
  String get failedToLoadUserData => 'Failed to load user data';

  @override
  String get profileUpdatedSuccessfully => 'Profile updated successfully';

  @override
  String get failedToSaveProfile => 'Failed to save profile';

  @override
  String get manageBusinessProfile => 'Manage Business Profile';

  @override
  String get managePoliticalProfile => 'Manage Political Profile';

  @override
  String get configurePoliticalDetails => 'Configure your political details, party, and other parameters';

  @override
  String get businessParameters => 'Business Parameters';

  @override
  String get politicalParameters => 'Political Parameters';

  @override
  String get contentRefreshedSuccessfully => 'Content refreshed successfully';

  @override
  String get failedToRefreshContent => 'Failed to refresh content';

  @override
  String get mySubscription => 'My Subscription';

  @override
  String get startByAddingBaseImage => 'Start by adding a base image';

  @override
  String get backgroundOfPoster => 'This will be the background of your poster';

  @override
  String get selectBaseImage => 'Select Base Image';

  @override
  String get addBaseImage => 'Add Base Image';

  @override
  String get aboutPageComingSoon => 'About page coming soon';

  @override
  String get languageSettingsComingSoon => 'Language settings coming soon';

  @override
  String get notificationSettingsComingSoon => 'Notification settings coming soon';

  @override
  String get manageNotificationSettings => 'Manage notification settings';

  @override
  String get imageShape => 'Image Shape';

  @override
  String get businessLogoShape => 'Business Logo Shape';

  @override
  String get profilePhoto => 'Profile Photo';

  @override
  String get businessLogo => 'Business Logo';

  @override
  String get pleaseSelectUserType => 'Please select a user type';

  @override
  String get failedToLoadUserTypes => 'Failed to load user types';

  @override
  String get failedToSaveUserType => 'Failed to save user type';

  @override
  String get profileCompletedSuccessfully => 'Profile completed successfully';

  @override
  String get viewEditProfileInfo => 'View and edit your profile information';

  @override
  String get chooseImageSource => 'Choose Image Source';

  @override
  String get customImage => 'Custom Image';

  @override
  String get autoSelect => 'Auto Select';

  @override
  String get imageSource => 'Image Source';

  @override
  String get shapeOptions => 'Shape Options';

  @override
  String get none => 'None';

  @override
  String get circle => 'Circle';

  @override
  String get rectangle => 'Rectangle';

  @override
  String get roundedRectangle => 'Rounded Rectangle';

  @override
  String get diamond => 'Diamond';

  @override
  String get hexagon => 'Hexagon';

  @override
  String get quickPosters => 'QuickPosters';

  @override
  String get accountActions => 'Account Actions';

  @override
  String get signOutFromAccount => 'Sign out from your account';

  @override
  String get changingLanguage => 'Changing language...';

  @override
  String get phoneNumberLabel => 'Phone Number';

  @override
  String get enterYourPhoneNumber => 'Enter your phone number';

  @override
  String get userType => 'User Type';

  @override
  String get saveToGallery => 'Save to Gallery';

  @override
  String get share => 'Share';

  @override
  String get whatsApp => 'WhatsApp';

  @override
  String get invalidPhoneNumber => 'The phone number format is incorrect. Please enter a valid phone number.';

  @override
  String get quotaExceeded => 'SMS quota exceeded. Please try again later.';

  @override
  String get userDisabled => 'This user has been disabled. Please contact support.';

  @override
  String get operationNotAllowed => 'Phone authentication is not enabled. Please contact support.';

  @override
  String get captchaCheckFailed => 'reCAPTCHA verification failed. Please try again.';

  @override
  String get missingClientIdentifier => 'The app verification process failed. Please try again or contact support.';

  @override
  String get tooManyRequests => 'Too many requests from this device. We have temporarily blocked all requests from this device due to unusual activity. Please try again after some time (usually a few hours).';

  @override
  String get verificationFailed => 'Verification failed. Please try again.';

  @override
  String get failedToGetUserData => 'Failed to get user data';

  @override
  String get userNotFound => 'User not found';

  @override
  String get politician => 'Politician';

  @override
  String get businessman => 'Businessman';

  @override
  String get regularUser => 'Regular';

  @override
  String get forPoliticalCampaigns => 'For political campaigns and promotions';

  @override
  String get forBusinessPromotions => 'For business promotions and advertisements';

  @override
  String get forPersonalUse => 'For personal use and general purposes';

  @override
  String get developmentLogin => 'Development Login';

  @override
  String get developmentMode => 'Development Mode';

  @override
  String get loggedInAsDeveloper => 'You are logged in as a developer.\nThis is a temporary mode while Firebase phone authentication is blocked.';

  @override
  String get noActiveSubscription => 'No active subscription';

  @override
  String expiresInDays(int days) {
    return 'Expires in $days days';
  }

  @override
  String activeUntil(String date) {
    return 'Active until $date';
  }

  @override
  String get downloadImage => 'Download Image';

  @override
  String get shareOnWhatsApp => 'Share on WhatsApp';

  @override
  String get customImageSelected => 'Custom image selected!';

  @override
  String errorSelectingImage(String error) {
    return 'Error selecting image: $error';
  }

  @override
  String get imageAddedSuccessfully => 'Image added successfully!';

  @override
  String errorAddingImage(String error) {
    return 'Error adding image: $error';
  }

  @override
  String noImageAvailable(String source) {
    return 'No $source image available';
  }

  @override
  String get emailAddress => 'Email Address';

  @override
  String get enterEmailAddress => 'Enter your email address';

  @override
  String get optional => 'Optional';

  @override
  String get selectProfileImage => 'Select Profile Image';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get removeImage => 'Remove Image';

  @override
  String get saveProfile => 'Save Profile';

  @override
  String get updateProfile => 'Update Profile';

  @override
  String get profileImageUpdated => 'Profile image updated successfully';

  @override
  String get failedToUpdateProfileImage => 'Failed to update profile image';

  @override
  String get pleaseEnterValidEmail => 'Please enter a valid email address';

  @override
  String get pleaseEnterName => 'Please enter your name';

  @override
  String get nameTooShort => 'Name must be at least 2 characters long';

  @override
  String get invalidEmailFormat => 'Please enter a valid email format';

  @override
  String get business => 'Business';

  @override
  String get event => 'Event';

  @override
  String get festival => 'Festival';

  @override
  String get party => 'Party';

  @override
  String get education => 'Education';

  @override
  String get health => 'Health';

  @override
  String get technology => 'Technology';

  @override
  String get food => 'Food';

  @override
  String get travel => 'Travel';

  @override
  String get sports => 'Sports';

  @override
  String get fashion => 'Fashion';

  @override
  String get realEstate => 'Real Estate';

  @override
  String get flyers => 'Flyers';

  @override
  String get brochures => 'Brochures';

  @override
  String get businessCards => 'Business Cards';

  @override
  String get presentations => 'Presentations';

  @override
  String get logos => 'Logos';

  @override
  String get invitations => 'Invitations';

  @override
  String get tickets => 'Tickets';

  @override
  String get programs => 'Programs';

  @override
  String get banners => 'Banners';

  @override
  String get posters => 'Posters';

  @override
  String get celebration => 'Celebration';

  @override
  String get religious => 'Religious';

  @override
  String get cultural => 'Cultural';

  @override
  String get seasonal => 'Seasonal';

  @override
  String get birthday => 'Birthday';

  @override
  String get wedding => 'Wedding';

  @override
  String get anniversary => 'Anniversary';

  @override
  String get graduation => 'Graduation';

  @override
  String get certificates => 'Certificates';

  @override
  String get worksheets => 'Worksheets';

  @override
  String get awareness => 'Awareness';

  @override
  String get medical => 'Medical';

  @override
  String get fitness => 'Fitness';

  @override
  String get nutrition => 'Nutrition';

  @override
  String get appPromotion => 'App Promotion';

  @override
  String get software => 'Software';

  @override
  String get digitalServices => 'Digital Services';

  @override
  String get menu => 'Menu';

  @override
  String get recipe => 'Recipe';

  @override
  String get restaurant => 'Restaurant';

  @override
  String get catering => 'Catering';

  @override
  String get tourism => 'Tourism';

  @override
  String get hotels => 'Hotels';

  @override
  String get transportation => 'Transportation';

  @override
  String get adventure => 'Adventure';

  @override
  String get team => 'Team';

  @override
  String get tournament => 'Tournament';

  @override
  String get equipment => 'Equipment';

  @override
  String get clothing => 'Clothing';

  @override
  String get accessories => 'Accessories';

  @override
  String get beauty => 'Beauty';

  @override
  String get style => 'Style';

  @override
  String get property => 'Property';

  @override
  String get rental => 'Rental';

  @override
  String get commercial => 'Commercial';

  @override
  String get residential => 'Residential';

  @override
  String get noShape => 'No Shape';

  @override
  String get rounded => 'Rounded';

  @override
  String get loadMoreTemplates => 'Load More Templates';

  @override
  String get horizontal => 'Horizontal';

  @override
  String get vertical => 'Vertical';

  @override
  String get square => 'Square';

  @override
  String get smallBusiness => 'Small Business';

  @override
  String get eventOrganizers => 'Event Organizers';

  @override
  String get educators => 'Educators';

  @override
  String get marketers => 'Marketers';

  @override
  String get students => 'Students';

  @override
  String get professionals => 'Professionals';

  @override
  String get generalPublic => 'General Public';

  @override
  String get modern => 'Modern';

  @override
  String get minimalist => 'Minimalist';

  @override
  String get retro => 'Retro';

  @override
  String get elegant => 'Elegant';

  @override
  String get creative => 'Creative';

  @override
  String get professional => 'Professional';

  @override
  String get playful => 'Playful';

  @override
  String get print => 'Print';

  @override
  String get socialMedia => 'Social Media';

  @override
  String get onlineAdvertising => 'Online Advertising';

  @override
  String get email => 'Email';

  @override
  String get web => 'Web';

  @override
  String get high => 'High';

  @override
  String get medium => 'Medium';

  @override
  String get low => 'Low';

  @override
  String get freeForPersonalUse => 'Free for personal use';

  @override
  String get commercialUseAllowed => 'Commercial use allowed';

  @override
  String get premiumLicenseRequired => 'Premium license required';

  @override
  String get templateName => 'Template Name';

  @override
  String get templateDescription => 'Template Description';

  @override
  String get creator => 'Creator';

  @override
  String get dimensions => 'Dimensions';

  @override
  String get tags => 'Tags';

  @override
  String get colors => 'Colors';

  @override
  String get fontStyles => 'Font Styles';

  @override
  String get fileFormats => 'File Formats';

  @override
  String get category => 'Category';

  @override
  String get subCategory => 'Sub Category';

  @override
  String get layoutType => 'Layout Type';

  @override
  String get targetAudience => 'Target Audience';

  @override
  String get designStyle => 'Design Style';

  @override
  String get usageType => 'Usage Type';

  @override
  String get resolution => 'Resolution';

  @override
  String get licenseType => 'License Type';

  @override
  String get isPremium => 'Is Premium';

  @override
  String get isActive => 'Is Active';

  @override
  String get selectImage => 'Select Image';

  @override
  String get uploadTemplate => 'Upload Template';

  @override
  String get updateTemplate => 'Update Template';

  @override
  String get uploading => 'Uploading...';

  @override
  String get templateUploadedSuccessfully => 'Template uploaded successfully';

  @override
  String get templateUpdatedSuccessfully => 'Template updated successfully';

  @override
  String get failedToUploadTemplate => 'Failed to upload template';

  @override
  String get failedToUpdateTemplate => 'Failed to update template';

  @override
  String get pleaseSelectImage => 'Please select an image';

  @override
  String get pleaseEnterTemplateName => 'Please enter template name';

  @override
  String get pleaseSelectCategory => 'Please select a category';

  @override
  String get loadingBanners => 'Loading banners...';

  @override
  String get loadingMoreBanners => 'Loading more banners...';

  @override
  String get errorLoadingBanners => 'Error loading banners';

  @override
  String get noBannersFound => 'No banners found';

  @override
  String get selectBanner => 'Select Banner';

  @override
  String get customizeTemplate => 'Customize Template';

  @override
  String get imageCustomization => 'Image Customization';

  @override
  String get bannerCustomization => 'Banner Customization';

  @override
  String get textCustomization => 'Text Customization';

  @override
  String get previewTemplate => 'Preview Template';

  @override
  String get saveTemplate => 'Save Template';

  @override
  String get downloadTemplate => 'Download Template';

  @override
  String get shareTemplate => 'Share Template';

  @override
  String get templateSavedSuccessfully => 'Template saved successfully';

  @override
  String get templateDownloadedSuccessfully => 'Template downloaded successfully';

  @override
  String get failedToSaveTemplate => 'Failed to save template';

  @override
  String get failedToDownloadTemplate => 'Failed to download template';

  @override
  String get processingImage => 'Processing image...';

  @override
  String get generatingPreview => 'Generating preview...';

  @override
  String get preparingDownload => 'Preparing download...';

  @override
  String get manageBusiness => 'Manage Business';

  @override
  String get configureBusinessDetails => 'Configure your business details, address, and other parameters';

  @override
  String get managePolitical => 'Manage Political';

  @override
  String get failedToLoadBusinessProfile => 'Failed to load business profile';

  @override
  String get failedToLoadPoliticalProfile => 'Failed to load political profile';

  @override
  String get noBusinessParametersAvailable => 'No business parameters available';

  @override
  String get businessParametersConfiguredByAdmin => 'Business parameters will be configured by the administrator';

  @override
  String get noPoliticalParametersAvailable => 'No political parameters available';

  @override
  String get politicalParametersConfiguredByAdmin => 'Political parameters will be configured by the administrator';

  @override
  String get saveBusinessParameters => 'Save Business Parameters';

  @override
  String get savePoliticalParameters => 'Save Political Parameters';

  @override
  String get businessParametersSavedSuccessfully => 'Business parameters saved successfully';

  @override
  String get politicalParametersSavedSuccessfully => 'Political parameters saved successfully';

  @override
  String get failedToSaveBusinessParameters => 'Failed to save business parameters';

  @override
  String get failedToSavePoliticalParameters => 'Failed to save political parameters';

  @override
  String get businessType => 'Business Type';

  @override
  String get businessAddress => 'Business Address';

  @override
  String get businessLocation => 'Business Location';

  @override
  String get businessMobile => 'Business Mobile';

  @override
  String get businessWebsite => 'Business Website';

  @override
  String get businessEmail => 'Business Email';

  @override
  String get selectBusinessLogo => 'Select Business Logo';

  @override
  String get politicalPhoto => 'Political photo';

  @override
  String get selectPoliticalPhoto => 'Select Political Photo';

  @override
  String get partyName => 'Party Name';

  @override
  String get politicalPosition => 'Political Position';

  @override
  String get constituency => 'Constituency';

  @override
  String get politicalExperience => 'Political Experience';

  @override
  String get campaignSlogan => 'Campaign Slogan';

  @override
  String get politicalAchievements => 'Political Achievements';

  @override
  String get isRequired => 'is required';

  @override
  String get pleaseEnterValidNumber => 'Please enter a valid number';

  @override
  String get allTypes => 'All Types';

  @override
  String get removePremium => 'Remove Premium';

  @override
  String get makePremium => 'Make Premium';

  @override
  String get deleteUser => 'Delete User';

  @override
  String get userManagement => 'User Management';

  @override
  String get searchUsers => 'Search users...';

  @override
  String get noUsersFound => 'No users found';

  @override
  String get loadingUsers => 'Loading users...';

  @override
  String get failedToLoadUsers => 'Failed to load users';

  @override
  String get userDetails => 'User Details';

  @override
  String get accountCreated => 'Account Created';

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String get profileComplete => 'Profile Complete';

  @override
  String get profileIncomplete => 'Profile Incomplete';

  @override
  String get premiumStatus => 'Premium Status';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get subscriptionPlan => 'Subscription Plan';

  @override
  String get subscriptionEndDate => 'Subscription End Date';

  @override
  String get languagePreference => 'Language Preference';

  @override
  String get totalPosters => 'Total Posters';

  @override
  String get adminStatus => 'Admin Status';

  @override
  String get isAdmin => 'Admin';

  @override
  String get isNotAdmin => 'Not Admin';

  @override
  String get allUsers => 'All Users';

  @override
  String get free => 'Free';

  @override
  String get retry => 'Retry';

  @override
  String get tryAdjustingFilters => 'Try adjusting your search or filters';

  @override
  String get notProvided => 'Not provided';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get created => 'Created';

  @override
  String get name => 'Name';

  @override
  String get userPremiumStatusRemoved => 'User premium status removed';

  @override
  String get userUpgradedToPremium => 'User upgraded to premium';

  @override
  String get errorUpdatingUser => 'Error updating user';

  @override
  String areYouSureDeleteUser(String userName) {
    return 'Are you sure you want to delete $userName? This action cannot be undone.';
  }

  @override
  String get userDeletedSuccessfully => 'User deleted successfully';

  @override
  String get errorDeletingUser => 'Error deleting user';

  @override
  String get add => 'Add';

  @override
  String get extend => 'Extend';

  @override
  String subscriptionExtended(String duration) {
    return 'Subscription extended by $duration!';
  }

  @override
  String failedToExtendSubscription(String error) {
    return 'Failed to extend subscription: $error';
  }

  @override
  String failedToPurchaseSubscription(String error) {
    return 'Failed to purchase subscription: $error';
  }

  @override
  String get failedToRestorePurchases => 'Failed to restore purchases';

  @override
  String get trialPlanDescription => 'Try Premium features for 1 week at minimal cost';

  @override
  String get threeMonthPlanDescription => 'Perfect for short-term projects';

  @override
  String get sixMonthPlanDescription => 'Great value for medium-term use';

  @override
  String get annualPlanDescription => 'Best value for regular users';

  @override
  String get allPremiumFeatures => 'All Premium features';

  @override
  String get autoRenewsAt => 'Auto-renews at ₹199 for 3 months';

  @override
  String get cancelAnytime => 'Cancel anytime';

  @override
  String get quarterlyBilling => 'Quarterly billing';

  @override
  String get save50Percent => 'Save 50% compared to 3-month plan';

  @override
  String get prioritySupport => 'Priority support';

  @override
  String get save58Percent => 'Save 58% compared to 3-month plan';

  @override
  String get earlyAccessToNewFeatures => 'Early access to new features';

  @override
  String activeUntilDate(String date) {
    return 'Active until $date';
  }
}
