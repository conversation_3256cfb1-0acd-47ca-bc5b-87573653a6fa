// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appTitle => 'क्विक पोस्टर्स';

  @override
  String get welcome => 'क्विक पोस्टर्स में आपका स्वागत है';

  @override
  String get login => 'लॉगिन';

  @override
  String get logout => 'लॉगआउट';

  @override
  String get phoneNumber => 'फोन नंबर';

  @override
  String get enterPhoneNumber => 'अपना फोन नंबर दर्ज करें';

  @override
  String get sendOTP => 'OTP भेजें';

  @override
  String get verifyOTP => 'OTP सत्यापित करें';

  @override
  String get enterOTP => '6-अंकीय OTP दर्ज करें';

  @override
  String get resendOTP => 'OTP पुनः भेजें';

  @override
  String get home => 'होम';

  @override
  String get templates => 'टेम्प्लेट्स';

  @override
  String get categories => 'श्रेणियां';

  @override
  String get all => 'सभी';

  @override
  String get premium => 'प्रीमियम';

  @override
  String get profile => 'प्रोफाइल';

  @override
  String get settings => 'सेटिंग्स';

  @override
  String get language => 'भाषा';

  @override
  String get selectLanguage => 'भाषा चुनें';

  @override
  String get businessUser => 'व्यापार';

  @override
  String get politicalUser => 'राजनीतिज्ञ';

  @override
  String get individualUser => 'व्यक्तिगत उपयोगकर्ता';

  @override
  String get createPoster => 'पोस्टर बनाएं';

  @override
  String get editTemplate => 'टेम्प्लेट संपादित करें';

  @override
  String get saveImage => 'छवि सहेजें';

  @override
  String get shareImage => 'इमेज साझा करें';

  @override
  String get premiumFeature => 'प्रीमियम सुविधा';

  @override
  String get upgradeNow => 'अभी अपग्रेड करें';

  @override
  String get subscriptionPlans => 'सब्स्क्रिप्शन प्लान';

  @override
  String get trialPlan => 'ट्रायल प्लान';

  @override
  String get threeMonthPlan => '3-महीने का प्लान';

  @override
  String get sixMonthPlan => '6-महीने का प्लान';

  @override
  String get annualPlan => 'वार्षिक प्लान';

  @override
  String rupees(String amount) {
    return '₹$amount';
  }

  @override
  String get myProfile => 'मेरी प्रोफाइल';

  @override
  String get notifications => 'नोटिफिकेशन';

  @override
  String get about => 'के बारे में';

  @override
  String get privacyPolicy => 'गोपनीयता नीति';

  @override
  String get termsOfService => 'सेवा की शर्तें';

  @override
  String get refundPolicy => 'रिफंड नीति';

  @override
  String get aiImageGenerator => 'AI इमेज जेनरेटर';

  @override
  String get adminDashboard => 'एडमिन डैशबोर्ड';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get save => 'सहेजें';

  @override
  String get delete => 'हटाएं';

  @override
  String get edit => 'संपादित करें';

  @override
  String get loading => 'लोड हो रहा है...';

  @override
  String get error => 'त्रुटि';

  @override
  String get success => 'सफलता';

  @override
  String get tryAgain => 'पुनः प्रयास करें';

  @override
  String get noInternetConnection => 'इंटरनेट कनेक्शन नहीं है';

  @override
  String get somethingWentWrong => 'कुछ गलत हुआ';

  @override
  String get phoneNumberValidation => 'कृपया एक वैध 10-अंकीय फोन नंबर दर्ज करें';

  @override
  String get otpValidation => 'कृपया एक वैध 6-अंकीय OTP दर्ज करें';

  @override
  String get otpSent => 'OTP सफलतापूर्वक भेजा गया';

  @override
  String get otpExpired => 'OTP की समय सीमा समाप्त हो गई है। कृपया नया OTP मांगें';

  @override
  String get invalidOtp => 'गलत OTP। कृपया पुनः प्रयास करें';

  @override
  String get loginSuccessful => 'लॉगिन सफल';

  @override
  String get welcomeBack => 'वापस स्वागत है!';

  @override
  String get getStarted => 'शुरू करें';

  @override
  String get continueText => 'जारी रखें';

  @override
  String get skip => 'छोड़ें';

  @override
  String get next => 'अगला';

  @override
  String get previous => 'पिछला';

  @override
  String get done => 'पूर्ण';

  @override
  String get close => 'बंद करें';

  @override
  String get back => 'वापस';

  @override
  String get selectUserType => 'उपयोगकर्ता प्रकार चुनें';

  @override
  String get chooseAccountType => 'अपना खाता प्रकार चुनें';

  @override
  String get businessDescription => 'व्यापार विवरण';

  @override
  String get politicalDescription => 'राजनीतिक अभियानों और नेताओं के लिए आदर्श';

  @override
  String get individualDescription => 'व्यक्तिगत उपयोग और सोशल मीडिया के लिए बेहतरीन';

  @override
  String get completeProfile => 'अपनी प्रोफाइल पूरी करें';

  @override
  String get profileCompletionDescription => 'अपने अनुभव को व्यक्तिगत बनाने में हमारी सहायता करें';

  @override
  String get businessName => 'व्यापार नाम';

  @override
  String get enterBusinessName => 'अपने व्यापार का नाम दर्ज करें';

  @override
  String get businessCategory => 'व्यापार श्रेणी';

  @override
  String get selectBusinessCategory => 'अपनी व्यापार श्रेणी चुनें';

  @override
  String get politicalParty => 'राजनीतिक पार्टी';

  @override
  String get selectPoliticalParty => 'अपनी राजनीतिक पार्टी चुनें';

  @override
  String get designation => 'पदनाम';

  @override
  String get enterDesignation => 'अपना पदनाम दर्ज करें';

  @override
  String get fullName => 'पूरा नाम';

  @override
  String get enterFullName => 'अपना पूरा नाम दर्ज करें';

  @override
  String get profileUpdated => 'प्रोफाइल सफलतापूर्वक अपडेट की गई';

  @override
  String get exploreTemplates => 'टेम्प्लेट्स देखें';

  @override
  String get featuredTemplates => 'विशेष टेम्प्लेट्स';

  @override
  String get recentTemplates => 'हाल के टेम्प्लेट्स';

  @override
  String get popularTemplates => 'लोकप्रिय टेम्प्लेट्स';

  @override
  String get loadingTemplates => 'टेम्प्लेट्स लोड हो रहे हैं...';

  @override
  String get noTemplatesFound => 'कोई टेम्प्लेट नहीं मिला';

  @override
  String get searchTemplates => 'टेम्प्लेट्स खोजें...';

  @override
  String get filterByCategory => 'श्रेणी के अनुसार फिल्टर करें';

  @override
  String get viewAll => 'सभी देखें';

  @override
  String get loadMore => 'और लोड करें';

  @override
  String get refreshing => 'रिफ्रेश हो रहा है...';

  @override
  String get pullToRefresh => 'रिफ्रेश करने के लिए खींचें';

  @override
  String get refresh => 'रीफ्रेश';

  @override
  String get account => 'खाता';

  @override
  String get appSettings => 'ऐप सेटिंग्स';

  @override
  String get tools => 'उपकरण';

  @override
  String get premiumSubscription => 'प्रीमियम सब्सक्रिप्शन';

  @override
  String get youArePremiumUser => 'आप एक प्रीमियम उपयोगकर्ता हैं';

  @override
  String get upgradeToPremium => 'प्रीमियम में अपग्रेड करें';

  @override
  String get createImagesWithAI => 'AI के साथ इमेज बनाएं';

  @override
  String get aboutQuickPosters => 'क्विक पोस्टर्स के बारे में';

  @override
  String get version => 'संस्करण 1.0.0';

  @override
  String get readPrivacyPolicy => 'हमारी गोपनीयता नीति पढ़ें';

  @override
  String get viewEditProfile => 'अपनी प्रोफाइल देखें और संपादित करें';

  @override
  String get changeAppLanguage => 'ऐप की भाषा बदलें';

  @override
  String get failedToLoadUserData => 'उपयोगकर्ता डेटा लोड करने में विफल';

  @override
  String get profileUpdatedSuccessfully => 'प्रोफाइल सफलतापूर्वक अपडेट की गई';

  @override
  String get failedToSaveProfile => 'प्रोफाइल सहेजने में विफल';

  @override
  String get manageBusinessProfile => 'व्यापारिक प्रोफाइल प्रबंधित करें';

  @override
  String get managePoliticalProfile => 'राजनीतिक प्रोफाइल प्रबंधित करें';

  @override
  String get configurePoliticalDetails => 'अपने राजनीतिक विवरण, पार्टी और अन्य पैरामीटर कॉन्फ़िगर करें';

  @override
  String get businessParameters => 'व्यापारिक पैरामीटर';

  @override
  String get politicalParameters => 'राजनीतिक पैरामीटर';

  @override
  String get contentRefreshedSuccessfully => 'सामग्री सफलतापूर्वक रिफ्रेश की गई';

  @override
  String get failedToRefreshContent => 'सामग्री रिफ्रेश करने में विफल';

  @override
  String get mySubscription => 'मेरी सब्सक्रिप्शन';

  @override
  String get startByAddingBaseImage => 'एक बेस इमेज जोड़कर शुरू करें';

  @override
  String get backgroundOfPoster => 'यह आपके पोस्टर की पृष्ठभूमि होगी';

  @override
  String get selectBaseImage => 'बेस इमेज चुनें';

  @override
  String get addBaseImage => 'बेस इमेज जोड़ें';

  @override
  String get aboutPageComingSoon => 'के बारे में पेज जल्द आ रहा है';

  @override
  String get languageSettingsComingSoon => 'भाषा सेटिंग्स जल्द आ रही हैं';

  @override
  String get notificationSettingsComingSoon => 'नोटिफिकेशन सेटिंग्स जल्द आ रही हैं';

  @override
  String get manageNotificationSettings => 'नोटिफिकेशन सेटिंग्स प्रबंधित करें';

  @override
  String get imageShape => 'छवि आकार';

  @override
  String get businessLogoShape => 'व्यापारिक लोगो आकार';

  @override
  String get profilePhoto => 'प्रोफाइल फोटो';

  @override
  String get businessLogo => 'व्यापार लोगो';

  @override
  String get pleaseSelectUserType => 'कृपया एक उपयोगकर्ता प्रकार चुनें';

  @override
  String get failedToLoadUserTypes => 'उपयोगकर्ता प्रकार लोड करने में विफल';

  @override
  String get failedToSaveUserType => 'उपयोगकर्ता प्रकार सहेजने में विफल';

  @override
  String get profileCompletedSuccessfully => 'प्रोफाइल सफलतापूर्वक पूर्ण की गई';

  @override
  String get viewEditProfileInfo => 'अपनी प्रोफाइल जानकारी देखें और संपादित करें';

  @override
  String get chooseImageSource => 'इमेज स्रोत चुनें';

  @override
  String get customImage => 'कस्टम छवि';

  @override
  String get autoSelect => 'स्वचालित चयन';

  @override
  String get imageSource => 'इमेज स्रोत';

  @override
  String get shapeOptions => 'आकार विकल्प';

  @override
  String get none => 'कोई नहीं';

  @override
  String get circle => 'वृत्त';

  @override
  String get rectangle => 'आयत';

  @override
  String get roundedRectangle => 'गोल आयत';

  @override
  String get diamond => 'हीरा';

  @override
  String get hexagon => 'षट्भुज';

  @override
  String get quickPosters => 'क्विकपोस्टर्स';

  @override
  String get accountActions => 'खाता क्रियाएं';

  @override
  String get signOutFromAccount => 'अपने खाते से साइन आउट करें';

  @override
  String get changingLanguage => 'भाषा बदली जा रही है...';

  @override
  String get phoneNumberLabel => 'फोन नंबर';

  @override
  String get enterYourPhoneNumber => 'अपना फोन नंबर दर्ज करें';

  @override
  String get userType => 'उपयोगकर्ता प्रकार';

  @override
  String get saveToGallery => 'गैलरी में सहेजें';

  @override
  String get share => 'साझा करें';

  @override
  String get whatsApp => 'व्हाट्सऐप';

  @override
  String get invalidPhoneNumber => 'फोन नंबर का प्रारूप गलत है। कृपया एक वैध फोन नंबर दर्ज करें।';

  @override
  String get quotaExceeded => 'SMS कोटा समाप्त हो गया है। कृपया बाद में पुनः प्रयास करें।';

  @override
  String get userDisabled => 'यह उपयोगकर्ता अक्षम कर दिया गया है। कृपया सहायता से संपर्क करें।';

  @override
  String get operationNotAllowed => 'फोन प्रमाणीकरण सक्षम नहीं है। कृपया सहायता से संपर्क करें।';

  @override
  String get captchaCheckFailed => 'reCAPTCHA सत्यापन विफल हुआ। कृपया पुनः प्रयास करें।';

  @override
  String get missingClientIdentifier => 'ऐप सत्यापन प्रक्रिया विफल हुई। कृपया पुनः प्रयास करें या सहायता से संपर्क करें।';

  @override
  String get tooManyRequests => 'इस डिवाइस से बहुत सारे अनुरोध। असामान्य गतिविधि के कारण हमने इस डिवाइस से सभी अनुरोधों को अस्थायी रूप से अवरुद्ध कर दिया है। कृपया कुछ समय बाद पुनः प्रयास करें (आमतौर पर कुछ घंटे)।';

  @override
  String get verificationFailed => 'सत्यापन विफल हुआ। कृपया पुनः प्रयास करें।';

  @override
  String get failedToGetUserData => 'उपयोगकर्ता डेटा प्राप्त करने में विफल';

  @override
  String get userNotFound => 'उपयोगकर्ता नहीं मिला';

  @override
  String get politician => 'राजनेता';

  @override
  String get businessman => 'व्यापारी';

  @override
  String get regularUser => 'नियमित';

  @override
  String get forPoliticalCampaigns => 'राजनीतिक अभियानों और प्रचार के लिए';

  @override
  String get forBusinessPromotions => 'व्यापारिक प्रचार और विज्ञापनों के लिए';

  @override
  String get forPersonalUse => 'व्यक्तिगत उपयोग और सामान्य उद्देश्यों के लिए';

  @override
  String get developmentLogin => 'डेवलपमेंट लॉगिन';

  @override
  String get developmentMode => 'विकास मोड';

  @override
  String get loggedInAsDeveloper => 'आप एक डेवलपर के रूप में लॉग इन हैं।\nयह एक अस्थायी मोड है जब Firebase फोन प्रमाणीकरण अवरुद्ध है।';

  @override
  String get noActiveSubscription => 'कोई सक्रिय सब्सक्रिप्शन नहीं';

  @override
  String expiresInDays(int days) {
    return '$days दिनों में समाप्त होता है';
  }

  @override
  String activeUntil(String date) {
    return '$date तक सक्रिय';
  }

  @override
  String get downloadImage => 'इमेज डाउनलोड करें';

  @override
  String get shareOnWhatsApp => 'व्हाट्सऐप पर साझा करें';

  @override
  String get customImageSelected => 'कस्टम छवि चुनी गई!';

  @override
  String errorSelectingImage(String error) {
    return 'छवि चुनने में त्रुटि: $error';
  }

  @override
  String get imageAddedSuccessfully => 'इमेज सफलतापूर्वक जोड़ी गई!';

  @override
  String errorAddingImage(String error) {
    return 'इमेज जोड़ने में त्रुटि: $error';
  }

  @override
  String noImageAvailable(String source) {
    return 'कोई $source इमेज उपलब्ध नहीं';
  }

  @override
  String get emailAddress => 'ईमेल पता';

  @override
  String get enterEmailAddress => 'अपना ईमेल पता दर्ज करें';

  @override
  String get optional => 'वैकल्पिक';

  @override
  String get selectProfileImage => 'प्रोफाइल इमेज चुनें';

  @override
  String get camera => 'कैमरा';

  @override
  String get gallery => 'गैलरी';

  @override
  String get removeImage => 'इमेज हटाएं';

  @override
  String get saveProfile => 'प्रोफाइल सहेजें';

  @override
  String get updateProfile => 'प्रोफाइल अपडेट करें';

  @override
  String get profileImageUpdated => 'प्रोफाइल इमेज सफलतापूर्वक अपडेट की गई';

  @override
  String get failedToUpdateProfileImage => 'प्रोफाइल इमेज अपडेट करने में विफल';

  @override
  String get pleaseEnterValidEmail => 'कृपया एक वैध ईमेल पता दर्ज करें';

  @override
  String get pleaseEnterName => 'कृपया अपना नाम दर्ज करें';

  @override
  String get nameTooShort => 'नाम कम से कम 2 अक्षर का होना चाहिए';

  @override
  String get invalidEmailFormat => 'कृपया एक वैध ईमेल प्रारूप दर्ज करें';

  @override
  String get business => 'व्यापार';

  @override
  String get event => 'कार्यक्रम';

  @override
  String get festival => 'त्योहार';

  @override
  String get party => 'पार्टी';

  @override
  String get education => 'शिक्षा';

  @override
  String get health => 'स्वास्थ्य';

  @override
  String get technology => 'प्रौद्योगिकी';

  @override
  String get food => 'भोजन';

  @override
  String get travel => 'यात्रा';

  @override
  String get sports => 'खेल';

  @override
  String get fashion => 'फैशन';

  @override
  String get realEstate => 'रियल एस्टेट';

  @override
  String get flyers => 'फ्लायर्स';

  @override
  String get brochures => 'ब्रोशर';

  @override
  String get businessCards => 'बिजनेस कार्ड';

  @override
  String get presentations => 'प्रस्तुतियां';

  @override
  String get logos => 'लोगो';

  @override
  String get invitations => 'निमंत्रण';

  @override
  String get tickets => 'टिकट';

  @override
  String get programs => 'कार्यक्रम';

  @override
  String get banners => 'बैनर';

  @override
  String get posters => 'पोस्टर';

  @override
  String get celebration => 'उत्सव';

  @override
  String get religious => 'धार्मिक';

  @override
  String get cultural => 'सांस्कृतिक';

  @override
  String get seasonal => 'मौसमी';

  @override
  String get birthday => 'जन्मदिन';

  @override
  String get wedding => 'शादी';

  @override
  String get anniversary => 'वर्षगांठ';

  @override
  String get graduation => 'स्नातक';

  @override
  String get certificates => 'प्रमाणपत्र';

  @override
  String get worksheets => 'वर्कशीट';

  @override
  String get awareness => 'जागरूकता';

  @override
  String get medical => 'चिकित्सा';

  @override
  String get fitness => 'फिटनेस';

  @override
  String get nutrition => 'पोषण';

  @override
  String get appPromotion => 'ऐप प्रमोशन';

  @override
  String get software => 'सॉफ्टवेयर';

  @override
  String get digitalServices => 'डिजिटल सेवाएं';

  @override
  String get menu => 'मेनू';

  @override
  String get recipe => 'रेसिपी';

  @override
  String get restaurant => 'रेस्टोरेंट';

  @override
  String get catering => 'कैटरिंग';

  @override
  String get tourism => 'पर्यटन';

  @override
  String get hotels => 'होटल';

  @override
  String get transportation => 'परिवहन';

  @override
  String get adventure => 'साहसिक';

  @override
  String get team => 'टीम';

  @override
  String get tournament => 'टूर्नामेंट';

  @override
  String get equipment => 'उपकरण';

  @override
  String get clothing => 'कपड़े';

  @override
  String get accessories => 'एक्सेसरीज';

  @override
  String get beauty => 'सुंदरता';

  @override
  String get style => 'स्टाइल';

  @override
  String get property => 'संपत्ति';

  @override
  String get rental => 'किराया';

  @override
  String get commercial => 'व्यावसायिक';

  @override
  String get residential => 'आवासीय';

  @override
  String get noShape => 'कोई आकार नहीं';

  @override
  String get rounded => 'गोलाकार';

  @override
  String get loadMoreTemplates => 'और टेम्प्लेट्स लोड करें';

  @override
  String get horizontal => 'क्षैतिज';

  @override
  String get vertical => 'लंबवत';

  @override
  String get square => 'वर्गाकार';

  @override
  String get smallBusiness => 'छोटा व्यापार';

  @override
  String get eventOrganizers => 'कार्यक्रम आयोजक';

  @override
  String get educators => 'शिक्षक';

  @override
  String get marketers => 'मार्केटर्स';

  @override
  String get students => 'छात्र';

  @override
  String get professionals => 'पेशेवर';

  @override
  String get generalPublic => 'आम जनता';

  @override
  String get modern => 'आधुनिक';

  @override
  String get minimalist => 'न्यूनतम';

  @override
  String get retro => 'रेट्रो';

  @override
  String get elegant => 'सुरुचिपूर्ण';

  @override
  String get creative => 'रचनात्मक';

  @override
  String get professional => 'पेशेवर';

  @override
  String get playful => 'मनोरंजक';

  @override
  String get print => 'प्रिंट';

  @override
  String get socialMedia => 'सोशल मीडिया';

  @override
  String get onlineAdvertising => 'ऑनलाइन विज्ञापन';

  @override
  String get email => 'ईमेल';

  @override
  String get web => 'वेब';

  @override
  String get high => 'उच्च';

  @override
  String get medium => 'मध्यम';

  @override
  String get low => 'निम्न';

  @override
  String get freeForPersonalUse => 'व्यक्तिगत उपयोग के लिए मुफ्त';

  @override
  String get commercialUseAllowed => 'व्यावसायिक उपयोग की अनुमति';

  @override
  String get premiumLicenseRequired => 'प्रीमियम लाइसेंस आवश्यक';

  @override
  String get templateName => 'टेम्प्लेट नाम';

  @override
  String get templateDescription => 'टेम्प्लेट विवरण';

  @override
  String get creator => 'निर्माता';

  @override
  String get dimensions => 'आयाम';

  @override
  String get tags => 'टैग';

  @override
  String get colors => 'रंग';

  @override
  String get fontStyles => 'फॉन्ट शैलियां';

  @override
  String get fileFormats => 'फाइल प्रारूप';

  @override
  String get category => 'श्रेणी';

  @override
  String get subCategory => 'उप श्रेणी';

  @override
  String get layoutType => 'लेआउट प्रकार';

  @override
  String get targetAudience => 'लक्षित दर्शक';

  @override
  String get designStyle => 'डिज़ाइन शैली';

  @override
  String get usageType => 'उपयोग प्रकार';

  @override
  String get resolution => 'रिज़ॉल्यूशन';

  @override
  String get licenseType => 'लाइसेंस प्रकार';

  @override
  String get isPremium => 'प्रीमियम है';

  @override
  String get isActive => 'सक्रिय है';

  @override
  String get selectImage => 'छवि चुनें';

  @override
  String get uploadTemplate => 'टेम्प्लेट अपलोड करें';

  @override
  String get updateTemplate => 'टेम्प्लेट अपडेट करें';

  @override
  String get uploading => 'अपलोड हो रहा है...';

  @override
  String get templateUploadedSuccessfully => 'टेम्प्लेट सफलतापूर्वक अपलोड किया गया';

  @override
  String get templateUpdatedSuccessfully => 'टेम्प्लेट सफलतापूर्वक अपडेट किया गया';

  @override
  String get failedToUploadTemplate => 'टेम्प्लेट अपलोड करने में विफल';

  @override
  String get failedToUpdateTemplate => 'टेम्प्लेट अपडेट करने में विफल';

  @override
  String get pleaseSelectImage => 'कृपया एक छवि चुनें';

  @override
  String get pleaseEnterTemplateName => 'कृपया टेम्प्लेट नाम दर्ज करें';

  @override
  String get pleaseSelectCategory => 'कृपया एक श्रेणी चुनें';

  @override
  String get loadingBanners => 'बैनर लोड हो रहे हैं...';

  @override
  String get loadingMoreBanners => 'और बैनर लोड हो रहे हैं...';

  @override
  String get errorLoadingBanners => 'बैनर लोड करने में त्रुटि';

  @override
  String get noBannersFound => 'कोई बैनर नहीं मिला';

  @override
  String get selectBanner => 'बैनर चुनें';

  @override
  String get customizeTemplate => 'टेम्प्लेट कस्टमाइज़ करें';

  @override
  String get imageCustomization => 'छवि कस्टमाइज़ेशन';

  @override
  String get bannerCustomization => 'बैनर कस्टमाइज़ेशन';

  @override
  String get textCustomization => 'टेक्स्ट कस्टमाइज़ेशन';

  @override
  String get previewTemplate => 'टेम्प्लेट पूर्वावलोकन';

  @override
  String get saveTemplate => 'टेम्प्लेट सहेजें';

  @override
  String get downloadTemplate => 'टेम्प्लेट डाउनलोड करें';

  @override
  String get shareTemplate => 'टेम्प्लेट साझा करें';

  @override
  String get templateSavedSuccessfully => 'टेम्प्लेट सफलतापूर्वक सहेजा गया';

  @override
  String get templateDownloadedSuccessfully => 'टेम्प्लेट सफलतापूर्वक डाउनलोड किया गया';

  @override
  String get failedToSaveTemplate => 'टेम्प्लेट सहेजने में विफल';

  @override
  String get failedToDownloadTemplate => 'टेम्प्लेट डाउनलोड करने में विफल';

  @override
  String get processingImage => 'छवि प्रसंस्करण हो रहा है...';

  @override
  String get generatingPreview => 'पूर्वावलोकन तैयार हो रहा है...';

  @override
  String get preparingDownload => 'डाउनलोड तैयार हो रहा है...';

  @override
  String get manageBusiness => 'व्यापार प्रबंधन';

  @override
  String get configureBusinessDetails => 'अपने व्यापार विवरण, पता और अन्य पैरामीटर कॉन्फ़िगर करें';

  @override
  String get managePolitical => 'राजनीतिक प्रबंधन';

  @override
  String get failedToLoadBusinessProfile => 'व्यापारिक प्रोफ़ाइल लोड करने में विफल';

  @override
  String get failedToLoadPoliticalProfile => 'राजनीतिक प्रोफ़ाइल लोड करने में विफल';

  @override
  String get noBusinessParametersAvailable => 'कोई व्यापारिक पैरामीटर उपलब्ध नहीं';

  @override
  String get businessParametersConfiguredByAdmin => 'व्यापारिक पैरामीटर प्रशासक द्वारा कॉन्फ़िगर किए जाएंगे';

  @override
  String get noPoliticalParametersAvailable => 'कोई राजनीतिक पैरामीटर उपलब्ध नहीं';

  @override
  String get politicalParametersConfiguredByAdmin => 'राजनीतिक पैरामीटर प्रशासक द्वारा कॉन्फ़िगर किए जाएंगे';

  @override
  String get saveBusinessParameters => 'व्यापारिक पैरामीटर सहेजें';

  @override
  String get savePoliticalParameters => 'राजनीतिक पैरामीटर सहेजें';

  @override
  String get businessParametersSavedSuccessfully => 'व्यापारिक पैरामीटर सफलतापूर्वक सहेजे गए';

  @override
  String get politicalParametersSavedSuccessfully => 'राजनीतिक पैरामीटर सफलतापूर्वक सहेजे गए';

  @override
  String get failedToSaveBusinessParameters => 'व्यापारिक पैरामीटर सहेजने में विफल';

  @override
  String get failedToSavePoliticalParameters => 'राजनीतिक पैरामीटर सहेजने में विफल';

  @override
  String get businessType => 'व्यापार प्रकार';

  @override
  String get businessAddress => 'व्यापार पता';

  @override
  String get businessLocation => 'व्यापार स्थान';

  @override
  String get businessMobile => 'व्यापार मोबाइल';

  @override
  String get businessWebsite => 'व्यापार वेबसाइट';

  @override
  String get businessEmail => 'व्यापार ईमेल';

  @override
  String get selectBusinessLogo => 'व्यापार लोगो चुनें';

  @override
  String get politicalPhoto => 'राजनीतिक फोटो';

  @override
  String get selectPoliticalPhoto => 'राजनीतिक फोटो चुनें';

  @override
  String get partyName => 'पार्टी नाम';

  @override
  String get politicalPosition => 'राजनीतिक पद';

  @override
  String get constituency => 'निर्वाचन क्षेत्र';

  @override
  String get politicalExperience => 'राजनीतिक अनुभव';

  @override
  String get campaignSlogan => 'चुनावी नारा';

  @override
  String get politicalAchievements => 'राजनीतिक उपलब्धियां';

  @override
  String get isRequired => 'आवश्यक है';

  @override
  String get pleaseEnterValidNumber => 'कृपया एक वैध संख्या दर्ज करें';

  @override
  String get allTypes => 'सभी प्रकार';

  @override
  String get removePremium => 'प्रीमियम हटाएं';

  @override
  String get makePremium => 'प्रीमियम बनाएं';

  @override
  String get deleteUser => 'उपयोगकर्ता हटाएं';

  @override
  String get userManagement => 'उपयोगकर्ता प्रबंधन';

  @override
  String get searchUsers => 'उपयोगकर्ता खोजें...';

  @override
  String get noUsersFound => 'कोई उपयोगकर्ता नहीं मिला';

  @override
  String get loadingUsers => 'उपयोगकर्ता लोड हो रहे हैं...';

  @override
  String get failedToLoadUsers => 'उपयोगकर्ता लोड करने में विफल';

  @override
  String get userDetails => 'उपयोगकर्ता विवरण';

  @override
  String get accountCreated => 'खाता बनाया गया';

  @override
  String get lastUpdated => 'अंतिम अपडेट';

  @override
  String get profileComplete => 'प्रोफ़ाइल पूर्ण';

  @override
  String get profileIncomplete => 'प्रोफ़ाइल अधूरी';

  @override
  String get premiumStatus => 'प्रीमियम स्थिति';

  @override
  String get active => 'सक्रिय';

  @override
  String get inactive => 'निष्क्रिय';

  @override
  String get subscriptionPlan => 'सब्सक्रिप्शन योजना';

  @override
  String get subscriptionEndDate => 'सब्सक्रिप्शन समाप्ति तिथि';

  @override
  String get languagePreference => 'भाषा प्राथमिकता';

  @override
  String get totalPosters => 'कुल पोस्टर';

  @override
  String get adminStatus => 'प्रशासक स्थिति';

  @override
  String get isAdmin => 'प्रशासक';

  @override
  String get isNotAdmin => 'प्रशासक नहीं';

  @override
  String get allUsers => 'सभी उपयोगकर्ता';

  @override
  String get free => 'मुफ्त';

  @override
  String get retry => 'पुनः प्रयास';

  @override
  String get tryAdjustingFilters => 'अपनी खोज या फ़िल्टर को समायोजित करने का प्रयास करें';

  @override
  String get notProvided => 'प्रदान नहीं किया गया';

  @override
  String get yes => 'हाँ';

  @override
  String get no => 'नहीं';

  @override
  String get created => 'बनाया गया';

  @override
  String get name => 'नाम';

  @override
  String get userPremiumStatusRemoved => 'उपयोगकर्ता प्रीमियम स्थिति हटा दी गई';

  @override
  String get userUpgradedToPremium => 'उपयोगकर्ता को प्रीमियम में अपग्रेड किया गया';

  @override
  String get errorUpdatingUser => 'उपयोगकर्ता अपडेट करने में त्रुटि';

  @override
  String areYouSureDeleteUser(String userName) {
    return 'क्या आप वाकई $userName को हटाना चाहते हैं? यह क्रिया पूर्ववत नहीं की जा सकती।';
  }

  @override
  String get userDeletedSuccessfully => 'उपयोगकर्ता सफलतापूर्वक हटा दिया गया';

  @override
  String get errorDeletingUser => 'उपयोगकर्ता हटाने में त्रुटि';

  @override
  String get add => 'जोड़ें';

  @override
  String get extend => 'बढ़ाएं';

  @override
  String subscriptionExtended(String duration) {
    return 'सब्सक्रिप्शन $duration तक बढ़ाया गया!';
  }

  @override
  String failedToExtendSubscription(String error) {
    return 'सब्सक्रिप्शन बढ़ाने में विफल: $error';
  }

  @override
  String failedToPurchaseSubscription(String error) {
    return 'सब्सक्रिप्शन खरीदने में विफल: $error';
  }

  @override
  String get failedToRestorePurchases => 'खरीदारी पुनर्स्थापित करने में विफल';

  @override
  String get trialPlanDescription => 'न्यूनतम लागत पर 1 सप्ताह के लिए प्रीमियम सुविधाओं का प्रयास करें';

  @override
  String get threeMonthPlanDescription => 'अल्पकालिक परियोजनाओं के लिए उपयुक्त';

  @override
  String get sixMonthPlanDescription => 'मध्यम अवधि के उपयोग के लिए बेहतरीन मूल्य';

  @override
  String get annualPlanDescription => 'नियमित उपयोगकर्ताओं के लिए सर्वोत्तम मूल्य';

  @override
  String get allPremiumFeatures => 'सभी प्रीमियम सुविधाएं';

  @override
  String get autoRenewsAt => '3 महीने के लिए ₹199 पर ऑटो-रिन्यू';

  @override
  String get cancelAnytime => 'कभी भी रद्द करें';

  @override
  String get quarterlyBilling => 'त्रैमासिक बिलिंग';

  @override
  String get save50Percent => '3-महीने के प्लान की तुलना में 50% बचत';

  @override
  String get prioritySupport => 'प्राथमिकता सहायता';

  @override
  String get save58Percent => '3-महीने के प्लान की तुलना में 58% बचत';

  @override
  String get earlyAccessToNewFeatures => 'नई सुविधाओं तक प्रारंभिक पहुंच';
}
