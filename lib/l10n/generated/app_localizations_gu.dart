// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Gujarati (`gu`).
class AppLocalizationsGu extends AppLocalizations {
  AppLocalizationsGu([String locale = 'gu']) : super(locale);

  @override
  String get appTitle => 'ક્વિક પોસ્ટર્સ';

  @override
  String get welcome => 'ક્વિક પોસ્ટર્સમાં આપનું સ્વાગત છે';

  @override
  String get login => 'લોગિન';

  @override
  String get logout => 'લૉગઆઉટ';

  @override
  String get phoneNumber => 'ફોન નંબર';

  @override
  String get enterPhoneNumber => 'તમારો ફોન નંબર દાખલ કરો';

  @override
  String get sendOTP => 'OTP મોકલો';

  @override
  String get verifyOTP => 'OTP ચકાસો';

  @override
  String get enterOTP => '6-અંકનો OTP દાખલ કરો';

  @override
  String get resendOTP => 'OTP ફરીથી મોકલો';

  @override
  String get home => 'હોમ';

  @override
  String get templates => 'ટેમ્પ્લેટ્સ';

  @override
  String get categories => 'શ્રેણીઓ';

  @override
  String get all => 'બધા';

  @override
  String get premium => 'પ્રીમિયમ';

  @override
  String get profile => 'પ્રોફાઇલ';

  @override
  String get settings => 'સેટિંગ્સ';

  @override
  String get language => 'ભાષા';

  @override
  String get selectLanguage => 'ભાષા પસંદ કરો';

  @override
  String get businessUser => 'વ્યાપાર';

  @override
  String get politicalUser => 'રાજકારણી';

  @override
  String get individualUser => 'વ્યક્તિગત વપરાશકર્તા';

  @override
  String get createPoster => 'પોસ્ટર બનાવો';

  @override
  String get editTemplate => 'ટેમ્પ્લેટ સંપાદિત કરો';

  @override
  String get saveImage => 'છબી સાચવો';

  @override
  String get shareImage => 'ઇમેજ શેર કરો';

  @override
  String get premiumFeature => 'પ્રીમિયમ સુવિધા';

  @override
  String get upgradeNow => 'હવે અપગ્રેડ કરો';

  @override
  String get subscriptionPlans => 'સબ્સ્ક્રિપ્શન પ્લાન';

  @override
  String get trialPlan => 'ટ્રાયલ પ્લાન';

  @override
  String get threeMonthPlan => '3-મહિનાનો પ્લાન';

  @override
  String get sixMonthPlan => '6-મહિનાનો પ્લાન';

  @override
  String get annualPlan => 'વાર્ષિક પ્લાન';

  @override
  String rupees(String amount) {
    return '₹$amount';
  }

  @override
  String get myProfile => 'મારી પ્રોફાઇલ';

  @override
  String get notifications => 'નોટિફિકેશન';

  @override
  String get about => 'વિશે';

  @override
  String get privacyPolicy => 'ગોપનીયતા નીતિ';

  @override
  String get termsOfService => 'સેવાની શરતો';

  @override
  String get refundPolicy => 'રિફંડ નીતિ';

  @override
  String get aiImageGenerator => 'AI ઇમેજ જનરેટર';

  @override
  String get adminDashboard => 'એડમિન ડેશબોર્ડ';

  @override
  String get cancel => 'રદ કરો';

  @override
  String get save => 'સાચવો';

  @override
  String get delete => 'કાઢી નાખો';

  @override
  String get edit => 'સંપાદિત કરો';

  @override
  String get loading => 'લોડ થઈ રહ્યું છે...';

  @override
  String get error => 'ભૂલ';

  @override
  String get success => 'સફળતા';

  @override
  String get tryAgain => 'ફરીથી પ્રયાસ કરો';

  @override
  String get noInternetConnection => 'ઇન્ટરનેટ કનેક્શન નથી';

  @override
  String get somethingWentWrong => 'કંઈક ખોટું થયું';

  @override
  String get phoneNumberValidation => 'કૃપા કરીને માન્ય 10-અંકનો ફોન નંબર દાખલ કરો';

  @override
  String get otpValidation => 'કૃપા કરીને માન્ય 6-અંકનો OTP દાખલ કરો';

  @override
  String get otpSent => 'OTP સફળતાપૂર્વક મોકલાયો';

  @override
  String get otpExpired => 'OTP ની મુદત સમાપ્ત થઈ ગઈ છે. કૃપા કરીને નવો OTP માંગો';

  @override
  String get invalidOtp => 'ખોટો OTP. કૃપા કરીને ફરીથી પ્રયાસ કરો';

  @override
  String get loginSuccessful => 'લોગિન સફળ';

  @override
  String get welcomeBack => 'ફરીથી સ્વાગત છે!';

  @override
  String get getStarted => 'શરૂ કરો';

  @override
  String get continueText => 'ચાલુ રાખો';

  @override
  String get skip => 'છોડો';

  @override
  String get next => 'આગળ';

  @override
  String get previous => 'પાછળ';

  @override
  String get done => 'પૂર્ણ';

  @override
  String get close => 'બંધ કરો';

  @override
  String get back => 'પાછા';

  @override
  String get selectUserType => 'વપરાશકર્તા પ્રકાર પસંદ કરો';

  @override
  String get chooseAccountType => 'તમારો એકાઉન્ટ પ્રકાર પસંદ કરો';

  @override
  String get businessDescription => 'વ્યાપાર વર્ણન';

  @override
  String get politicalDescription => 'રાજકીય ઝુંબેશ અને નેતાઓ માટે આદર્શ';

  @override
  String get individualDescription => 'વ્યક્તિગત ઉપયોગ અને સોશિયલ મીડિયા માટે ઉત્તમ';

  @override
  String get completeProfile => 'તમારી પ્રોફાઇલ પૂર્ણ કરો';

  @override
  String get profileCompletionDescription => 'તમારા અનુભવને વ્યક્તિગત બનાવવામાં અમારી મદદ કરો';

  @override
  String get businessName => 'વ્યાપાર નામ';

  @override
  String get enterBusinessName => 'તમારા વ્યાપારનું નામ દાખલ કરો';

  @override
  String get businessCategory => 'વ્યાપાર કેટેગરી';

  @override
  String get selectBusinessCategory => 'તમારી વ્યાપાર શ્રેણી પસંદ કરો';

  @override
  String get politicalParty => 'રાજકીય પક્ષ';

  @override
  String get selectPoliticalParty => 'તમારો રાજકીય પક્ષ પસંદ કરો';

  @override
  String get designation => 'હોદ્દો';

  @override
  String get enterDesignation => 'તમારો હોદ્દો દાખલ કરો';

  @override
  String get fullName => 'પૂરું નામ';

  @override
  String get enterFullName => 'તમારું પૂરું નામ દાખલ કરો';

  @override
  String get profileUpdated => 'પ્રોફાઇલ સફળતાપૂર્વક અપડેટ થઈ';

  @override
  String get exploreTemplates => 'ટેમ્પ્લેટ્સ અન્વેષણ કરો';

  @override
  String get featuredTemplates => 'વિશેષ ટેમ્પ્લેટ્સ';

  @override
  String get recentTemplates => 'તાજેતરના ટેમ્પ્લેટ્સ';

  @override
  String get popularTemplates => 'લોકપ્રિય ટેમ્પ્લેટ્સ';

  @override
  String get loadingTemplates => 'ટેમ્પ્લેટ્સ લોડ થઈ રહ્યા છે...';

  @override
  String get noTemplatesFound => 'કોઈ ટેમ્પ્લેટ્સ મળ્યા નથી';

  @override
  String get searchTemplates => 'ટેમ્પ્લેટ્સ શોધો...';

  @override
  String get filterByCategory => 'શ્રેણી પ્રમાણે ફિલ્ટર કરો';

  @override
  String get viewAll => 'બધા જુઓ';

  @override
  String get loadMore => 'વધુ લોડ કરો';

  @override
  String get refreshing => 'રિફ્રેશ થઈ રહ્યું છે...';

  @override
  String get pullToRefresh => 'રિફ્રેશ કરવા માટે ખેંચો';

  @override
  String get refresh => 'રીફ્રેશ';

  @override
  String get account => 'ખાતું';

  @override
  String get appSettings => 'એપ સેટિંગ્સ';

  @override
  String get tools => 'સાધનો';

  @override
  String get premiumSubscription => 'પ્રીમિયમ સબ્સ્ક્રિપ્શન';

  @override
  String get youArePremiumUser => 'તમે પ્રીમિયમ વપરાશકર્તા છો';

  @override
  String get upgradeToPremium => 'પ્રીમિયમમાં અપગ્રેડ કરો';

  @override
  String get createImagesWithAI => 'AI સાથે ઇમેજ બનાવો';

  @override
  String get aboutQuickPosters => 'ક્વિક પોસ્ટર્સ વિશે';

  @override
  String get version => 'સંસ્કરણ 1.0.0';

  @override
  String get readPrivacyPolicy => 'અમારી ગોપનીયતા નીતિ વાંચો';

  @override
  String get viewEditProfile => 'તમારી પ્રોફાઇલ જુઓ અને સંપાદિત કરો';

  @override
  String get changeAppLanguage => 'એપની ભાષા બદલો';

  @override
  String get failedToLoadUserData => 'વપરાશકર્તા ડેટા લોડ કરવામાં નિષ્ફળ';

  @override
  String get profileUpdatedSuccessfully => 'પ્રોફાઇલ સફળતાપૂર્વક અપડેટ થઈ';

  @override
  String get failedToSaveProfile => 'પ્રોફાઇલ સાચવવામાં નિષ્ફળ';

  @override
  String get manageBusinessProfile => 'વ્યાપારિક પ્રોફાઇલ મેનેજ કરો';

  @override
  String get managePoliticalProfile => 'રાજકીય પ્રોફાઇલ મેનેજ કરો';

  @override
  String get configurePoliticalDetails => 'તમારી રાજકીય વિગતો, પાર્ટી અને અન્ય પેરામીટર કોન્ફિગર કરો';

  @override
  String get businessParameters => 'વ્યાપારિક પેરામીટર';

  @override
  String get politicalParameters => 'રાજકીય પેરામીટર';

  @override
  String get contentRefreshedSuccessfully => 'સામગ્રી સફળતાપૂર્વક રિફ્રેશ થઈ';

  @override
  String get failedToRefreshContent => 'સામગ્રી રિફ્રેશ કરવામાં નિષ્ફળ';

  @override
  String get mySubscription => 'મારું સબ્સ્ક્રિપ્શન';

  @override
  String get startByAddingBaseImage => 'બેઝ ઇમેજ ઉમેરીને શરૂ કરો';

  @override
  String get backgroundOfPoster => 'આ તમારા પોસ્ટરની પૃષ્ઠભૂમિ હશે';

  @override
  String get selectBaseImage => 'બેઝ ઇમેજ પસંદ કરો';

  @override
  String get addBaseImage => 'બેઝ ઇમેજ ઉમેરો';

  @override
  String get aboutPageComingSoon => 'વિશે પેજ ટૂંક સમયમાં આવી રહ્યું છે';

  @override
  String get languageSettingsComingSoon => 'ભાષા સેટિંગ્સ ટૂંક સમયમાં આવી રહી છે';

  @override
  String get notificationSettingsComingSoon => 'નોટિફિકેશન સેટિંગ્સ ટૂંક સમયમાં આવી રહી છે';

  @override
  String get manageNotificationSettings => 'નોટિફિકેશન સેટિંગ્સ મેનેજ કરો';

  @override
  String get imageShape => 'ઇમેજ આકાર';

  @override
  String get businessLogoShape => 'વ્યાપારિક લોગો આકાર';

  @override
  String get profilePhoto => 'પ્રોફાઇલ ફોટો';

  @override
  String get businessLogo => 'વ્યાપાર લોગો';

  @override
  String get pleaseSelectUserType => 'કૃપા કરીને વપરાશકર્તા પ્રકાર પસંદ કરો';

  @override
  String get failedToLoadUserTypes => 'વપરાશકર્તા પ્રકાર લોડ કરવામાં નિષ્ફળ';

  @override
  String get failedToSaveUserType => 'વપરાશકર્તા પ્રકાર સાચવવામાં નિષ્ફળ';

  @override
  String get profileCompletedSuccessfully => 'પ્રોફાઇલ સફળતાપૂર્વક પૂર્ણ થઈ';

  @override
  String get viewEditProfileInfo => 'તમારી પ્રોફાઇલ માહિતી જુઓ અને સંપાદિત કરો';

  @override
  String get chooseImageSource => 'ઇમેજ સ્રોત પસંદ કરો';

  @override
  String get customImage => 'કસ્ટમ ઇમેજ';

  @override
  String get autoSelect => 'સ્વચાલિત પસંદગી';

  @override
  String get imageSource => 'ઇમેજ સ્રોત';

  @override
  String get shapeOptions => 'આકાર વિકલ્પો';

  @override
  String get none => 'કોઈ નહીં';

  @override
  String get circle => 'વર્તુળ';

  @override
  String get rectangle => 'લંબચોરસ';

  @override
  String get roundedRectangle => 'ગોળાકાર લંબચોરસ';

  @override
  String get diamond => 'હીરો';

  @override
  String get hexagon => 'ષટકોણ';

  @override
  String get quickPosters => 'ક્વિક પોસ્ટર્સ';

  @override
  String get accountActions => 'ખાતા ક્રિયાઓ';

  @override
  String get signOutFromAccount => 'તમારા ખાતામાંથી સાઇન આઉટ કરો';

  @override
  String get changingLanguage => 'ભાષા બદલાઈ રહી છે...';

  @override
  String get phoneNumberLabel => 'ફોન નંબર';

  @override
  String get enterYourPhoneNumber => 'તમારો ફોન નંબર દાખલ કરો';

  @override
  String get userType => 'વપરાશકર્તા પ્રકાર';

  @override
  String get saveToGallery => 'ગેલેરીમાં સાચવો';

  @override
  String get share => 'શેર કરો';

  @override
  String get whatsApp => 'વ્હાટ્સએપ';

  @override
  String get invalidPhoneNumber => 'ફોન નંબરનું ફોર્મેટ ખોટું છે. કૃપા કરીને માન્ય ફોન નંબર દાખલ કરો.';

  @override
  String get quotaExceeded => 'SMS કોટા સમાપ્ત થઈ ગયો છે. કૃપા કરીને પછીથી ફરી પ્રયાસ કરો.';

  @override
  String get userDisabled => 'આ વપરાશકર્તાને અક્ષમ કરવામાં આવ્યો છે. કૃપા કરીને સહાયતાનો સંપર્ક કરો.';

  @override
  String get operationNotAllowed => 'ફોન પ્રમાણીકરણ સક્ષમ નથી. કૃપા કરીને સહાયતાનો સંપર્ક કરો.';

  @override
  String get captchaCheckFailed => 'reCAPTCHA ચકાસણી નિષ્ફળ ગઈ. કૃપા કરીને ફરી પ્રયાસ કરો.';

  @override
  String get missingClientIdentifier => 'એપ ચકાસણી પ્રક્રિયા નિષ્ફળ ગઈ. કૃપા કરીને ફરી પ્રયાસ કરો અથવા સહાયતાનો સંપર્ક કરો.';

  @override
  String get tooManyRequests => 'આ ઉપકરણથી ઘણી બધી વિનંતીઓ. અસામાન્ય પ્રવૃત્તિને કારણે અમે આ ઉપકરણની બધી વિનંતીઓને અસ્થાયી રૂપે અવરોધિત કરી છે. કૃપા કરીને થોડા સમય પછી ફરી પ્રયાસ કરો (સામાન્ય રીતે થોડા કલાક).';

  @override
  String get verificationFailed => 'ચકાસણી નિષ્ફળ ગઈ. કૃપા કરીને ફરી પ્રયાસ કરો.';

  @override
  String get failedToGetUserData => 'વપરાશકર્તા ડેટા મેળવવામાં નિષ્ફળ';

  @override
  String get userNotFound => 'વપરાશકર્તા મળ્યો નથી';

  @override
  String get politician => 'રાજકારણી';

  @override
  String get businessman => 'વ્યાપારી';

  @override
  String get regularUser => 'નિયમિત';

  @override
  String get forPoliticalCampaigns => 'રાજકીય ઝુંબેશ અને પ્રચાર માટે';

  @override
  String get forBusinessPromotions => 'વ્યાપારિક પ્રચાર અને જાહેરાતો માટે';

  @override
  String get forPersonalUse => 'વ્યક્તિગત ઉપયોગ અને સામાન્ય હેતુઓ માટે';

  @override
  String get developmentLogin => 'ડેવલપમેન્ટ લોગિન';

  @override
  String get developmentMode => 'ડેવલપમેન્ટ મોડ';

  @override
  String get loggedInAsDeveloper => 'તમે ડેવલપર તરીકે લોગિન કર્યું છે.\nFirebase ફોન પ્રમાણીકરણ અવરોધિત હોય ત્યારે આ અસ્થાયી મોડ છે.';

  @override
  String get noActiveSubscription => 'કોઈ સક્રિય સબ્સ્ક્રિપ્શન નથી';

  @override
  String expiresInDays(int days) {
    return '$days દિવસમાં સમાપ્ત થાય છે';
  }

  @override
  String activeUntil(String date) {
    return '$date સુધી સક્રિય';
  }

  @override
  String get downloadImage => 'ઇમેજ ડાઉનલોડ કરો';

  @override
  String get shareOnWhatsApp => 'વ્હાટ્સએપ પર શેર કરો';

  @override
  String get customImageSelected => 'કસ્ટમ ઇમેજ પસંદ કરવામાં આવી!';

  @override
  String errorSelectingImage(String error) {
    return 'ઇમેજ પસંદ કરવામાં ભૂલ: $error';
  }

  @override
  String get imageAddedSuccessfully => 'ઇમેજ સફળતાપૂર્વક ઉમેરી!';

  @override
  String errorAddingImage(String error) {
    return 'ઇમેજ ઉમેરવામાં ભૂલ: $error';
  }

  @override
  String noImageAvailable(String source) {
    return 'કોઈ $source ઇમેજ ઉપલબ્ધ નથી';
  }

  @override
  String get emailAddress => 'ઇમેઇલ સરનામું';

  @override
  String get enterEmailAddress => 'તમારું ઇમેઇલ સરનામું દાખલ કરો';

  @override
  String get optional => 'વૈકલ્પિક';

  @override
  String get selectProfileImage => 'પ્રોફાઇલ ઇમેજ પસંદ કરો';

  @override
  String get camera => 'કેમેરા';

  @override
  String get gallery => 'ગેલેરી';

  @override
  String get removeImage => 'ઇમેજ દૂર કરો';

  @override
  String get saveProfile => 'પ્રોફાઇલ સાચવો';

  @override
  String get updateProfile => 'પ્રોફાઇલ અપડેટ કરો';

  @override
  String get profileImageUpdated => 'પ્રોફાઇલ ઇમેજ સફળતાપૂર્વક અપડેટ થઈ';

  @override
  String get failedToUpdateProfileImage => 'પ્રોફાઇલ ઇમેજ અપડેટ કરવામાં નિષ્ફળ';

  @override
  String get pleaseEnterValidEmail => 'કૃપા કરીને માન્ય ઇમેઇલ સરનામું દાખલ કરો';

  @override
  String get pleaseEnterName => 'કૃપા કરીને તમારું નામ દાખલ કરો';

  @override
  String get nameTooShort => 'નામ ઓછામાં ઓછા 2 અક્ષરનું હોવું જોઈએ';

  @override
  String get invalidEmailFormat => 'કૃપા કરીને માન્ય ઇમેઇલ ફોર્મેટ દાખલ કરો';

  @override
  String get business => 'વ્યાપાર';

  @override
  String get event => 'ઇવેન્ટ';

  @override
  String get festival => 'તહેવાર';

  @override
  String get party => 'પાર્ટી';

  @override
  String get education => 'શિક્ષણ';

  @override
  String get health => 'આરોગ્ય';

  @override
  String get technology => 'ટેકનોલોજી';

  @override
  String get food => 'ખોરાક';

  @override
  String get travel => 'પ્રવાસ';

  @override
  String get sports => 'રમતગમત';

  @override
  String get fashion => 'ફેશન';

  @override
  String get realEstate => 'રિયલ એસ્ટેટ';

  @override
  String get flyers => 'ફ્લાયર્સ';

  @override
  String get brochures => 'બ્રોશર';

  @override
  String get businessCards => 'બિઝનેસ કાર્ડ';

  @override
  String get presentations => 'પ્રેઝન્ટેશન';

  @override
  String get logos => 'લોગો';

  @override
  String get invitations => 'આમંત્રણ';

  @override
  String get tickets => 'ટિકિટ';

  @override
  String get programs => 'પ્રોગ્રામ';

  @override
  String get banners => 'બેનર';

  @override
  String get posters => 'પોસ્ટર';

  @override
  String get celebration => 'ઉજવણી';

  @override
  String get religious => 'ધાર્મિક';

  @override
  String get cultural => 'સાંસ્કૃતિક';

  @override
  String get seasonal => 'મોસમી';

  @override
  String get birthday => 'જન્મદિવસ';

  @override
  String get wedding => 'લગ્ન';

  @override
  String get anniversary => 'વર્ષગાંઠ';

  @override
  String get graduation => 'સ્નાતક';

  @override
  String get certificates => 'પ્રમાણપત્ર';

  @override
  String get worksheets => 'વર્કશીટ';

  @override
  String get awareness => 'જાગૃતિ';

  @override
  String get medical => 'તબીબી';

  @override
  String get fitness => 'ફિટનેસ';

  @override
  String get nutrition => 'પોષણ';

  @override
  String get appPromotion => 'એપ પ્રમોશન';

  @override
  String get software => 'સોફ્ટવેર';

  @override
  String get digitalServices => 'ડિજિટલ સેવાઓ';

  @override
  String get menu => 'મેનુ';

  @override
  String get recipe => 'રેસિપી';

  @override
  String get restaurant => 'રેસ્ટોરન્ટ';

  @override
  String get catering => 'કેટરિંગ';

  @override
  String get tourism => 'પર્યટન';

  @override
  String get hotels => 'હોટેલ';

  @override
  String get transportation => 'પરિવહન';

  @override
  String get adventure => 'સાહસ';

  @override
  String get team => 'ટીમ';

  @override
  String get tournament => 'ટુર્નામેન્ટ';

  @override
  String get equipment => 'સાધનો';

  @override
  String get clothing => 'કપડાં';

  @override
  String get accessories => 'એક્સેસરીઝ';

  @override
  String get beauty => 'સુંદરતા';

  @override
  String get style => 'શૈલી';

  @override
  String get property => 'મિલકત';

  @override
  String get rental => 'ભાડું';

  @override
  String get commercial => 'વ્યાપારિક';

  @override
  String get residential => 'રહેણાંક';

  @override
  String get noShape => 'કોઈ આકાર નથી';

  @override
  String get rounded => 'ગોળાકાર';

  @override
  String get loadMoreTemplates => 'વધુ ટેમ્પ્લેટ્સ લોડ કરો';

  @override
  String get horizontal => 'આડું';

  @override
  String get vertical => 'ઊભું';

  @override
  String get square => 'ચોરસ';

  @override
  String get smallBusiness => 'નાનો વ્યાપાર';

  @override
  String get eventOrganizers => 'ઇવેન્ટ આયોજકો';

  @override
  String get educators => 'શિક્ષકો';

  @override
  String get marketers => 'માર્કેટર્સ';

  @override
  String get students => 'વિદ્યાર્થીઓ';

  @override
  String get professionals => 'વ્યાવસાયિકો';

  @override
  String get generalPublic => 'સામાન્ય જનતા';

  @override
  String get modern => 'આધુનિક';

  @override
  String get minimalist => 'ન્યૂનતમ';

  @override
  String get retro => 'રેટ્રો';

  @override
  String get elegant => 'સુંદર';

  @override
  String get creative => 'સર્જનાત્મક';

  @override
  String get professional => 'વ્યાવસાયિક';

  @override
  String get playful => 'રમતિયાળ';

  @override
  String get print => 'પ્રિન્ટ';

  @override
  String get socialMedia => 'સોશિયલ મીડિયા';

  @override
  String get onlineAdvertising => 'ઓનલાઇન જાહેરાત';

  @override
  String get email => 'ઇમેઇલ';

  @override
  String get web => 'વેબ';

  @override
  String get high => 'ઉચ્ચ';

  @override
  String get medium => 'મધ્યમ';

  @override
  String get low => 'નીચું';

  @override
  String get freeForPersonalUse => 'વ્યક્તિગત ઉપયોગ માટે મફત';

  @override
  String get commercialUseAllowed => 'વ્યાપારિક ઉપયોગની મંજૂરી';

  @override
  String get premiumLicenseRequired => 'પ્રીમિયમ લાઇસન્સ જરૂરી';

  @override
  String get templateName => 'ટેમ્પ્લેટ નામ';

  @override
  String get templateDescription => 'ટેમ્પ્લેટ વર્ણન';

  @override
  String get creator => 'સર્જક';

  @override
  String get dimensions => 'પરિમાણો';

  @override
  String get tags => 'ટૅગ્સ';

  @override
  String get colors => 'રંગો';

  @override
  String get fontStyles => 'ફોન્ટ શૈલીઓ';

  @override
  String get fileFormats => 'ફાઇલ ફોર્મેટ્સ';

  @override
  String get category => 'કેટેગરી';

  @override
  String get subCategory => 'સબ કેટેગરી';

  @override
  String get layoutType => 'લેઆઉટ પ્રકાર';

  @override
  String get targetAudience => 'લક્ષિત પ્રેક્ષકો';

  @override
  String get designStyle => 'ડિઝાઇન શૈલી';

  @override
  String get usageType => 'ઉપયોગ પ્રકાર';

  @override
  String get resolution => 'રિઝોલ્યુશન';

  @override
  String get licenseType => 'લાઇસન્સ પ્રકાર';

  @override
  String get isPremium => 'પ્રીમિયમ છે';

  @override
  String get isActive => 'સક્રિય છે';

  @override
  String get selectImage => 'ઇમેજ પસંદ કરો';

  @override
  String get uploadTemplate => 'ટેમ્પ્લેટ અપલોડ કરો';

  @override
  String get updateTemplate => 'ટેમ્પ્લેટ અપડેટ કરો';

  @override
  String get uploading => 'અપલોડ થઈ રહ્યું છે...';

  @override
  String get templateUploadedSuccessfully => 'ટેમ્પ્લેટ સફળતાપૂર્વક અપલોડ થયું';

  @override
  String get templateUpdatedSuccessfully => 'ટેમ્પ્લેટ સફળતાપૂર્વક અપડેટ થયું';

  @override
  String get failedToUploadTemplate => 'ટેમ્પ્લેટ અપલોડ કરવામાં નિષ્ફળ';

  @override
  String get failedToUpdateTemplate => 'ટેમ્પ્લેટ અપડેટ કરવામાં નિષ્ફળ';

  @override
  String get pleaseSelectImage => 'કૃપા કરીને એક ઇમેજ પસંદ કરો';

  @override
  String get pleaseEnterTemplateName => 'કૃપા કરીને ટેમ્પ્લેટ નામ દાખલ કરો';

  @override
  String get pleaseSelectCategory => 'કૃપા કરીને એક કેટેગરી પસંદ કરો';

  @override
  String get loadingBanners => 'બેનર લોડ થઈ રહ્યા છે...';

  @override
  String get loadingMoreBanners => 'વધુ બેનર લોડ થઈ રહ્યા છે...';

  @override
  String get errorLoadingBanners => 'બેનર લોડ કરવામાં ભૂલ';

  @override
  String get noBannersFound => 'કોઈ બેનર મળ્યા નથી';

  @override
  String get selectBanner => 'બેનર પસંદ કરો';

  @override
  String get customizeTemplate => 'ટેમ્પ્લેટ કસ્ટમાઇઝ કરો';

  @override
  String get imageCustomization => 'ઇમેજ કસ્ટમાઇઝેશન';

  @override
  String get bannerCustomization => 'બેનર કસ્ટમાઇઝેશન';

  @override
  String get textCustomization => 'ટેક્સ્ટ કસ્ટમાઇઝેશન';

  @override
  String get previewTemplate => 'ટેમ્પ્લેટ પ્રીવ્યુ';

  @override
  String get saveTemplate => 'ટેમ્પ્લેટ સેવ કરો';

  @override
  String get downloadTemplate => 'ટેમ્પ્લેટ ડાઉનલોડ કરો';

  @override
  String get shareTemplate => 'ટેમ્પ્લેટ શેર કરો';

  @override
  String get templateSavedSuccessfully => 'ટેમ્પ્લેટ સફળતાપૂર્વક સેવ થયું';

  @override
  String get templateDownloadedSuccessfully => 'ટેમ્પ્લેટ સફળતાપૂર્વક ડાઉનલોડ થયું';

  @override
  String get failedToSaveTemplate => 'ટેમ્પ્લેટ સેવ કરવામાં નિષ્ફળ';

  @override
  String get failedToDownloadTemplate => 'ટેમ્પ્લેટ ડાઉનલોડ કરવામાં નિષ્ફળ';

  @override
  String get processingImage => 'ઇમેજ પ્રોસેસિંગ થઈ રહી છે...';

  @override
  String get generatingPreview => 'પ્રીવ્યુ જનરેટ થઈ રહ્યું છે...';

  @override
  String get preparingDownload => 'ડાઉનલોડ તૈયાર થઈ રહ્યું છે...';

  @override
  String get manageBusiness => 'વ્યાપાર વ્યવસ્થાપન';

  @override
  String get configureBusinessDetails => 'તમારી વ્યાપારિક વિગતો, સરનામું અને અન્ય પેરામીટર કોન્ફિગર કરો';

  @override
  String get managePolitical => 'રાજકીય વ્યવસ્થાપન';

  @override
  String get failedToLoadBusinessProfile => 'વ્યાપારિક પ્રોફાઇલ લોડ કરવામાં નિષ્ફળ';

  @override
  String get failedToLoadPoliticalProfile => 'રાજકીય પ્રોફાઇલ લોડ કરવામાં નિષ્ફળ';

  @override
  String get noBusinessParametersAvailable => 'કોઈ વ્યાપારિક પેરામીટર ઉપલબ્ધ નથી';

  @override
  String get businessParametersConfiguredByAdmin => 'વ્યાપારિક પેરામીટર એડમિન દ્વારા કોન્ફિગર કરવામાં આવશે';

  @override
  String get noPoliticalParametersAvailable => 'કોઈ રાજકીય પેરામીટર ઉપલબ્ધ નથી';

  @override
  String get politicalParametersConfiguredByAdmin => 'રાજકીય પેરામીટર એડમિન દ્વારા કોન્ફિગર કરવામાં આવશે';

  @override
  String get saveBusinessParameters => 'વ્યાપારિક પેરામીટર સેવ કરો';

  @override
  String get savePoliticalParameters => 'રાજકીય પેરામીટર સેવ કરો';

  @override
  String get businessParametersSavedSuccessfully => 'વ્યાપારિક પેરામીટર સફળતાપૂર્વક સેવ થયા';

  @override
  String get politicalParametersSavedSuccessfully => 'રાજકીય પેરામીટર સફળતાપૂર્વક સેવ થયા';

  @override
  String get failedToSaveBusinessParameters => 'વ્યાપારિક પેરામીટર સેવ કરવામાં નિષ્ફળ';

  @override
  String get failedToSavePoliticalParameters => 'રાજકીય પેરામીટર સેવ કરવામાં નિષ્ફળ';

  @override
  String get businessType => 'વ્યાપાર પ્રકાર';

  @override
  String get businessAddress => 'વ્યાપાર સરનામું';

  @override
  String get businessLocation => 'વ્યાપાર સ્થાન';

  @override
  String get businessMobile => 'વ્યાપાર મોબાઇલ';

  @override
  String get businessWebsite => 'વ્યાપાર વેબસાઇટ';

  @override
  String get businessEmail => 'વ્યાપાર ઇમેઇલ';

  @override
  String get selectBusinessLogo => 'વ્યાપાર લોગો પસંદ કરો';

  @override
  String get politicalPhoto => 'રાજકીય ફોટો';

  @override
  String get selectPoliticalPhoto => 'રાજકીય ફોટો પસંદ કરો';

  @override
  String get partyName => 'પાર્ટીનું નામ';

  @override
  String get politicalPosition => 'રાજકીય પદ';

  @override
  String get constituency => 'મતવિસ્તાર';

  @override
  String get politicalExperience => 'રાજકીય અનુભવ';

  @override
  String get campaignSlogan => 'પ્રચાર સૂત્ર';

  @override
  String get politicalAchievements => 'રાજકીય સિદ્ધિઓ';

  @override
  String get isRequired => 'જરૂરી છે';

  @override
  String get pleaseEnterValidNumber => 'કૃપા કરીને માન્ય નંબર દાખલ કરો';

  @override
  String get allTypes => 'બધા પ્રકાર';

  @override
  String get removePremium => 'પ્રીમિયમ દૂર કરો';

  @override
  String get makePremium => 'પ્રીમિયમ બનાવો';

  @override
  String get deleteUser => 'વપરાશકર્તા ડિલીટ કરો';

  @override
  String get userManagement => 'વપરાશકર્તા વ્યવસ્થાપન';

  @override
  String get searchUsers => 'વપરાશકર્તાઓ શોધો...';

  @override
  String get noUsersFound => 'કોઈ વપરાશકર્તા મળ્યા નથી';

  @override
  String get loadingUsers => 'વપરાશકર્તાઓ લોડ થઈ રહ્યા છે...';

  @override
  String get failedToLoadUsers => 'વપરાશકર્તાઓ લોડ કરવામાં નિષ્ફળ';

  @override
  String get userDetails => 'વપરાશકર્તા વિગતો';

  @override
  String get accountCreated => 'એકાઉન્ટ બનાવ્યું';

  @override
  String get lastUpdated => 'છેલ્લું અપડેટ';

  @override
  String get profileComplete => 'પ્રોફાઇલ પૂર્ણ';

  @override
  String get profileIncomplete => 'પ્રોફાઇલ અધૂરી';

  @override
  String get premiumStatus => 'પ્રીમિયમ સ્થિતિ';

  @override
  String get active => 'સક્રિય';

  @override
  String get inactive => 'નિષ્ક્રિય';

  @override
  String get subscriptionPlan => 'સબ્સ્ક્રિપ્શન પ્લાન';

  @override
  String get subscriptionEndDate => 'સબ્સ્ક્રિપ્શન સમાપ્તિ તારીખ';

  @override
  String get languagePreference => 'ભાષા પસંદગી';

  @override
  String get totalPosters => 'કુલ પોસ્ટર';

  @override
  String get adminStatus => 'એડમિન સ્થિતિ';

  @override
  String get isAdmin => 'એડમિન';

  @override
  String get isNotAdmin => 'એડમિન નથી';

  @override
  String get allUsers => 'બધા વપરાશકર્તાઓ';

  @override
  String get free => 'મફત';

  @override
  String get retry => 'ફરી પ્રયાસ કરો';

  @override
  String get tryAdjustingFilters => 'તમારી શોધ અથવા ફિલ્ટર સમાયોજિત કરવાનો પ્રયાસ કરો';

  @override
  String get notProvided => 'પ્રદાન કરવામાં આવ્યું નથી';

  @override
  String get yes => 'હા';

  @override
  String get no => 'ના';

  @override
  String get created => 'બનાવ્યું';

  @override
  String get name => 'નામ';

  @override
  String get userPremiumStatusRemoved => 'વપરાશકર્તા પ્રીમિયમ સ્થિતિ દૂર કરવામાં આવી';

  @override
  String get userUpgradedToPremium => 'વપરાશકર્તા પ્રીમિયમમાં અપગ્રેડ થયો';

  @override
  String get errorUpdatingUser => 'વપરાશકર્તા અપડેટ કરવામાં ભૂલ';

  @override
  String areYouSureDeleteUser(String userName) {
    return 'શું તમે ખરેખર $userName ને ડિલીટ કરવા માંગો છો? આ ક્રિયા પૂર્વવત્ કરી શકાશે નહીં.';
  }

  @override
  String get userDeletedSuccessfully => 'વપરાશકર્તા સફળતાપૂર્વક ડિલીટ થયો';

  @override
  String get errorDeletingUser => 'વપરાશકર્તા ડિલીટ કરવામાં ભૂલ';

  @override
  String get add => 'ઉમેરો';

  @override
  String get extend => 'વધારો';

  @override
  String subscriptionExtended(String duration) {
    return 'સબ્સ્ક્રિપ્શન $duration વધારવામાં આવ્યું!';
  }

  @override
  String failedToExtendSubscription(String error) {
    return 'સબ્સ્ક્રિપ્શન વધારવામાં નિષ્ફળ: $error';
  }

  @override
  String failedToPurchaseSubscription(String error) {
    return 'સબ્સ્ક્રિપ્શન ખરીદવામાં નિષ્ફળ: $error';
  }

  @override
  String get failedToRestorePurchases => 'ખરીદીઓ પુનઃસ્થાપિત કરવામાં નિષ્ફળ';

  @override
  String get trialPlanDescription => 'ન્યૂનતમ ખર્ચે 1 અઠવાડિયા માટે પ્રીમિયમ સુવિધાઓ અજમાવો';

  @override
  String get threeMonthPlanDescription => 'ટૂંકા ગાળાના પ્રોજેક્ટ્સ માટે યોગ્ય';

  @override
  String get sixMonthPlanDescription => 'મધ્યમ ગાળાના ઉપયોગ માટે શ્રેષ્ઠ મૂલ્ય';

  @override
  String get annualPlanDescription => 'નિયમિત વપરાશકર્તાઓ માટે શ્રેષ્ઠ મૂલ્ય';

  @override
  String get allPremiumFeatures => 'બધી પ્રીમિયમ સુવિધાઓ';

  @override
  String get autoRenewsAt => '3 મહિના માટે ₹199 પર ઓટો-રિન્યૂ';

  @override
  String get cancelAnytime => 'કોઈપણ સમયે રદ કરો';

  @override
  String get quarterlyBilling => 'ત્રિમાસિક બિલિંગ';

  @override
  String get save50Percent => '3-મહિનાના પ્લાનની સરખામણીમાં 50% બચત';

  @override
  String get prioritySupport => 'પ્રાથમિકતા સહાય';

  @override
  String get save58Percent => '3-મહિનાના પ્લાનની સરખામણીમાં 58% બચત';

  @override
  String get earlyAccessToNewFeatures => 'નવી સુવિધાઓમાં પ્રારંભિક પ્રવેશ';
}
