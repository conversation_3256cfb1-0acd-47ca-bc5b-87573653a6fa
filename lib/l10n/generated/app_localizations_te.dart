// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Telugu (`te`).
class AppLocalizationsTe extends AppLocalizations {
  AppLocalizationsTe([String locale = 'te']) : super(locale);

  @override
  String get appTitle => 'క్విక్ పోస్టర్స్';

  @override
  String get welcome => 'క్విక్ పోస్టర్స్‌కు స్వాగతం';

  @override
  String get login => 'లాగిన్';

  @override
  String get logout => 'లాగ్అవుట్';

  @override
  String get phoneNumber => 'ఫోన్ నంబర్';

  @override
  String get enterPhoneNumber => 'మీ ఫోన్ నంబర్‌ను నమోదు చేయండి';

  @override
  String get sendOTP => 'OTP పంపండి';

  @override
  String get verifyOTP => 'OTP ధృవీకరించండి';

  @override
  String get enterOTP => '6-అంకెల OTP నమోదు చేయండి';

  @override
  String get resendOTP => 'OTP మళ్లీ పంపండి';

  @override
  String get home => 'హోమ్';

  @override
  String get templates => 'టెంప్లేట్‌లు';

  @override
  String get categories => 'వర్గాలు';

  @override
  String get all => 'అన్నీ';

  @override
  String get premium => 'ప్రీమియం';

  @override
  String get profile => 'ప్రొఫైల్';

  @override
  String get settings => 'సెట్టింగ్‌లు';

  @override
  String get language => 'భాష';

  @override
  String get selectLanguage => 'భాషను ఎంచుకోండి';

  @override
  String get businessUser => 'వ్యాపారం';

  @override
  String get politicalUser => 'రాజకీయవేత్త';

  @override
  String get individualUser => 'వ్యక్తిగత వినియోగదారు';

  @override
  String get createPoster => 'పోస్టర్ సృష్టించండి';

  @override
  String get editTemplate => 'టెంప్లేట్‌ను సవరించండి';

  @override
  String get saveImage => 'చిత్రాన్ని సేవ్ చేయండి';

  @override
  String get shareImage => 'ఇమేజ్‌ను షేర్ చేయండి';

  @override
  String get premiumFeature => 'ప్రీమియం ఫీచర్';

  @override
  String get upgradeNow => 'ఇప్పుడే అప్‌గ్రేడ్ చేయండి';

  @override
  String get subscriptionPlans => 'సబ్‌స్క్రిప్షన్ ప్లాన్‌లు';

  @override
  String get trialPlan => 'ట్రయల్ ప్లాన్';

  @override
  String get threeMonthPlan => '3-నెలల ప్లాన్';

  @override
  String get sixMonthPlan => '6-నెలల ప్లాన్';

  @override
  String get annualPlan => 'వార్షిక ప్లాన్';

  @override
  String rupees(String amount) {
    return '₹$amount';
  }

  @override
  String get myProfile => 'నా ప్రొఫైల్';

  @override
  String get notifications => 'నోటిఫికేషన్‌లు';

  @override
  String get about => 'గురించి';

  @override
  String get privacyPolicy => 'గోప్యతా విధానం';

  @override
  String get termsOfService => 'సేవా నిబంధనలు';

  @override
  String get refundPolicy => 'రీఫండ్ విధానం';

  @override
  String get aiImageGenerator => 'AI ఇమేజ్ జనరేటర్';

  @override
  String get adminDashboard => 'అడ్మిన్ డాష్‌బోర్డ్';

  @override
  String get cancel => 'రద్దు చేయండి';

  @override
  String get save => 'సేవ్ చేయండి';

  @override
  String get delete => 'తొలగించండి';

  @override
  String get edit => 'సవరించు';

  @override
  String get loading => 'లోడ్ అవుతోంది...';

  @override
  String get error => 'లోపం';

  @override
  String get success => 'విజయం';

  @override
  String get tryAgain => 'మళ్లీ ప్రయత్నించండి';

  @override
  String get noInternetConnection => 'ఇంటర్నెట్ కనెక్షన్ లేదు';

  @override
  String get somethingWentWrong => 'ఏదో తప్పు జరిగింది';

  @override
  String get phoneNumberValidation => 'దయచేసి చెల్లుబాటు అయ్యే 10-అంకెల ఫోన్ నంబర్‌ను నమోదు చేయండి';

  @override
  String get otpValidation => 'దయచేసి చెల్లుబాటు అయ్యే 6-అంకెల OTP నమోదు చేయండి';

  @override
  String get otpSent => 'OTP విజయవంతంగా పంపబడింది';

  @override
  String get otpExpired => 'OTP గడువు ముగిసింది. దయచేసి కొత్త OTP అడగండి';

  @override
  String get invalidOtp => 'తప్పు OTP. దయచేసి మళ్లీ ప్రయత్నించండి';

  @override
  String get loginSuccessful => 'లాగిన్ విజయవంతం';

  @override
  String get welcomeBack => 'మళ్లీ స్వాగతం!';

  @override
  String get getStarted => 'ప్రారంభించండి';

  @override
  String get continueText => 'కొనసాగించండి';

  @override
  String get skip => 'దాటవేయండి';

  @override
  String get next => 'తదుపరి';

  @override
  String get previous => 'మునుపటి';

  @override
  String get done => 'పూర్తయింది';

  @override
  String get close => 'మూసివేయండి';

  @override
  String get back => 'వెనుకకు';

  @override
  String get selectUserType => 'వినియోగదారు రకాన్ని ఎంచుకోండి';

  @override
  String get chooseAccountType => 'మీ ఖాతా రకాన్ని ఎంచుకోండి';

  @override
  String get businessDescription => 'వ్యాపార వివరణ';

  @override
  String get politicalDescription => 'రాజకీయ ప్రచారాలు మరియు నాయకులకు అనువైనది';

  @override
  String get individualDescription => 'వ్యక్తిగత వాడుక మరియు సోషల్ మీడియాకు గొప్పది';

  @override
  String get completeProfile => 'మీ ప్రొఫైల్‌ను పూర్తి చేయండి';

  @override
  String get profileCompletionDescription => 'మీ అనుభవాన్ని వ్యక్తిగతీకరించడంలో మాకు సహాయం చేయండి';

  @override
  String get businessName => 'వ్యాపార పేరు';

  @override
  String get enterBusinessName => 'మీ వ్యాపార పేరును నమోదు చేయండి';

  @override
  String get businessCategory => 'వ్యాపార వర్గం';

  @override
  String get selectBusinessCategory => 'మీ వ్యాపార వర్గాన్ని ఎంచుకోండి';

  @override
  String get politicalParty => 'రాజకీయ పార్టీ';

  @override
  String get selectPoliticalParty => 'మీ రాజకీయ పార్టీని ఎంచుకోండి';

  @override
  String get designation => 'హోదా';

  @override
  String get enterDesignation => 'మీ హోదాను నమోదు చేయండి';

  @override
  String get fullName => 'పూర్తి పేరు';

  @override
  String get enterFullName => 'మీ పూర్తి పేరును నమోదు చేయండి';

  @override
  String get profileUpdated => 'ప్రొఫైల్ విజయవంతంగా అప్‌డేట్ చేయబడింది';

  @override
  String get exploreTemplates => 'టెంప్లేట్‌లను అన్వేషించండి';

  @override
  String get featuredTemplates => 'ప్రత్యేక టెంప్లేట్‌లు';

  @override
  String get recentTemplates => 'ఇటీవలి టెంప్లేట్‌లు';

  @override
  String get popularTemplates => 'ప్రసిద్ధ టెంప్లేట్‌లు';

  @override
  String get loadingTemplates => 'టెంప్లేట్‌లు లోడ్ అవుతున్నాయి...';

  @override
  String get noTemplatesFound => 'టెంప్లేట్‌లు ఏవీ కనుగొనబడలేదు';

  @override
  String get searchTemplates => 'టెంప్లేట్‌లను వెతకండి...';

  @override
  String get filterByCategory => 'వర్గం ఆధారంగా ఫిల్టర్ చేయండి';

  @override
  String get viewAll => 'అన్నీ చూడండి';

  @override
  String get loadMore => 'మరిన్ని లోడ్ చేయండి';

  @override
  String get refreshing => 'రిఫ్రెష్ అవుతోంది...';

  @override
  String get pullToRefresh => 'రిఫ్రెష్ చేయడానికి లాగండి';

  @override
  String get refresh => 'రిఫ్రెష్';

  @override
  String get account => 'ఖాతా';

  @override
  String get appSettings => 'యాప్ సెట్టింగ్‌లు';

  @override
  String get tools => 'సాధనాలు';

  @override
  String get premiumSubscription => 'ప్రీమియం సబ్‌స్క్రిప్షన్';

  @override
  String get youArePremiumUser => 'మీరు ప్రీమియం వినియోగదారు';

  @override
  String get upgradeToPremium => 'ప్రీమియంకు అప్‌గ్రేడ్ చేయండి';

  @override
  String get createImagesWithAI => 'AI తో ఇమేజ్‌లను సృష్టించండి';

  @override
  String get aboutQuickPosters => 'క్విక్ పోస్టర్స్ గురించి';

  @override
  String get version => 'వెర్షన్ 1.0.0';

  @override
  String get readPrivacyPolicy => 'మా గోప్యతా విధానాన్ని చదవండి';

  @override
  String get viewEditProfile => 'మీ ప్రొఫైల్‌ను చూడండి మరియు సవరించండి';

  @override
  String get changeAppLanguage => 'యాప్ భాషను మార్చండి';

  @override
  String get failedToLoadUserData => 'వినియోగదారు డేటాను లోడ్ చేయడంలో విఫలమైంది';

  @override
  String get profileUpdatedSuccessfully => 'ప్రొఫైల్ విజయవంతంగా అప్‌డేట్ చేయబడింది';

  @override
  String get failedToSaveProfile => 'ప్రొఫైల్‌ను సేవ్ చేయడంలో విఫలమైంది';

  @override
  String get manageBusinessProfile => 'వ్యాపార ప్రొఫైల్‌ను నిర్వహించండి';

  @override
  String get managePoliticalProfile => 'రాజకీయ ప్రొఫైల్‌ను నిర్వహించండి';

  @override
  String get configurePoliticalDetails => 'మీ రాజకీయ వివరాలు, పార్టీ మరియు ఇతర పారామీటర్లను కాన్ఫిగర్ చేయండి';

  @override
  String get businessParameters => 'వ్యాపార పారామీటర్లు';

  @override
  String get politicalParameters => 'రాజకీయ పారామీటర్లు';

  @override
  String get contentRefreshedSuccessfully => 'కంటెంట్ విజయవంతంగా రిఫ్రెష్ చేయబడింది';

  @override
  String get failedToRefreshContent => 'కంటెంట్‌ను రిఫ్రెష్ చేయడంలో విఫలమైంది';

  @override
  String get mySubscription => 'నా సబ్‌స్క్రిప్షన్';

  @override
  String get startByAddingBaseImage => 'బేస్ ఇమేజ్‌ను జోడించడం ద్వారా ప్రారంభించండి';

  @override
  String get backgroundOfPoster => 'ఇది మీ పోస్టర్ యొక్క నేపథ్యం అవుతుంది';

  @override
  String get selectBaseImage => 'బేస్ ఇమేజ్‌ను ఎంచుకోండి';

  @override
  String get addBaseImage => 'బేస్ ఇమేజ్‌ను జోడించండి';

  @override
  String get aboutPageComingSoon => 'గురించి పేజీ త్వరలో వస్తోంది';

  @override
  String get languageSettingsComingSoon => 'భాష సెట్టింగ్‌లు త్వరలో వస్తున్నాయి';

  @override
  String get notificationSettingsComingSoon => 'నోటిఫికేషన్ సెట్టింగ్‌లు త్వరలో వస్తున్నాయి';

  @override
  String get manageNotificationSettings => 'నోటిఫికేషన్ సెట్టింగ్‌లను నిర్వహించండి';

  @override
  String get imageShape => 'చిత్ర ఆకారం';

  @override
  String get businessLogoShape => 'వ్యాపార లోగో ఆకారం';

  @override
  String get profilePhoto => 'ప్రొఫైల్ ఫోటో';

  @override
  String get businessLogo => 'వ్యాపార లోగో';

  @override
  String get pleaseSelectUserType => 'దయచేసి వినియోగదారు రకాన్ని ఎంచుకోండి';

  @override
  String get failedToLoadUserTypes => 'వినియోగదారు రకాలను లోడ్ చేయడంలో విఫలమైంది';

  @override
  String get failedToSaveUserType => 'వినియోగదారు రకాన్ని సేవ్ చేయడంలో విఫలమైంది';

  @override
  String get profileCompletedSuccessfully => 'ప్రొఫైల్ విజయవంతంగా పూర్తయింది';

  @override
  String get viewEditProfileInfo => 'మీ ప్రొఫైల్ సమాచారాన్ని చూడండి మరియు సవరించండి';

  @override
  String get chooseImageSource => 'ఇమేజ్ మూలాన్ని ఎంచుకోండి';

  @override
  String get customImage => 'కస్టమ్ చిత్రం';

  @override
  String get autoSelect => 'స్వయంచాలక ఎంపిక';

  @override
  String get imageSource => 'ఇమేజ్ మూలం';

  @override
  String get shapeOptions => 'ఆకార ఎంపికలు';

  @override
  String get none => 'ఏదీ లేదు';

  @override
  String get circle => 'వృత్తం';

  @override
  String get rectangle => 'దీర్ఘచతురస్రం';

  @override
  String get roundedRectangle => 'గుండ్రని దీర్ఘచతురస్రం';

  @override
  String get diamond => 'వజ్రం';

  @override
  String get hexagon => 'షట్కోణం';

  @override
  String get quickPosters => 'క్విక్ పోస్టర్స్';

  @override
  String get accountActions => 'ఖాతా చర్యలు';

  @override
  String get signOutFromAccount => 'మీ ఖాతా నుండి సైన్ అవుట్ చేయండి';

  @override
  String get changingLanguage => 'భాష మార్చబడుతోంది...';

  @override
  String get phoneNumberLabel => 'ఫోన్ నంబర్';

  @override
  String get enterYourPhoneNumber => 'మీ ఫోన్ నంబర్‌ను నమోదు చేయండి';

  @override
  String get userType => 'వినియోగదారు రకం';

  @override
  String get saveToGallery => 'గ్యాలరీలో సేవ్ చేయండి';

  @override
  String get share => 'షేర్';

  @override
  String get whatsApp => 'వాట్సాప్';

  @override
  String get invalidPhoneNumber => 'ఫోన్ నంబర్ ఫార్మాట్ తప్పు. దయచేసి చెల్లుబాటు అయ్యే ఫోన్ నంబర్‌ను నమోదు చేయండి.';

  @override
  String get quotaExceeded => 'SMS కోటా అయిపోయింది. దయచేసి తర్వాత మళ్లీ ప్రయత్నించండి.';

  @override
  String get userDisabled => 'ఈ వినియోగదారు నిలిపివేయబడ్డారు. దయచేసి మద్దతును సంప్రదించండి.';

  @override
  String get operationNotAllowed => 'ఫోన్ ప్రమాణీకరణ ప్రారంభించబడలేదు. దయచేసి మద్దతును సంప్రదించండి.';

  @override
  String get captchaCheckFailed => 'reCAPTCHA ధృవీకరణ విఫలమైంది. దయచేసి మళ్లీ ప్రయత్నించండి.';

  @override
  String get missingClientIdentifier => 'యాప్ ధృవీకరణ ప్రక్రియ విఫలమైంది. దయచేసి మళ్లీ ప్రయత్నించండి లేదా మద్దతును సంప్రదించండి.';

  @override
  String get tooManyRequests => 'ఈ పరికరం నుండి చాలా అభ్యర్థనలు. అసాధారణ కార్యకలాపాల కారణంగా మేము ఈ పరికరం నుండి అన్ని అభ్యర్థనలను తాత్కాలికంగా నిరోధించాము. దయచేసి కొంత సమయం తర్వాత మళ్లీ ప్రయత్నించండి (సాధారణంగా కొన్ని గంటలు).';

  @override
  String get verificationFailed => 'ధృవీకరణ విఫలమైంది. దయచేసి మళ్లీ ప్రయత్నించండి.';

  @override
  String get failedToGetUserData => 'వినియోగదారు డేటాను పొందడంలో విఫలమైంది';

  @override
  String get userNotFound => 'వినియోగదారు కనుగొనబడలేదు';

  @override
  String get politician => 'రాజకీయవేత్త';

  @override
  String get businessman => 'వ్యాపారవేత్త';

  @override
  String get regularUser => 'సాధారణ';

  @override
  String get forPoliticalCampaigns => 'రాజకీయ ప్రచారాలు మరియు ప్రమోషన్‌ల కోసం';

  @override
  String get forBusinessPromotions => 'వ్యాపార ప్రమోషన్‌లు మరియు ప్రకటనల కోసం';

  @override
  String get forPersonalUse => 'వ్యక్తిగత ఉపయోగం మరియు సాధారణ ప్రయోజనాల కోసం';

  @override
  String get developmentLogin => 'డెవలప్‌మెంట్ లాగిన్';

  @override
  String get developmentMode => 'డెవలప్‌మెంట్ మోడ్';

  @override
  String get loggedInAsDeveloper => 'మీరు డెవలపర్‌గా లాగిన్ అయ్యారు.\nFirebase ఫోన్ ప్రమాణీకరణ నిరోధించబడినప్పుడు ఇది తాత్కాలిక మోడ్.';

  @override
  String get noActiveSubscription => 'చురుకైన సబ్‌స్క్రిప్షన్ లేదు';

  @override
  String expiresInDays(int days) {
    return '$days రోజుల్లో గడువు ముగుస్తుంది';
  }

  @override
  String activeUntil(String date) {
    return '$date వరకు చురుకుగా ఉంటుంది';
  }

  @override
  String get downloadImage => 'ఇమేజ్‌ను డౌన్‌లోడ్ చేయండి';

  @override
  String get shareOnWhatsApp => 'వాట్సాప్‌లో షేర్ చేయండి';

  @override
  String get customImageSelected => 'కస్టమ్ ఇమేజ్ ఎంచుకోబడింది!';

  @override
  String errorSelectingImage(String error) {
    return 'ఇమేజ్ ఎంచుకోవడంలో లోపం: $error';
  }

  @override
  String get imageAddedSuccessfully => 'ఇమేజ్ విజయవంతంగా జోడించబడింది!';

  @override
  String errorAddingImage(String error) {
    return 'ఇమేజ్ జోడించడంలో లోపం: $error';
  }

  @override
  String noImageAvailable(String source) {
    return '$source ఇమేజ్ అందుబాటులో లేదు';
  }

  @override
  String get emailAddress => 'ఇమెయిల్ చిరునామా';

  @override
  String get enterEmailAddress => 'మీ ఇమెయిల్ చిరునామాను నమోదు చేయండి';

  @override
  String get optional => 'ఐచ్ఛికం';

  @override
  String get selectProfileImage => 'ప్రొఫైల్ ఇమేజ్‌ను ఎంచుకోండి';

  @override
  String get camera => 'కెమెరా';

  @override
  String get gallery => 'గ్యాలరీ';

  @override
  String get removeImage => 'ఇమేజ్‌ను తీసివేయండి';

  @override
  String get saveProfile => 'ప్రొఫైల్‌ను సేవ్ చేయండి';

  @override
  String get updateProfile => 'ప్రొఫైల్‌ను అప్‌డేట్ చేయండి';

  @override
  String get profileImageUpdated => 'ప్రొఫైల్ ఇమేజ్ విజయవంతంగా అప్‌డేట్ చేయబడింది';

  @override
  String get failedToUpdateProfileImage => 'ప్రొఫైల్ ఇమేజ్‌ను అప్‌డేట్ చేయడంలో విఫలమైంది';

  @override
  String get pleaseEnterValidEmail => 'దయచేసి చెల్లుబాటు అయ్యే ఇమెయిల్ చిరునామాను నమోదు చేయండి';

  @override
  String get pleaseEnterName => 'దయచేసి మీ పేరును నమోదు చేయండి';

  @override
  String get nameTooShort => 'పేరు కనీసం 2 అక్షరాలు ఉండాలి';

  @override
  String get invalidEmailFormat => 'దయచేసి చెల్లుబాటు అయ్యే ఇమెయిల్ ఫార్మాట్‌ను నమోదు చేయండి';

  @override
  String get business => 'వ్యాపారం';

  @override
  String get event => 'ఈవెంట్';

  @override
  String get festival => 'పండుగ';

  @override
  String get party => 'పార్టీ';

  @override
  String get education => 'విద్య';

  @override
  String get health => 'ఆరోగ్యం';

  @override
  String get technology => 'సాంకేతికత';

  @override
  String get food => 'ఆహారం';

  @override
  String get travel => 'ప్రయాణం';

  @override
  String get sports => 'క్రీడలు';

  @override
  String get fashion => 'ఫ్యాషన్';

  @override
  String get realEstate => 'రియల్ ఎస్టేట్';

  @override
  String get flyers => 'ఫ్లైయర్స్';

  @override
  String get brochures => 'బ్రోషర్లు';

  @override
  String get businessCards => 'బిజినెస్ కార్డులు';

  @override
  String get presentations => 'ప్రెజెంటేషన్లు';

  @override
  String get logos => 'లోగోలు';

  @override
  String get invitations => 'ఆహ్వానాలు';

  @override
  String get tickets => 'టిక్కెట్లు';

  @override
  String get programs => 'కార్యక్రమాలు';

  @override
  String get banners => 'బ్యానర్లు';

  @override
  String get posters => 'పోస్టర్లు';

  @override
  String get celebration => 'వేడుక';

  @override
  String get religious => 'మతపరమైన';

  @override
  String get cultural => 'సాంస్కృతిక';

  @override
  String get seasonal => 'కాలానుగుణ';

  @override
  String get birthday => 'పుట్టినరోజు';

  @override
  String get wedding => 'వివాహం';

  @override
  String get anniversary => 'వార్షికోత్సవం';

  @override
  String get graduation => 'గ్రాడ్యుయేషన్';

  @override
  String get certificates => 'సర్టిఫికేట్లు';

  @override
  String get worksheets => 'వర్క్‌షీట్లు';

  @override
  String get awareness => 'అవగాహన';

  @override
  String get medical => 'వైద్య';

  @override
  String get fitness => 'ఫిట్‌నెస్';

  @override
  String get nutrition => 'పోషణ';

  @override
  String get appPromotion => 'యాప్ ప్రమోషన్';

  @override
  String get software => 'సాఫ్ట్‌వేర్';

  @override
  String get digitalServices => 'డిజిటల్ సేవలు';

  @override
  String get menu => 'మెనూ';

  @override
  String get recipe => 'రెసిపీ';

  @override
  String get restaurant => 'రెస్టారెంట్';

  @override
  String get catering => 'క్యాటరింగ్';

  @override
  String get tourism => 'పర్యాటకం';

  @override
  String get hotels => 'హోటల్స్';

  @override
  String get transportation => 'రవాణా';

  @override
  String get adventure => 'సాహసం';

  @override
  String get team => 'జట్టు';

  @override
  String get tournament => 'టోర్నమెంట్';

  @override
  String get equipment => 'పరికరాలు';

  @override
  String get clothing => 'దుస్తులు';

  @override
  String get accessories => 'ఉపకరణాలు';

  @override
  String get beauty => 'అందం';

  @override
  String get style => 'శైలి';

  @override
  String get property => 'ఆస్తి';

  @override
  String get rental => 'అద్దె';

  @override
  String get commercial => 'వాణిజ్య';

  @override
  String get residential => 'నివాస';

  @override
  String get noShape => 'ఆకారం లేదు';

  @override
  String get rounded => 'గుండ్రని';

  @override
  String get loadMoreTemplates => 'మరిన్ని టెంప్లేట్లను లోడ్ చేయండి';

  @override
  String get horizontal => 'క్షితిజ సమాంతర';

  @override
  String get vertical => 'నిలువు';

  @override
  String get square => 'చతురస్రం';

  @override
  String get smallBusiness => 'చిన్న వ్యాపారం';

  @override
  String get eventOrganizers => 'ఈవెంట్ నిర్వాహకులు';

  @override
  String get educators => 'విద్యావేత్తలు';

  @override
  String get marketers => 'మార్కెటర్లు';

  @override
  String get students => 'విద్యార్థులు';

  @override
  String get professionals => 'వృత్తిపరులు';

  @override
  String get generalPublic => 'సాధారణ ప్రజలు';

  @override
  String get modern => 'ఆధునిక';

  @override
  String get minimalist => 'కనిష్ట';

  @override
  String get retro => 'రెట్రో';

  @override
  String get elegant => 'సొగసైన';

  @override
  String get creative => 'సృజనాత్మక';

  @override
  String get professional => 'వృత్తిపరమైన';

  @override
  String get playful => 'ఆట పాట';

  @override
  String get print => 'ప్రింట్';

  @override
  String get socialMedia => 'సోషల్ మీడియా';

  @override
  String get onlineAdvertising => 'ఆన్‌లైన్ ప్రకటనలు';

  @override
  String get email => 'ఇమెయిల్';

  @override
  String get web => 'వెబ్';

  @override
  String get high => 'అధిక';

  @override
  String get medium => 'మధ్యమ';

  @override
  String get low => 'తక్కువ';

  @override
  String get freeForPersonalUse => 'వ్యక్తిగత వినియోగానికి ఉచితం';

  @override
  String get commercialUseAllowed => 'వాణిజ్య వినియోగానికి అనుమతి';

  @override
  String get premiumLicenseRequired => 'ప్రీమియం లైసెన్స్ అవసరం';

  @override
  String get templateName => 'టెంప్లేట్ పేరు';

  @override
  String get templateDescription => 'టెంప్లేట్ వివరణ';

  @override
  String get creator => 'సృష్టికర్త';

  @override
  String get dimensions => 'కొలతలు';

  @override
  String get tags => 'ట్యాగ్‌లు';

  @override
  String get colors => 'రంగులు';

  @override
  String get fontStyles => 'ఫాంట్ శైలులు';

  @override
  String get fileFormats => 'ఫైల్ ఫార్మాట్‌లు';

  @override
  String get category => 'వర్గం';

  @override
  String get subCategory => 'ఉప వర్గం';

  @override
  String get layoutType => 'లేఅవుట్ రకం';

  @override
  String get targetAudience => 'లక్ష్య ప్రేక్షకులు';

  @override
  String get designStyle => 'డిజైన్ శైలి';

  @override
  String get usageType => 'వినియోగ రకం';

  @override
  String get resolution => 'రిజల్యూషన్';

  @override
  String get licenseType => 'లైసెన్స్ రకం';

  @override
  String get isPremium => 'ప్రీమియం';

  @override
  String get isActive => 'చురుకుగా ఉంది';

  @override
  String get selectImage => 'చిత్రాన్ని ఎంచుకోండి';

  @override
  String get uploadTemplate => 'టెంప్లేట్‌ను అప్‌లోడ్ చేయండి';

  @override
  String get updateTemplate => 'టెంప్లేట్‌ను అప్‌డేట్ చేయండి';

  @override
  String get uploading => 'అప్‌లోడ్ అవుతోంది...';

  @override
  String get templateUploadedSuccessfully => 'టెంప్లేట్ విజయవంతంగా అప్‌లోడ్ చేయబడింది';

  @override
  String get templateUpdatedSuccessfully => 'టెంప్లేట్ విజయవంతంగా అప్‌డేట్ చేయబడింది';

  @override
  String get failedToUploadTemplate => 'టెంప్లేట్ అప్‌లోడ్ చేయడంలో విఫలమైంది';

  @override
  String get failedToUpdateTemplate => 'టెంప్లేట్ అప్‌డేట్ చేయడంలో విఫలమైంది';

  @override
  String get pleaseSelectImage => 'దయచేసి ఒక చిత్రాన్ని ఎంచుకోండి';

  @override
  String get pleaseEnterTemplateName => 'దయచేసి టెంప్లేట్ పేరును నమోదు చేయండి';

  @override
  String get pleaseSelectCategory => 'దయచేసి ఒక వర్గాన్ని ఎంచుకోండి';

  @override
  String get loadingBanners => 'బ్యానర్లు లోడ్ అవుతున్నాయి...';

  @override
  String get loadingMoreBanners => 'మరిన్ని బ్యానర్లు లోడ్ అవుతున్నాయి...';

  @override
  String get errorLoadingBanners => 'బ్యానర్లను లోడ్ చేయడంలో లోపం';

  @override
  String get noBannersFound => 'బ్యానర్లు ఏవీ కనుగొనబడలేదు';

  @override
  String get selectBanner => 'బ్యానర్‌ను ఎంచుకోండి';

  @override
  String get customizeTemplate => 'టెంప్లేట్‌ను అనుకూలీకరించండి';

  @override
  String get imageCustomization => 'చిత్ర అనుకూలీకరణ';

  @override
  String get bannerCustomization => 'బ్యానర్ అనుకూలీకరణ';

  @override
  String get textCustomization => 'టెక్స్ట్ అనుకూలీకరణ';

  @override
  String get previewTemplate => 'టెంప్లేట్ ప్రివ్యూ';

  @override
  String get saveTemplate => 'టెంప్లేట్‌ను సేవ్ చేయండి';

  @override
  String get downloadTemplate => 'టెంప్లేట్‌ను డౌన్‌లోడ్ చేయండి';

  @override
  String get shareTemplate => 'టెంప్లేట్‌ను షేర్ చేయండి';

  @override
  String get templateSavedSuccessfully => 'టెంప్లేట్ విజయవంతంగా సేవ్ చేయబడింది';

  @override
  String get templateDownloadedSuccessfully => 'టెంప్లేట్ విజయవంతంగా డౌన్‌లోడ్ చేయబడింది';

  @override
  String get failedToSaveTemplate => 'టెంప్లేట్ సేవ్ చేయడంలో విఫలమైంది';

  @override
  String get failedToDownloadTemplate => 'టెంప్లేట్ డౌన్‌లోడ్ చేయడంలో విఫలమైంది';

  @override
  String get processingImage => 'చిత్రం ప్రాసెస్ అవుతోంది...';

  @override
  String get generatingPreview => 'ప్రివ్యూ జనరేట్ అవుతోంది...';

  @override
  String get preparingDownload => 'డౌన్‌లోడ్ సిద్ధం అవుతోంది...';

  @override
  String get manageBusiness => 'వ్యాపార నిర్వహణ';

  @override
  String get configureBusinessDetails => 'మీ వ్యాపార వివరాలు, చిరునామా మరియు ఇతర పారామీటర్లను కాన్ఫిగర్ చేయండి';

  @override
  String get managePolitical => 'రాజకీయ నిర్వహణ';

  @override
  String get failedToLoadBusinessProfile => 'వ్యాపార ప్రొఫైల్‌ను లోడ్ చేయడంలో విఫలమైంది';

  @override
  String get failedToLoadPoliticalProfile => 'రాజకీయ ప్రొఫైల్‌ను లోడ్ చేయడంలో విఫలమైంది';

  @override
  String get noBusinessParametersAvailable => 'వ్యాపార పారామీటర్లు ఏవీ అందుబాటులో లేవు';

  @override
  String get businessParametersConfiguredByAdmin => 'వ్యాపార పారామీటర్లు నిర్వాహకుడిచే కాన్ఫిగర్ చేయబడతాయి';

  @override
  String get noPoliticalParametersAvailable => 'రాజకీయ పారామీటర్లు ఏవీ అందుబాటులో లేవు';

  @override
  String get politicalParametersConfiguredByAdmin => 'రాజకీయ పారామీటర్లు నిర్వాహకుడిచే కాన్ఫిగర్ చేయబడతాయి';

  @override
  String get saveBusinessParameters => 'వ్యాపార పారామీటర్లను సేవ్ చేయండి';

  @override
  String get savePoliticalParameters => 'రాజకీయ పారామీటర్లను సేవ్ చేయండి';

  @override
  String get businessParametersSavedSuccessfully => 'వ్యాపార పారామీటర్లు విజయవంతంగా సేవ్ చేయబడ్డాయి';

  @override
  String get politicalParametersSavedSuccessfully => 'రాజకీయ పారామీటర్లు విజయవంతంగా సేవ్ చేయబడ్డాయి';

  @override
  String get failedToSaveBusinessParameters => 'వ్యాపార పారామీటర్లను సేవ్ చేయడంలో విఫలమైంది';

  @override
  String get failedToSavePoliticalParameters => 'రాజకీయ పారామీటర్లను సేవ్ చేయడంలో విఫలమైంది';

  @override
  String get businessType => 'వ్యాపార రకం';

  @override
  String get businessAddress => 'వ్యాపార చిరునామా';

  @override
  String get businessLocation => 'వ్యాపార స్థానం';

  @override
  String get businessMobile => 'వ్యాపార మొబైల్';

  @override
  String get businessWebsite => 'వ్యాపార వెబ్‌సైట్';

  @override
  String get businessEmail => 'వ్యాపార ఇమెయిల్';

  @override
  String get selectBusinessLogo => 'వ్యాపార లోగోను ఎంచుకోండి';

  @override
  String get politicalPhoto => 'రాజకీయ ఫోటో';

  @override
  String get selectPoliticalPhoto => 'రాజకీయ ఫోటోను ఎంచుకోండి';

  @override
  String get partyName => 'పార్టీ పేరు';

  @override
  String get politicalPosition => 'రాజకీయ పదవి';

  @override
  String get constituency => 'నియోజకవర్గం';

  @override
  String get politicalExperience => 'రాజకీయ అనుభవం';

  @override
  String get campaignSlogan => 'ప్రచార నినాదం';

  @override
  String get politicalAchievements => 'రాజకీయ విజయాలు';

  @override
  String get isRequired => 'అవసరం';

  @override
  String get pleaseEnterValidNumber => 'దయచేసి చెల్లుబాటు అయ్యే సంఖ్యను నమోదు చేయండి';

  @override
  String get allTypes => 'అన్ని రకాలు';

  @override
  String get removePremium => 'ప్రీమియం తొలగించు';

  @override
  String get makePremium => 'ప్రీమియం చేయండి';

  @override
  String get deleteUser => 'వినియోగదారుని తొలగించు';

  @override
  String get userManagement => 'వినియోగదారు నిర్వహణ';

  @override
  String get searchUsers => 'వినియోగదారులను వెతకండి...';

  @override
  String get noUsersFound => 'వినియోగదారులు ఎవరూ కనుగొనబడలేదు';

  @override
  String get loadingUsers => 'వినియోగదారులు లోడ్ అవుతున్నారు...';

  @override
  String get failedToLoadUsers => 'వినియోగదారులను లోడ్ చేయడంలో విఫలమైంది';

  @override
  String get userDetails => 'వినియోగదారు వివరాలు';

  @override
  String get accountCreated => 'ఖాతా సృష్టించబడింది';

  @override
  String get lastUpdated => 'చివరిసారి అప్‌డేట్ చేయబడింది';

  @override
  String get profileComplete => 'ప్రొఫైల్ పూర్తయింది';

  @override
  String get profileIncomplete => 'ప్రొఫైల్ అసంపూర్ణం';

  @override
  String get premiumStatus => 'ప్రీమియం స్థితి';

  @override
  String get active => 'చురుకుగా ఉంది';

  @override
  String get inactive => 'నిష్క్రియం';

  @override
  String get subscriptionPlan => 'సబ్‌స్క్రిప్షన్ ప్లాన్';

  @override
  String get subscriptionEndDate => 'సబ్‌స్క్రిప్షన్ ముగింపు తేదీ';

  @override
  String get languagePreference => 'భాష ప్రాధాన్యత';

  @override
  String get totalPosters => 'మొత్తం పోస్టర్లు';

  @override
  String get adminStatus => 'నిర్వాహక స్థితి';

  @override
  String get isAdmin => 'నిర్వాహకుడు';

  @override
  String get isNotAdmin => 'నిర్వాహకుడు కాదు';

  @override
  String get allUsers => 'అన్ని వినియోగదారులు';

  @override
  String get free => 'ఉచితం';

  @override
  String get retry => 'మళ్లీ ప్రయత్నించండి';

  @override
  String get tryAdjustingFilters => 'మీ శోధన లేదా ఫిల్టర్‌లను సర్దుబాటు చేయడానికి ప్రయత్నించండి';

  @override
  String get notProvided => 'అందించబడలేదు';

  @override
  String get yes => 'అవును';

  @override
  String get no => 'లేదు';

  @override
  String get created => 'సృష్టించబడింది';

  @override
  String get name => 'పేరు';

  @override
  String get userPremiumStatusRemoved => 'వినియోగదారు ప్రీమియం స్థితి తొలగించబడింది';

  @override
  String get userUpgradedToPremium => 'వినియోగదారు ప్రీమియంకు అప్‌గ్రేడ్ చేయబడ్డారు';

  @override
  String get errorUpdatingUser => 'వినియోగదారుని అప్‌డేట్ చేయడంలో లోపం';

  @override
  String areYouSureDeleteUser(String userName) {
    return 'మీరు నిజంగా $userName ను తొలగించాలనుకుంటున్నారా? ఈ చర్యను రద్దు చేయలేము.';
  }

  @override
  String get userDeletedSuccessfully => 'వినియోగదారు విజయవంతంగా తొలగించబడ్డారు';

  @override
  String get errorDeletingUser => 'వినియోగదారుని తొలగించడంలో లోపం';

  @override
  String get add => 'జోడించండి';

  @override
  String get extend => 'పొడిగించండి';

  @override
  String subscriptionExtended(String duration) {
    return 'సబ్‌స్క్రిప్షన్ $duration పొడిగించబడింది!';
  }

  @override
  String failedToExtendSubscription(String error) {
    return 'సబ్‌స్క్రిప్షన్ పొడిగించడంలో విఫలమైంది: $error';
  }

  @override
  String failedToPurchaseSubscription(String error) {
    return 'సబ్‌స్క్రిప్షన్ కొనుగోలు చేయడంలో విఫలమైంది: $error';
  }

  @override
  String get failedToRestorePurchases => 'కొనుగోళ్లను పునరుద్ధరించడంలో విఫలమైంది';

  @override
  String get trialPlanDescription => 'కనీస ఖర్చుతో 1 వారం పాటు ప్రీమియం ఫీచర్లను ప్రయత్నించండి';

  @override
  String get threeMonthPlanDescription => 'స్వల్పకాలిక ప్రాజెక్ట్‌లకు సరైనది';

  @override
  String get sixMonthPlanDescription => 'మధ్యకాలిక వినియోగానికి గొప్ప విలువ';

  @override
  String get annualPlanDescription => 'సాధారణ వినియోగదారులకు ఉత్తమ విలువ';

  @override
  String get allPremiumFeatures => 'అన్ని ప్రీమియం ఫీచర్లు';

  @override
  String get autoRenewsAt => '3 నెలలకు ₹199 వద్ద ఆటో-రిన్యూ';

  @override
  String get cancelAnytime => 'ఎప్పుడైనా రద్దు చేయండి';

  @override
  String get quarterlyBilling => 'త్రైమాసిక బిల్లింగ్';

  @override
  String get save50Percent => '3-నెలల ప్లాన్‌తో పోల్చితే 50% ఆదా';

  @override
  String get prioritySupport => 'ప్రాధాన్యత మద్దతు';

  @override
  String get save58Percent => '3-నెలల ప్లాన్‌తో పోల్చితే 58% ఆదా';

  @override
  String get earlyAccessToNewFeatures => 'కొత్త ఫీచర్లకు ముందస్తు యాక్సెస్';

  @override
  String activeUntilDate(String date) {
    return '$date వరకు చురుకుగా ఉంది';
  }
}
