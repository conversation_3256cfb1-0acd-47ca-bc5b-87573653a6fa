import 'package:flutter/material.dart';

/// Theme configuration for QuickPosters app
///
/// The app has two theme variants:
/// 1. Primary Theme (default): Uses primaryGradient (blue to blue-violet) for UI elements
/// 2. Theme2 (alternative): Uses accentGradient (blue-violet to violet) for UI elements
///
/// The primary theme is currently used throughout the app, while Theme2 is preserved
/// for potential future use. To use Theme2, set useTheme2=true in GradientButton
/// or use accentGradientAppBar instead of gradientAppBar.
class AppTheme {
  AppTheme._(); // Private constructor to prevent instantiation

  // Main colors
  static const Color primaryBlue = Color(0xFF2979FF);
  static const Color accentViolet = Color(0xFF8E24AA);
  static const Color gradientMidBlueViolet = Color(0xFF7C4DFF);
  static const Color backgroundWhite = Color(0xFFFFFFFF);
  static const Color lightGradientBg = Color(0xFFF0F4FF);
  static const Color textRichBlack = Color(0xFF121212);
  static const Color secondaryText = Color(0xFF5C5C5C);

  // Premium theme colors
  static const Color premiumGold = Color(0xFFD4AF37);
  static const Color premiumLightGold = Color(0xFFF5E7A3);
  static const Color premiumDarkGold = Color(0xFFAA8C2C);
  static const Color premiumBlack = Color(0xFF111111);
  static const Color premiumDarkGrey = Color(0xFF222222);
  static const Color premiumLightGrey = Color(0xFF333333);

  // Additional colors for UI elements
  static const Color successGreen = Color(0xFF4CAF50);
  static const Color errorRed = Color(0xFFE53935);
  static const Color warningAmber = Color(0xFFFFC107);
  static const Color warningOrange = Color(0xFFFF9800);
  static const Color infoBlue = Color(0xFF2196F3);
  static const Color lightGray = Color(0xFFE0E0E0);

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryBlue, gradientMidBlueViolet],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [gradientMidBlueViolet, accentViolet],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient fullGradient = LinearGradient(
    colors: [primaryBlue, gradientMidBlueViolet, accentViolet],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient lightGradient = LinearGradient(
    colors: [backgroundWhite, lightGradientBg],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // Premium gradients
  static const LinearGradient premiumGoldGradient = LinearGradient(
    colors: [premiumLightGold, premiumGold, premiumDarkGold],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient premiumDarkGradient = LinearGradient(
    colors: [premiumBlack, premiumDarkGrey, premiumLightGrey],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient premiumGoldBlackGradient = LinearGradient(
    colors: [premiumBlack, premiumDarkGrey, premiumLightGrey],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: [0.0, 0.7, 1.0],
  );

  // Shadow styles
  static List<BoxShadow> get lightShadow => [
    BoxShadow(
      color: primaryBlue.withOpacity(0.15),
      blurRadius: 10,
      offset: const Offset(0, 4),
    ),
  ];

  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: primaryBlue.withOpacity(0.25),
      blurRadius: 15,
      offset: const Offset(0, 6),
    ),
  ];

  static List<BoxShadow> get heavyShadow => [
    BoxShadow(
      color: accentViolet.withOpacity(0.3),
      blurRadius: 20,
      offset: const Offset(0, 8),
    ),
  ];

  // Premium shadows
  static List<BoxShadow> get premiumGoldShadow => [
    BoxShadow(
      color: premiumGold.withAlpha(77), // 0.3 * 255 = 77
      blurRadius: 15,
      offset: const Offset(0, 5),
    ),
  ];

  static List<BoxShadow> get premiumBlackShadow => [
    BoxShadow(
      color: premiumBlack.withAlpha(128), // 0.5 * 255 = 128
      blurRadius: 20,
      offset: const Offset(0, 10),
    ),
  ];

  // Text styles
  static TextStyle get headingLarge => const TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: textRichBlack,
    letterSpacing: 0.5,
  );

  static TextStyle get headingMedium => const TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: textRichBlack,
    letterSpacing: 0.3,
  );

  static TextStyle get headingSmall => const TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: textRichBlack,
    letterSpacing: 0.2,
  );

  static TextStyle get bodyLarge => const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: textRichBlack,
  );

  static TextStyle get bodyMedium => const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: textRichBlack,
  );

  static TextStyle get bodySmall => const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: secondaryText,
  );

  static TextStyle get buttonText => const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: backgroundWhite,
    letterSpacing: 0.5,
  );

  // Button styles
  static ButtonStyle get primaryButtonStyle => ButtonStyle(
    padding: MaterialStateProperty.all<EdgeInsets>(
      const EdgeInsets.symmetric(vertical: 14, horizontal: 28),
    ),
    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
      RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    ),
    elevation: MaterialStateProperty.all<double>(4),
    shadowColor: MaterialStateProperty.all<Color>(primaryBlue.withOpacity(0.5)),
    backgroundColor: MaterialStateProperty.all<Color>(Colors.transparent),
  );

  static ButtonStyle get secondaryButtonStyle => ButtonStyle(
    padding: MaterialStateProperty.all<EdgeInsets>(
      const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
    ),
    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
      RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: const BorderSide(color: primaryBlue, width: 2),
      ),
    ),
    backgroundColor: MaterialStateProperty.all<Color>(backgroundWhite),
  );

  // Card styles
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: backgroundWhite,
    borderRadius: BorderRadius.circular(16),
    boxShadow: lightShadow,
  );

  static BoxDecoration get gradientCardDecoration => BoxDecoration(
    gradient: lightGradient,
    borderRadius: BorderRadius.circular(16),
    boxShadow: lightShadow,
  );

  static BoxDecoration get fancyCardDecoration => BoxDecoration(
    gradient: lightGradient,
    borderRadius: BorderRadius.circular(16),
    boxShadow: mediumShadow,
    border: Border.all(
      color: primaryBlue.withOpacity(0.1),
      width: 1,
    ),
  );

  // Premium card decorations
  static BoxDecoration get premiumGoldCardDecoration => BoxDecoration(
    color: premiumBlack,
    borderRadius: BorderRadius.circular(16),
    boxShadow: premiumBlackShadow,
    border: Border.all(
      color: premiumGold,
      width: 1,
    ),
  );

  static BoxDecoration get premiumDarkCardDecoration => BoxDecoration(
    gradient: premiumDarkGradient,
    borderRadius: BorderRadius.circular(16),
    boxShadow: premiumBlackShadow,
  );

  static BoxDecoration get premiumGoldBorderedCardDecoration => BoxDecoration(
    color: premiumBlack,
    borderRadius: BorderRadius.circular(16),
    boxShadow: premiumBlackShadow,
    border: Border.all(
      color: premiumGold,
      width: 2,
    ),
  );

  // Custom AppBar with gradient background
  static PreferredSizeWidget gradientAppBar({
    required String title,
    List<Widget>? actions,
    bool centerTitle = true,
    PreferredSizeWidget? bottom,
    double elevation = 0,
    LinearGradient? gradient,
  }) {
    return AppBar(
      title: Text(title),
      centerTitle: centerTitle,
      actions: actions,
      bottom: bottom,
      elevation: elevation,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: gradient ?? primaryGradient,
          borderRadius: const BorderRadius.vertical(
            bottom: Radius.circular(16),
          ),
        ),
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          bottom: Radius.circular(16),
        ),
      ),
    );
  }

  // Custom AppBar with accent gradient background (theme2)
  static PreferredSizeWidget accentGradientAppBar({
    required String title,
    List<Widget>? actions,
    bool centerTitle = true,
    PreferredSizeWidget? bottom,
    double elevation = 0,
  }) {
    return AppBar(
      title: Text(title),
      centerTitle: centerTitle,
      actions: actions,
      bottom: bottom,
      elevation: elevation,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: accentGradient,
          borderRadius: const BorderRadius.vertical(
            bottom: Radius.circular(16),
          ),
        ),
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          bottom: Radius.circular(16),
        ),
      ),
    );
  }

  // Custom AppBar with premium black gradient and gold accents
  static PreferredSizeWidget premiumGoldAppBar({
    required String title,
    List<Widget>? actions,
    bool centerTitle = true,
    PreferredSizeWidget? bottom,
    double elevation = 0,
  }) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          color: premiumGold,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: centerTitle,
      actions: actions,
      bottom: bottom,
      elevation: elevation,
      iconTheme: const IconThemeData(color: premiumGold),
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: premiumDarkGradient,
          borderRadius: const BorderRadius.vertical(
            bottom: Radius.circular(16),
          ),
          border: const Border(
            bottom: BorderSide(
              color: premiumGold,
              width: 2,
            ),
          ),
        ),
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          bottom: Radius.circular(16),
        ),
      ),
    );
  }

  // Get the main ThemeData for the app
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryBlue,
        primary: primaryBlue,
        secondary: accentViolet,
        tertiary: gradientMidBlueViolet,
        background: backgroundWhite,
        surface: lightGradientBg,
        onPrimary: backgroundWhite,
        onSecondary: backgroundWhite,
        onBackground: textRichBlack,
        onSurface: textRichBlack,
        brightness: Brightness.light,
      ),
      scaffoldBackgroundColor: backgroundWhite,
      appBarTheme: AppBarTheme(
        backgroundColor: primaryBlue,
        foregroundColor: backgroundWhite,
        elevation: 0,
        centerTitle: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(16),
          ),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: primaryButtonStyle,
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: secondaryButtonStyle,
      ),
      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          foregroundColor: MaterialStateProperty.all<Color>(primaryBlue),
          textStyle: MaterialStateProperty.all<TextStyle>(
            const TextStyle(
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: lightGradientBg,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: primaryBlue.withOpacity(0.2), width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryBlue, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorRed, width: 1),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadowColor: primaryBlue.withOpacity(0.2),
      ),
      fontFamily: 'Roboto',
    );
  }

  // Get the premium ThemeData for the app
  static ThemeData get premiumTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: premiumBlack,
        primary: premiumGold,
        secondary: premiumDarkGold,
        tertiary: premiumLightGold,
        background: premiumBlack,
        surface: premiumDarkGrey,
        onPrimary: premiumBlack,
        onSecondary: premiumLightGold,
        onBackground: premiumLightGold,
        onSurface: premiumLightGold,
        brightness: Brightness.dark,
      ),
      scaffoldBackgroundColor: premiumBlack,
      appBarTheme: AppBarTheme(
        backgroundColor: premiumBlack,
        foregroundColor: premiumGold,
        elevation: 0,
        centerTitle: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(16),
          ),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          padding: MaterialStateProperty.all<EdgeInsets>(
            const EdgeInsets.symmetric(vertical: 14, horizontal: 28),
          ),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: const BorderSide(color: premiumGold, width: 1),
            ),
          ),
          elevation: MaterialStateProperty.all<double>(4),
          shadowColor: MaterialStateProperty.all<Color>(premiumGold.withAlpha(77)),
          backgroundColor: MaterialStateProperty.all<Color>(premiumBlack),
          foregroundColor: MaterialStateProperty.all<Color>(premiumGold),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: ButtonStyle(
          padding: MaterialStateProperty.all<EdgeInsets>(
            const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
          ),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: const BorderSide(color: premiumGold, width: 2),
            ),
          ),
          backgroundColor: MaterialStateProperty.all<Color>(premiumBlack),
          foregroundColor: MaterialStateProperty.all<Color>(premiumGold),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          foregroundColor: MaterialStateProperty.all<Color>(premiumGold),
          textStyle: MaterialStateProperty.all<TextStyle>(
            const TextStyle(
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: premiumDarkGrey,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: premiumGold.withAlpha(51), width: 1), // 0.2 * 255 = 51
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: premiumGold, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorRed, width: 1),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        labelStyle: TextStyle(color: premiumGold),
        hintStyle: TextStyle(color: premiumGold.withAlpha(128)),
      ),
      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadowColor: premiumGold.withAlpha(51), // 0.2 * 255 = 51
        color: premiumDarkGrey,
      ),
      iconTheme: IconThemeData(
        color: premiumGold,
      ),
      textTheme: TextTheme(
        displayLarge: TextStyle(color: premiumLightGold),
        displayMedium: TextStyle(color: premiumLightGold),
        displaySmall: TextStyle(color: premiumLightGold),
        headlineLarge: TextStyle(color: premiumLightGold),
        headlineMedium: TextStyle(color: premiumLightGold),
        headlineSmall: TextStyle(color: premiumLightGold),
        titleLarge: TextStyle(color: premiumGold),
        titleMedium: TextStyle(color: premiumGold),
        titleSmall: TextStyle(color: premiumGold),
        bodyLarge: TextStyle(color: premiumLightGold),
        bodyMedium: TextStyle(color: premiumLightGold),
        bodySmall: TextStyle(color: premiumLightGold.withAlpha(204)), // 0.8 * 255 = 204
      ),
      fontFamily: 'Roboto',
    );
  }


}
