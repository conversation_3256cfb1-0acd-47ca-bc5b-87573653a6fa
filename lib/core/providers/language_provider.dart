import 'package:flutter/material.dart';
import '../services/language_service.dart';
import '../services/notification_service.dart';
import '../utils/logger.dart';

/// Provider for managing app language state
class LanguageProvider extends ChangeNotifier {
  Locale _currentLocale = const Locale('en');
  bool _isLoading = true;
  String? _error;

  /// Current locale being used by the app
  Locale get currentLocale => _currentLocale;

  /// Current language code
  String get currentLanguageCode => _currentLocale.languageCode;

  /// Whether the language is still loading
  bool get isLoading => _isLoading;

  /// Error message if language loading failed
  String? get error => _error;

  /// Display name of current language
  String get currentLanguageDisplayName => 
      LanguageService.getDisplayName(currentLanguageCode);

  /// English name of current language
  String get currentLanguageEnglishName => 
      LanguageService.getEnglishName(currentLanguageCode);

  /// Constructor - initializes with saved language
  LanguageProvider() {
    _initializeLanguage();
  }

  /// Initialize language from saved preferences
  Future<void> _initializeLanguage() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      AppLogger.info('Initializing language provider...');
      
      final savedLanguageCode = await LanguageService.initializeLanguage();
      _currentLocale = LanguageService.getLocaleForLanguage(savedLanguageCode);
      
      AppLogger.info('Language provider initialized with: $savedLanguageCode');
    } catch (e) {
      _error = 'Failed to load language preferences';
      AppLogger.error('Error initializing language provider', e);
      
      // Fallback to default language
      _currentLocale = const Locale('en');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Change the app language
  Future<bool> changeLanguage(String languageCode) async {
    try {
      AppLogger.info('Changing language to: $languageCode');
      
      if (!LanguageService.isLanguageSupported(languageCode)) {
        throw ArgumentError('Unsupported language code: $languageCode');
      }

      // Don't change if it's the same language
      if (_currentLocale.languageCode == languageCode) {
        AppLogger.info('Language is already set to: $languageCode');
        return true;
      }

      _isLoading = true;
      _error = null;
      notifyListeners();

      // Save to local storage
      await LanguageService.saveLanguage(languageCode);

      // Update in Firestore if user is logged in
      await LanguageService.updateUserLanguage(languageCode);

      // Update notification topics
      await _updateNotificationTopics(languageCode);

      // Update the locale
      _currentLocale = LanguageService.getLocaleForLanguage(languageCode);

      AppLogger.info('Language successfully changed to: $languageCode');
      return true;
    } catch (e) {
      _error = 'Failed to change language';
      AppLogger.error('Error changing language to $languageCode', e);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Update notification topics when language changes
  Future<void> _updateNotificationTopics(String newLanguageCode) async {
    try {
      AppLogger.info('Updating notification topics for language: $newLanguageCode');
      
      final notificationService = NotificationService();
      
      // Unsubscribe from all language topics
      final allLanguageCodes = LanguageService.getSupportedLanguageCodes();
      for (final langCode in allLanguageCodes) {
        await notificationService.unsubscribeFromTopic('lang_$langCode');
      }

      // Subscribe to new language topic
      await notificationService.subscribeToTopic('lang_$newLanguageCode');
      
      AppLogger.info('Notification topics updated for language: $newLanguageCode');
    } catch (e) {
      AppLogger.error('Error updating notification topics for language', e);
      // Don't throw error as this is not critical for language change
    }
  }

  /// Sync language preferences when user logs in
  Future<void> syncLanguageOnLogin() async {
    try {
      AppLogger.info('Syncing language preferences on login...');
      
      _isLoading = true;
      _error = null;
      notifyListeners();

      await LanguageService.syncLanguagePreferences();
      
      // Reload the current language in case it changed
      final currentLanguageCode = await LanguageService.getSavedLanguage();
      if (_currentLocale.languageCode != currentLanguageCode) {
        _currentLocale = LanguageService.getLocaleForLanguage(currentLanguageCode);
        
        // Update notification topics for the synced language
        await _updateNotificationTopics(currentLanguageCode);
        
        AppLogger.info('Language synced and updated to: $currentLanguageCode');
      }
    } catch (e) {
      _error = 'Failed to sync language preferences';
      AppLogger.error('Error syncing language on login', e);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Reset language to default when user logs out
  Future<void> resetLanguageOnLogout() async {
    try {
      AppLogger.info('Resetting language on logout...');
      
      // Keep the current language in local storage but clear Firestore sync
      // This way user's language preference is preserved locally
      
      // Just update notification topics to unsubscribe from user-specific topics
      // The NotificationService will handle this in its logout flow
      
      AppLogger.info('Language reset completed on logout');
    } catch (e) {
      AppLogger.error('Error resetting language on logout', e);
    }
  }

  /// Get all supported languages
  List<Map<String, String>> getSupportedLanguages() {
    return LanguageService.getSupportedLanguagesList();
  }

  /// Check if a language is currently selected
  bool isLanguageSelected(String languageCode) {
    return _currentLocale.languageCode == languageCode;
  }

  /// Refresh language from saved preferences
  Future<void> refreshLanguage() async {
    await _initializeLanguage();
  }

  /// Clear any error state
  void clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  /// Get locale for a specific language code
  Locale getLocaleForLanguage(String languageCode) {
    return LanguageService.getLocaleForLanguage(languageCode);
  }
}
