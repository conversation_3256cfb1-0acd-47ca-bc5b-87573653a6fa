import 'package:flutter/material.dart';

class TemplateCustomizationProvider extends ChangeNotifier {
  // Name display options
  String _nameDisplayOption = 'business_name';
  String get nameDisplayOption => _nameDisplayOption;
  
  // Contact display options
  String _contactDisplayOption = 'business_phone';
  String get contactDisplayOption => _contactDisplayOption;
  
  // Text colors
  Color _nameTextColor = Colors.white;
  Color get nameTextColor => _nameTextColor;
  
  Color _contactTextColor = Colors.white;
  Color get contactTextColor => _contactTextColor;
  
  // Text cases
  String _nameTextCase = 'normal';
  String get nameTextCase => _nameTextCase;
  
  String _contactTextCase = 'normal';
  String get contactTextCase => _contactTextCase;

  // Update methods that notify listeners
  void updateNameDisplayOption(String option) {
    _nameDisplayOption = option;
    notifyListeners();
  }

  void updateContactDisplayOption(String option) {
    _contactDisplayOption = option;
    notifyListeners();
  }

  void updateNameTextColor(Color color) {
    _nameTextColor = color;
    notifyListeners();
  }

  void updateContactTextColor(Color color) {
    _contactTextColor = color;
    notifyListeners();
  }

  void updateNameTextCase(String textCase) {
    _nameTextCase = textCase;
    notifyListeners();
  }

  void updateContactTextCase(String textCase) {
    _contactTextCase = textCase;
    notifyListeners();
  }

  // Method to update all customizations at once
  void updateAllCustomizations({
    String? nameDisplayOption,
    String? contactDisplayOption,
    Color? nameTextColor,
    String? nameTextCase,
    Color? contactTextColor,
    String? contactTextCase,
  }) {
    if (nameDisplayOption != null) _nameDisplayOption = nameDisplayOption;
    if (contactDisplayOption != null) _contactDisplayOption = contactDisplayOption;
    if (nameTextColor != null) _nameTextColor = nameTextColor;
    if (nameTextCase != null) _nameTextCase = nameTextCase;
    if (contactTextColor != null) _contactTextColor = contactTextColor;
    if (contactTextCase != null) _contactTextCase = contactTextCase;
    
    notifyListeners();
  }
} 