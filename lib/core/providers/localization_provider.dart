import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/firebase_localization_service.dart';
import '../../features/user/domain/usecases/user_service.dart';
import '../utils/logger.dart';

/// Provider for managing localization state and user language preferences
class LocalizationProvider extends ChangeNotifier {
  final FirebaseLocalizationService _localizationService;
  String _currentLanguageCode = 'en';
  bool _isLoading = false;

  LocalizationProvider({FirebaseLocalizationService? localizationService})
      : _localizationService = localizationService ?? FirebaseLocalizationService();

  /// Current user's language code
  String get currentLanguageCode => _currentLanguageCode;

  /// Whether localization data is being loaded
  bool get isLoading => _isLoading;

  /// Get Firebase localization service instance
  FirebaseLocalizationService get localizationService => _localizationService;

  /// Initialize user's language preference
  Future<void> initializeUserLanguage(BuildContext context) async {
    try {
      _isLoading = true;
      notifyListeners();

      final userService = context.read<UserService>();
      final currentUser = await userService.getCurrentUser();

      if (currentUser != null) {
        // Get user's language preference from their profile
        _currentLanguageCode = await _localizationService.getUserLanguage(currentUser.uid);
      } else {
        // Fallback to device locale or default to English
        final deviceLocale = Localizations.localeOf(context);
        _currentLanguageCode = _mapLocaleToLanguageCode(deviceLocale);
      }

      AppLogger.info('Initialized user language: $_currentLanguageCode');
    } catch (e) {
      AppLogger.error('Error initializing user language', e);
      _currentLanguageCode = 'en'; // Fallback to English
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Update user's language preference
  Future<void> updateLanguage(String languageCode, BuildContext context) async {
    if (_currentLanguageCode == languageCode) return;

    try {
      _isLoading = true;
      notifyListeners();

      _currentLanguageCode = languageCode;

      // TODO: Save to user profile if user is logged in
      // This would require adding updateUserLanguage method to UserService
      AppLogger.info('Updated user language preference: $languageCode');

    } catch (e) {
      AppLogger.error('Error updating user language', e);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Map Flutter locale to our supported language codes
  String _mapLocaleToLanguageCode(Locale locale) {
    switch (locale.languageCode) {
      case 'hi':
        return 'hi'; // Hindi
      case 'mr':
        return 'mr'; // Marathi
      case 'bn':
        return 'bn'; // Bengali
      case 'gu':
        return 'gu'; // Gujarati
      case 'ta':
        return 'ta'; // Tamil
      case 'te':
        return 'te'; // Telugu
      case 'en':
      default:
        return 'en'; // English (default)
    }
  }

  /// Get supported language codes
  static List<String> get supportedLanguageCodes => [
    'en', // English
    'hi', // Hindi
    'mr', // Marathi
    'bn', // Bengali
    'gu', // Gujarati
    'ta', // Tamil
    'te', // Telugu
  ];

  /// Get language display names
  static Map<String, String> get languageDisplayNames => {
    'en': 'English',
    'hi': 'हिंदी',
    'mr': 'मराठी',
    'bn': 'বাংলা',
    'gu': 'ગુજરાતી',
    'ta': 'தமிழ்',
    'te': 'తెలుగు',
  };

  /// Check if a language code is supported
  static bool isLanguageSupported(String languageCode) {
    return supportedLanguageCodes.contains(languageCode);
  }
}

/// Extension to easily access localization provider from context
extension LocalizationContext on BuildContext {
  /// Get current user's language code
  String get userLanguageCode {
    try {
      return Provider.of<LocalizationProvider>(this, listen: false).currentLanguageCode;
    } catch (e) {
      return 'en'; // Fallback to English
    }
  }

  /// Get Firebase localization service
  FirebaseLocalizationService get localizationService {
    try {
      return Provider.of<LocalizationProvider>(this, listen: false).localizationService;
    } catch (e) {
      return FirebaseLocalizationService(); // Fallback to new instance
    }
  }
}
