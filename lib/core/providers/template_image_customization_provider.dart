import 'package:flutter/material.dart';

class TemplateImageCustomization {
  final Offset offset;
  final double scale;
  final String shape;
  final String source;
  final String? customUrl;

  TemplateImageCustomization({
    this.offset = Offset.zero,
    this.scale = 1.0,
    this.shape = 'rounded_rectangle',
    this.source = 'auto',
    this.customUrl,
  });

  TemplateImageCustomization copyWith({
    Offset? offset,
    double? scale,
    String? shape,
    String? source,
    String? customUrl,
  }) {
    return TemplateImageCustomization(
      offset: offset ?? this.offset,
      scale: scale ?? this.scale,
      shape: shape ?? this.shape,
      source: source ?? this.source,
      customUrl: customUrl ?? this.customUrl,
    );
  }
}

class AdditionalImageCustomization {
  final Offset offset;
  final double scale;
  final String? imageUrl;

  AdditionalImageCustomization({
    this.offset = Offset.zero,
    this.scale = 1.0,
    this.imageUrl,
  });

  AdditionalImageCustomization copyWith({
    Offset? offset,
    double? scale,
    String? imageUrl,
  }) {
    return AdditionalImageCustomization(
      offset: offset ?? this.offset,
      scale: scale ?? this.scale,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }
}

class TemplateImageCustomizationProvider extends ChangeNotifier {
  // Main profile image customization
  TemplateImageCustomization _mainImageCustomization = TemplateImageCustomization();
  TemplateImageCustomization get mainImageCustomization => _mainImageCustomization;

  // Additional images customizations (using a map to store multiple images)
  final Map<String, AdditionalImageCustomization> _additionalImages = {};
  Map<String, AdditionalImageCustomization> get additionalImages => _additionalImages;

  // Update main image position
  void updateMainImageOffset(Offset offset) {
    _mainImageCustomization = _mainImageCustomization.copyWith(offset: offset);
    notifyListeners();
  }

  // Update main image scale
  void updateMainImageScale(double scale) {
    _mainImageCustomization = _mainImageCustomization.copyWith(scale: scale);
    notifyListeners();
  }

  // Update main image shape
  void updateMainImageShape(String shape) {
    _mainImageCustomization = _mainImageCustomization.copyWith(shape: shape);
    notifyListeners();
  }

  // Update main image source
  void updateMainImageSource(String source, {String? customUrl}) {
    _mainImageCustomization = _mainImageCustomization.copyWith(
      source: source,
      customUrl: customUrl,
    );
    notifyListeners();
  }

  // Add or update additional image
  void updateAdditionalImage(String id, {
    Offset? offset,
    double? scale,
    String? imageUrl,
  }) {
    final existing = _additionalImages[id];
    _additionalImages[id] = (existing ?? AdditionalImageCustomization()).copyWith(
      offset: offset,
      scale: scale,
      imageUrl: imageUrl,
    );
    notifyListeners();
  }

  // Remove additional image
  void removeAdditionalImage(String id) {
    _additionalImages.remove(id);
    notifyListeners();
  }

  // Clear all additional images
  void clearAdditionalImages() {
    _additionalImages.clear();
    notifyListeners();
  }

  // Reset all customizations
  void resetAll() {
    _mainImageCustomization = TemplateImageCustomization();
    _additionalImages.clear();
    notifyListeners();
  }
} 