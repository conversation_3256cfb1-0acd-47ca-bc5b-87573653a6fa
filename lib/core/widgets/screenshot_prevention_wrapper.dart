import 'package:flutter/material.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:no_screenshot/screenshot_snapshot.dart';
import '../services/screenshot_prevention_service.dart';
import '../utils/logger.dart';

/// A widget that wraps its child with screenshot prevention functionality
class ScreenshotPreventionWrapper extends StatefulWidget {
  final Widget child;
  final bool showWarningOnDetection;

  const ScreenshotPreventionWrapper({
    super.key,
    required this.child,
    this.showWarningOnDetection = true,
  });

  @override
  State<ScreenshotPreventionWrapper> createState() => _ScreenshotPreventionWrapperState();
}

class _ScreenshotPreventionWrapperState extends State<ScreenshotPreventionWrapper> {
  final ScreenshotPreventionService _screenshotService = ScreenshotPreventionService();

  @override
  void initState() {
    super.initState();
    _enableScreenshotPrevention();
    _listenForScreenshots();
  }

  Future<void> _enableScreenshotPrevention() async {
    await _screenshotService.enableScreenshotPrevention();
  }

  void _listenForScreenshots() {
    if (widget.showWarningOnDetection) {
      _screenshotService.screenshotStream.listen((event) {
        if (event.wasScreenshotTaken && mounted) {
          AppLogger.warning('Screenshot detected in ScreenshotPreventionWrapper');
          _screenshotService.showScreenshotWarningDialog(context);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
