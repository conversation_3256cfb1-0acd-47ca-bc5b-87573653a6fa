import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A fancy card with gradient background and shadow
class FancyCard extends StatelessWidget {
  final Widget child;
  final LinearGradient? gradient;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double elevation;
  final bool useGlassmorphism;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final double? width;
  final double? height;

  const FancyCard({
    Key? key,
    required this.child,
    this.gradient,
    this.borderRadius = 16.0,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.all(8),
    this.elevation = 4.0,
    this.useGlassmorphism = false,
    this.onTap,
    this.backgroundColor,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final BoxDecoration decoration = useGlassmorphism
        ? _buildGlassmorphismDecoration()
        : _buildGradientDecoration();

    final Widget cardContent = Container(
      width: width,
      height: height,
      padding: padding,
      decoration: decoration,
      child: child,
    );

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: Container(
          margin: margin,
          child: cardContent,
        ),
      );
    }

    return Container(
      margin: margin,
      child: cardContent,
    );
  }

  BoxDecoration _buildGradientDecoration() {
    return BoxDecoration(
      gradient: gradient ?? AppTheme.lightGradient,
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: [
        BoxShadow(
          color: AppTheme.primaryBlue.withOpacity(0.15),
          blurRadius: elevation * 2.5,
          offset: Offset(0, elevation),
        ),
      ],
      color: backgroundColor,
    );
  }

  BoxDecoration _buildGlassmorphismDecoration() {
    return BoxDecoration(
      gradient: LinearGradient(
        colors: [
          Colors.white.withOpacity(0.5),
          Colors.white.withOpacity(0.3),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: [
        BoxShadow(
          color: AppTheme.primaryBlue.withOpacity(0.1),
          blurRadius: elevation * 2,
          offset: Offset(0, elevation / 2),
        ),
      ],
      border: Border.all(
        color: Colors.white.withOpacity(0.5),
        width: 1.5,
      ),
    );
  }
}

/// A fancy card with gradient border
class GradientBorderCard extends StatelessWidget {
  final Widget child;
  final LinearGradient? gradient;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double elevation;
  final VoidCallback? onTap;
  final double borderWidth;

  const GradientBorderCard({
    Key? key,
    required this.child,
    this.gradient,
    this.borderRadius = 16.0,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.all(8),
    this.elevation = 4.0,
    this.onTap,
    this.borderWidth = 2.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        gradient: gradient ?? AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryBlue.withOpacity(0.15),
            blurRadius: elevation * 2.5,
            offset: Offset(0, elevation),
          ),
        ],
      ),
      child: Container(
        margin: EdgeInsets.all(borderWidth),
        decoration: BoxDecoration(
          color: AppTheme.backgroundWhite,
          borderRadius: BorderRadius.circular(borderRadius - borderWidth),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(borderRadius - borderWidth),
            child: Padding(
              padding: padding,
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}
