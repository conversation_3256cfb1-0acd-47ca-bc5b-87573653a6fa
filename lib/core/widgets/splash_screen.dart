import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../services/local_storage_service.dart';

/// A splash screen that displays different logos based on premium status
class SplashScreen extends StatefulWidget {
  final Widget? child;

  const SplashScreen({super.key, this.child});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  bool _showSplash = true;

  @override
  void initState() {
    super.initState();
    // Start a timer to hide the splash screen after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showSplash = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    // Save premium status to local storage during splash screen
    LocalStorageService.savePremiumStatus(isPremium);

    // We'll handle app icon changes in the ThemeProvider instead of here

    // If splash screen should not be shown, return the child widget
    if (!_showSplash) {
      return widget.child ?? const SizedBox.shrink();
    }

    // Otherwise, show the splash screen
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: isPremium
              ? const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF1A1A1A), // Dark background for premium
                    Color(0xFF000000),
                  ],
                )
              : const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.white,
                    Color(0xFFF5F5F5),
                  ],
                ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Display different logos based on premium status
              Image.asset(
                isPremium ? 'assets/icons/premium_logo.png' : 'assets/icons/logo.png',
                width: 200,
                height: 200,
              ),
              const SizedBox(height: 24),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  isPremium ? const Color(0xFFD4AF37) : Colors.blue,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
