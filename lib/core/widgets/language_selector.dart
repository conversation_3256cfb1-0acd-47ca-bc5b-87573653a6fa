import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/language_provider.dart';
import '../providers/theme_provider.dart';
import '../theme/app_theme.dart';
import '../services/language_service.dart';
import '../../l10n/generated/app_localizations.dart';

/// A compact language selector widget that can be used in settings or other screens
class LanguageSelector extends StatelessWidget {
  final bool showLabel;
  final bool isCompact;
  final VoidCallback? onLanguageChanged;

  const LanguageSelector({
    super.key,
    this.showLabel = true,
    this.isCompact = false,
    this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isPremium = themeProvider.isPremium;

    if (isCompact) {
      return _buildCompactSelector(context, languageProvider, isPremium);
    } else {
      return _buildFullSelector(context, languageProvider, isPremium);
    }
  }

  Widget _buildCompactSelector(BuildContext context, LanguageProvider languageProvider, bool isPremium) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isPremium
            ? AppTheme.premiumDarkGrey.withAlpha(128) // 0.5 opacity
            : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isPremium
              ? AppTheme.premiumGold.withAlpha(77) // 0.3 opacity
              : AppTheme.primaryBlue.withAlpha(77),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: languageProvider.currentLanguageCode,
          icon: Icon(
            Icons.arrow_drop_down,
            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
          ),
          style: TextStyle(
            color: isPremium ? Colors.white : AppTheme.textRichBlack,
            fontSize: 14,
          ),
          dropdownColor: isPremium ? AppTheme.premiumDarkGrey : Colors.white,
          items: LanguageService.getSupportedLanguagesList().map((language) {
            return DropdownMenuItem<String>(
              value: language['code'],
              child: Text(
                language['displayName']!,
                style: TextStyle(
                  color: isPremium ? Colors.white : AppTheme.textRichBlack,
                ),
              ),
            );
          }).toList(),
          onChanged: languageProvider.isLoading ? null : (String? newLanguageCode) {
            if (newLanguageCode != null && newLanguageCode != languageProvider.currentLanguageCode) {
              _changeLanguage(context, languageProvider, newLanguageCode);
            }
          },
        ),
      ),
    );
  }

  Widget _buildFullSelector(BuildContext context, LanguageProvider languageProvider, bool isPremium) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showLabel) ...[
          Text(
            AppLocalizations.of(context)!.language,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: isPremium
                ? AppTheme.premiumDarkGrey.withAlpha(128) // 0.5 opacity
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isPremium
                  ? AppTheme.premiumGold.withAlpha(77) // 0.3 opacity
                  : AppTheme.primaryBlue.withAlpha(77),
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: languageProvider.currentLanguageCode,
              icon: Icon(
                Icons.arrow_drop_down,
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
              style: TextStyle(
                color: isPremium ? Colors.white : AppTheme.textRichBlack,
                fontSize: 16,
              ),
              dropdownColor: isPremium ? AppTheme.premiumDarkGrey : Colors.white,
              isExpanded: true,
              items: LanguageService.getSupportedLanguagesList().map((language) {
                return DropdownMenuItem<String>(
                  value: language['code'],
                  child: Row(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: isPremium
                              ? AppTheme.premiumGold.withAlpha(51) // 0.2 opacity
                              : AppTheme.primaryBlue.withAlpha(51),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Center(
                          child: Text(
                            language['code']!.toUpperCase(),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 10,
                              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              language['displayName']!,
                              style: TextStyle(
                                color: isPremium ? Colors.white : AppTheme.textRichBlack,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              language['englishName']!,
                              style: TextStyle(
                                color: isPremium
                                    ? Colors.white.withAlpha(179) // 0.7 opacity
                                    : AppTheme.secondaryText,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: languageProvider.isLoading ? null : (String? newLanguageCode) {
                if (newLanguageCode != null && newLanguageCode != languageProvider.currentLanguageCode) {
                  _changeLanguage(context, languageProvider, newLanguageCode);
                }
              },
            ),
          ),
        ),
        if (languageProvider.isLoading) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context)!.changingLanguage,
                style: TextStyle(
                  fontSize: 12,
                  color: isPremium
                      ? Colors.white.withAlpha(179) // 0.7 opacity
                      : AppTheme.secondaryText,
                ),
              ),
            ],
          ),
        ],
        if (languageProvider.error != null) ...[
          const SizedBox(height: 8),
          Text(
            languageProvider.error!,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.errorRed,
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _changeLanguage(BuildContext context, LanguageProvider languageProvider, String newLanguageCode) async {
    try {
      final success = await languageProvider.changeLanguage(newLanguageCode);

      if (success && context.mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Language changed to ${LanguageService.getDisplayName(newLanguageCode)}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        // Call callback if provided
        onLanguageChanged?.call();
      } else if (context.mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to change language. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// A simple language indicator that shows current language
class LanguageIndicator extends StatelessWidget {
  final bool showFullName;

  const LanguageIndicator({
    super.key,
    this.showFullName = false,
  });

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isPremium
            ? AppTheme.premiumGold.withAlpha(51) // 0.2 opacity
            : AppTheme.primaryBlue.withAlpha(51),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.language,
            size: 16,
            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
          ),
          const SizedBox(width: 4),
          Text(
            showFullName
                ? languageProvider.currentLanguageDisplayName
                : languageProvider.currentLanguageCode.toUpperCase(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
        ],
      ),
    );
  }
}
