import 'dart:ui';
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// A container with gradient background
class GradientContainer extends StatelessWidget {
  final Widget child;
  final LinearGradient? gradient;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final List<BoxShadow>? boxShadow;
  final double? width;
  final double? height;
  final Alignment alignment;
  final Border? border;

  const GradientContainer({
    super.key,
    required this.child,
    this.gradient,
    this.borderRadius = 16.0,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.boxShadow,
    this.width,
    this.height,
    this.alignment = Alignment.center,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding,
      alignment: alignment,
      decoration: BoxDecoration(
        gradient: gradient ?? AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: boxShadow ?? AppTheme.lightShadow,
        border: border,
      ),
      child: child,
    );
  }
}

/// A container with a gradient overlay
class GradientOverlayContainer extends StatelessWidget {
  final Widget child;
  final LinearGradient? gradient;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double? width;
  final double? height;
  final Alignment alignment;
  final double overlayOpacity;

  const GradientOverlayContainer({
    super.key,
    required this.child,
    this.gradient,
    this.borderRadius = 16.0,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.width,
    this.height,
    this.alignment = Alignment.center,
    this.overlayOpacity = 0.7,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding,
      alignment: alignment,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: AppTheme.lightShadow,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Stack(
          children: [
            // Main content
            child,

            // Gradient overlay
            Positioned.fill(
              child: Opacity(
                opacity: overlayOpacity,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: gradient ?? AppTheme.primaryGradient,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// A glassmorphism container with blur effect
class GlassmorphicContainer extends StatelessWidget {
  final Widget child;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double? width;
  final double? height;
  final Alignment alignment;
  final Color? borderColor;
  final double borderWidth;
  final double blurIntensity;
  final Color backgroundColor;

  const GlassmorphicContainer({
    super.key,
    required this.child,
    this.borderRadius = 16.0,
    this.padding = const EdgeInsets.all(16),
    this.margin = EdgeInsets.zero,
    this.width,
    this.height,
    this.alignment = Alignment.center,
    this.borderColor,
    this.borderWidth = 1.5,
    this.blurIntensity = 10.0,
    this.backgroundColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding,
      alignment: alignment,
      decoration: BoxDecoration(
        color: backgroundColor.withAlpha(51), // 0.2 * 255 = 51
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: borderColor ?? Colors.white.withAlpha(128), // 0.5 * 255 = 128
          width: borderWidth,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryBlue.withAlpha(26), // 0.1 * 255 = 26
            blurRadius: blurIntensity,
            spreadRadius: 1,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius - borderWidth),
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: blurIntensity / 2,
            sigmaY: blurIntensity / 2,
          ),
          child: child,
        ),
      ),
    );
  }
}
