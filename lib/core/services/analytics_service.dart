import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Initialize analytics service
  Future<void> initialize() async {
    try {
      print('🚀 [AnalyticsService] Initializing Firebase Analytics...');
      
      // Set analytics collection enabled
      await _analytics.setAnalyticsCollectionEnabled(true);
      
      // Set user properties for better segmentation
      await _setInitialUserProperties();
      
      print('✅ [AnalyticsService] Firebase Analytics initialized successfully');
    } catch (e) {
      print('❌ [AnalyticsService] Error initializing Firebase Analytics: $e');
    }
  }

  // Set initial user properties for segmentation
  Future<void> _setInitialUserProperties() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('⚠️ [AnalyticsService] No authenticated user for setting properties');
        return;
      }

      // Get user data from Firestore
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        print('⚠️ [AnalyticsService] User document not found in Firestore: ${user.uid}');
        return;
      }

      final userData = userDoc.data()!;
      
      // Set user properties for Firebase Analytics segmentation
      await _analytics.setUserId(id: user.uid);
      
      // Set user type property
      final userType = userData['userType'] as String? ?? 'regular';
      await _analytics.setUserProperty(name: 'user_type', value: userType);
      
      // Set premium status
      final isPremium = userData['isPremium'] as bool? ?? false;
      await _analytics.setUserProperty(name: 'is_premium', value: isPremium.toString());
      
      // Set platform
      await _analytics.setUserProperty(name: 'platform', value: 'flutter');
      
      // Set profile completion status
      final isProfileComplete = userData['isProfileComplete'] as bool? ?? false;
      await _analytics.setUserProperty(name: 'profile_complete', value: isProfileComplete.toString());

      print('✅ [AnalyticsService] User properties set for segmentation');
      print('   User ID: ${user.uid}');
      print('   User Type: $userType');
      print('   Is Premium: $isPremium');
      print('   Profile Complete: $isProfileComplete');
      
    } catch (e) {
      print('❌ [AnalyticsService] Error setting user properties: $e');
    }
  }

  // Log authentication events
  Future<void> logLoginSuccess(String method) async {
    try {
      await _analytics.logLogin(loginMethod: method);
      await _analytics.logEvent(
        name: 'login_success',
        parameters: {
          'method': method,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      print('📊 [AnalyticsService] Logged login success: $method');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging login: $e');
    }
  }

  Future<void> logSignUp(String method) async {
    try {
      await _analytics.logSignUp(signUpMethod: method);
      await _analytics.logEvent(
        name: 'sign_up_success',
        parameters: {
          'method': method,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      print('📊 [AnalyticsService] Logged sign up: $method');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging sign up: $e');
    }
  }

  // Log user engagement events
  Future<void> logTemplateView(String templateId, String templateName) async {
    try {
      await _analytics.logEvent(
        name: 'template_view',
        parameters: {
          'template_id': templateId,
          'template_name': templateName,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      print('📊 [AnalyticsService] Logged template view: $templateName');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging template view: $e');
    }
  }

  Future<void> logTemplateCreate(String templateId, String templateName) async {
    try {
      await _analytics.logEvent(
        name: 'template_create',
        parameters: {
          'template_id': templateId,
          'template_name': templateName,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      print('📊 [AnalyticsService] Logged template create: $templateName');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging template create: $e');
    }
  }

  Future<void> logPremiumUpgrade() async {
    try {
      await _analytics.logEvent(
        name: 'premium_upgrade',
        parameters: {
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      // Update user property
      await _analytics.setUserProperty(name: 'is_premium', value: 'true');
      
      print('📊 [AnalyticsService] Logged premium upgrade');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging premium upgrade: $e');
    }
  }

  // Log user type selection
  Future<void> logUserTypeSelection(String userType) async {
    try {
      await _analytics.logEvent(
        name: 'user_type_selected',
        parameters: {
          'user_type': userType,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      // Update user property
      await _analytics.setUserProperty(name: 'user_type', value: userType);
      
      print('📊 [AnalyticsService] Logged user type selection: $userType');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging user type selection: $e');
    }
  }

  // Log language selection
  Future<void> logLanguageSelection(String language) async {
    try {
      await _analytics.logEvent(
        name: 'language_selected',
        parameters: {
          'language': language,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      // Update user property
      await _analytics.setUserProperty(name: 'language', value: language);
      
      print('📊 [AnalyticsService] Logged language selection: $language');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging language selection: $e');
    }
  }

  // Log profile completion
  Future<void> logProfileCompletion() async {
    try {
      await _analytics.logEvent(
        name: 'profile_completed',
        parameters: {
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      
      // Update user property
      await _analytics.setUserProperty(name: 'profile_complete', value: 'true');
      
      print('📊 [AnalyticsService] Logged profile completion');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging profile completion: $e');
    }
  }

  // Log notification events
  Future<void> logNotificationReceived(String notificationId, String title) async {
    try {
      await _analytics.logEvent(
        name: 'notification_received',
        parameters: {
          'notification_id': notificationId,
          'title': title,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      print('📊 [AnalyticsService] Logged notification received: $title');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging notification received: $e');
    }
  }

  Future<void> logNotificationOpened(String notificationId, String title) async {
    try {
      await _analytics.logEvent(
        name: 'notification_opened',
        parameters: {
          'notification_id': notificationId,
          'title': title,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      print('📊 [AnalyticsService] Logged notification opened: $title');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging notification opened: $e');
    }
  }

  // Log app usage events
  Future<void> logAppOpen() async {
    try {
      await _analytics.logAppOpen();
      print('📊 [AnalyticsService] Logged app open');
    } catch (e) {
      print('❌ [AnalyticsService] Error logging app open: $e');
    }
  }

  // Update user properties when they change
  Future<void> updateUserProperties() async {
    await _setInitialUserProperties();
  }

  // Get Firebase Analytics instance for advanced usage
  FirebaseAnalytics get analytics => _analytics;
} 