import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'analytics_service.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();

  String? _fcmToken;
  String? get fcmToken => _fcmToken;

  // Initialize notification service
  Future<void> initialize() async {
    print('🚀 [NotificationService] Initializing notification service...');
    await _initializeLocalNotifications();
    await _initializeFirebaseMessaging();
    await _requestPermissions();
    await _getFCMToken();
    await _setupMessageHandlers();

    // 🔥 CRITICAL: Subscribe ALL users to all_users topic immediately
    // This ensures everyone gets important announcements regardless of login status
    await subscribeToTopic('all_users');
    print('✅ [NotificationService] All users subscribed to all_users topic');

    // Initialize user-specific subscriptions if user is logged in
    await initializeUserSubscriptions();

    print('✅ [NotificationService] Notification service initialization completed');
  }

  // Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    print('🔵 [NotificationService] Initializing local notifications...');
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
    print('✅ [NotificationService] Local notifications initialized');
  }

  // Initialize Firebase messaging
  Future<void> _initializeFirebaseMessaging() async {
    print('🔵 [NotificationService] Initializing Firebase messaging...');
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    print('✅ [NotificationService] Firebase messaging initialized');
  }

  // Request notification permissions
  Future<void> _requestPermissions() async {
    print('🔵 [NotificationService] Requesting notification permissions...');
    // Request FCM permissions
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    print('📋 [NotificationService] Notification permission status: ${settings.authorizationStatus}');
    print('📋 [NotificationService] Alert permission: ${settings.alert}');
    print('📋 [NotificationService] Badge permission: ${settings.badge}');
    print('📋 [NotificationService] Sound permission: ${settings.sound}');

    // Request local notification permissions for Android 13+
    if (Platform.isAndroid) {
      print('🔵 [NotificationService] Requesting Android local notification permissions...');
      await _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
      print('✅ [NotificationService] Android local notification permissions requested');
    }
  }

  // Get FCM token
  Future<void> _getFCMToken() async {
    try {
      print('🔵 [NotificationService] Getting FCM token...');
      _fcmToken = await _firebaseMessaging.getToken();
      print('✅ [NotificationService] FCM Token obtained: $_fcmToken');

      if (_fcmToken != null) {
        await _saveFCMTokenToFirestore();
        await _saveFCMTokenLocally();
      } else {
        print('⚠️ [NotificationService] FCM token is null!');
      }
    } catch (e) {
      print('❌ [NotificationService] Error getting FCM token: $e');
    }
  }

  // Save FCM token to Firestore
  Future<void> _saveFCMTokenToFirestore() async {
    try {
      print('🔵 [NotificationService] Saving FCM token to Firestore...');
      final user = FirebaseAuth.instance.currentUser;
      if (user != null && _fcmToken != null) {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .update({
          'fcmToken': _fcmToken,
          'lastTokenUpdate': FieldValue.serverTimestamp(),
          'platform': Platform.isAndroid ? 'android' : 'ios',
        });
        print('✅ [NotificationService] FCM token saved to Firestore for user: ${user.uid}');
      } else {
        print('⚠️ [NotificationService] Cannot save FCM token - user: ${user?.uid}, token: $_fcmToken');
      }
    } catch (e) {
      print('❌ [NotificationService] Error saving FCM token to Firestore: $e');
    }
  }

  // Save FCM token locally
  Future<void> _saveFCMTokenLocally() async {
    try {
      print('🔵 [NotificationService] Saving FCM token locally...');
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', _fcmToken!);
      print('✅ [NotificationService] FCM token saved locally');
    } catch (e) {
      print('❌ [NotificationService] Error saving FCM token locally: $e');
    }
  }

  // Setup message handlers
  Future<void> _setupMessageHandlers() async {
    print('🔵 [NotificationService] Setting up message handlers...');

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    print('✅ [NotificationService] Foreground message handler set up');

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    print('✅ [NotificationService] Background notification tap handler set up');

    // Handle notification tap when app is terminated
    RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      print('📱 [NotificationService] App opened from terminated state via notification: ${initialMessage.messageId}');
      _handleNotificationTap(initialMessage);
    } else {
      print('📱 [NotificationService] App opened normally (not from notification)');
    }

    // Listen for token refresh
    _firebaseMessaging.onTokenRefresh.listen((newToken) {
      print('🔄 [NotificationService] FCM token refreshed: $newToken');
      _fcmToken = newToken;
      _saveFCMTokenToFirestore();
      _saveFCMTokenLocally();
    });

    print('✅ [NotificationService] All message handlers set up');
  }

  // Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    print('📱 [NotificationService] Received foreground message: ${message.messageId}');
    print('📋 [NotificationService] Title: ${message.notification?.title}');
    print('📋 [NotificationService] Body: ${message.notification?.body}');
    print('📋 [NotificationService] Data: ${message.data}');

    // Log analytics event
    if (message.messageId != null && message.notification?.title != null) {
      AnalyticsService().logNotificationReceived(
        message.messageId!,
        message.notification!.title!,
      );
    }

    _showLocalNotification(message);
  }

  // Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    print('👆 [NotificationService] Notification tapped: ${message.messageId}');
    print('📋 [NotificationService] Data: ${message.data}');

    // Log analytics event
    if (message.messageId != null && message.notification?.title != null) {
      AnalyticsService().logNotificationOpened(
        message.messageId!,
        message.notification!.title!,
      );
    }

    _navigateToScreen(message.data);
  }

  // Handle local notification tap
  void _onNotificationTapped(NotificationResponse response) {
    print('👆 [NotificationService] Local notification tapped: ${response.payload}');
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      _navigateToScreen(data);
    }
  }

  // Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    print('🔔 [NotificationService] Showing local notification for message: ${message.messageId}');

    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'quickposters_channel',
      'QuickPosters Notifications',
      channelDescription: 'Notifications for QuickPosters app',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'QuickPosters',
      message.notification?.body ?? 'You have a new notification',
      notificationDetails,
      payload: jsonEncode(message.data),
    );

    print('✅ [NotificationService] Local notification shown');
  }

  // Navigate to appropriate screen based on notification data
  void _navigateToScreen(Map<String, dynamic> data) {
    // TODO: Implement navigation logic based on notification type
    final type = data['type'];
    final targetId = data['targetId'];

    print('📱 [NotificationService] Navigating to: $type with ID: $targetId');

    // Example navigation logic:
    // switch (type) {
    //   case 'template':
    //     // Navigate to template details
    //     break;
    //   case 'banner':
    //     // Navigate to banner details
    //     break;
    //   case 'announcement':
    //     // Navigate to announcements
    //     break;
    //   default:
    //     // Navigate to home
    //     break;
    // }
  }

  // Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      print('🔵 [NotificationService] Subscribing to topic: $topic');
      await _firebaseMessaging.subscribeToTopic(topic);
      print('✅ [NotificationService] Successfully subscribed to topic: $topic');
    } catch (e) {
      print('❌ [NotificationService] Error subscribing to topic $topic: $e');
    }
  }

  // Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      print('🔵 [NotificationService] Unsubscribing from topic: $topic');
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      print('✅ [NotificationService] Successfully unsubscribed from topic: $topic');
    } catch (e) {
      print('❌ [NotificationService] Error unsubscribing from topic $topic: $e');
    }
  }

  // Subscribe to user-specific topics based on user type (smart subscription)
  Future<void> subscribeToUserTopics(String userType, bool isPremium, {String? language}) async {
    print('🚀 [NotificationService] Smart subscription for userType: $userType, isPremium: $isPremium, language: ${language ?? 'english'}');

    // 🔵 1. USER TYPE-BASED TOPICS (Subscribe only to relevant user type)
    switch (userType) {
      case 'businessman':
        await subscribeToTopic('businessman');
        print('✅ [NotificationService] Subscribed to businessman topic');
        break;
      case 'politician':
        await subscribeToTopic('politician');
        print('✅ [NotificationService] Subscribed to politician topic');
        break;
      default:
        await subscribeToTopic('individual_user');
        print('✅ [NotificationService] Subscribed to individual_user topic');
        break;
    }

    // 🟡 2. PREMIUM STATUS TOPICS (Subscribe based on actual status)
    if (isPremium) {
      await subscribeToTopic('premium_users');
      print('✅ [NotificationService] Subscribed to premium_users topic');
    } else {
      await subscribeToTopic('free_users');
      print('✅ [NotificationService] Subscribed to free_users topic');
    }

    // 🟢 3. LANGUAGE-BASED TOPICS (Subscribe only to user's language)
    final userLanguage = language ?? 'english';
    switch (userLanguage.toLowerCase()) {
      case 'marathi':
      case 'mr':
        await subscribeToTopic('lang_marathi');
        print('✅ [NotificationService] Subscribed to lang_marathi topic');
        break;
      case 'hindi':
      case 'hi':
        await subscribeToTopic('lang_hindi');
        print('✅ [NotificationService] Subscribed to lang_hindi topic');
        break;
      case 'bengali':
      case 'bn':
        await subscribeToTopic('lang_bengali');
        print('✅ [NotificationService] Subscribed to lang_bengali topic');
        break;
      case 'gujarati':
      case 'gu':
        await subscribeToTopic('lang_gujarati');
        print('✅ [NotificationService] Subscribed to lang_gujarati topic');
        break;
      case 'tamil':
      case 'ta':
        await subscribeToTopic('lang_tamil');
        print('✅ [NotificationService] Subscribed to lang_tamil topic');
        break;
      case 'telugu':
      case 'te':
        await subscribeToTopic('lang_telugu');
        print('✅ [NotificationService] Subscribed to lang_telugu topic');
        break;
      case 'english':
      case 'en':
      default:
        await subscribeToTopic('lang_english');
        print('✅ [NotificationService] Subscribed to lang_english topic');
        break;
    }

    // 🔴 4. FEATURE-BASED TOPICS (Subscribe all authenticated users)
    await subscribeToTopic('new_templates');
    await subscribeToTopic('offers');
    await subscribeToTopic('app_updates');
    print('✅ [NotificationService] Subscribed to feature-based topics');

    // 🟠 5. ENGAGEMENT-BASED TOPICS (All authenticated users are active initially)
    await subscribeToTopic('active_users');
    print('✅ [NotificationService] Subscribed to active_users topic');

    print('✅ [NotificationService] Smart subscription completed successfully');
  }

  // Clear all notifications
  Future<void> clearAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  // Get notification history from local storage
  Future<List<Map<String, dynamic>>> getNotificationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getStringList('notification_history') ?? [];
      return historyJson.map((json) => jsonDecode(json) as Map<String, dynamic>).toList();
    } catch (e) {
      print('📋 [NotificationService] Error getting notification history: $e');
      return [];
    }
  }

  // Save notification to history
  Future<void> _saveNotificationToHistory(RemoteMessage message) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getStringList('notification_history') ?? [];

      final notification = {
        'id': message.messageId,
        'title': message.notification?.title,
        'body': message.notification?.body,
        'data': message.data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      historyJson.insert(0, jsonEncode(notification));

      // Keep only last 50 notifications
      if (historyJson.length > 50) {
        historyJson.removeRange(50, historyJson.length);
      }

      await prefs.setStringList('notification_history', historyJson);
    } catch (e) {
      print('❌ [NotificationService] Error saving notification to history: $e');
    }
  }

  // Initialize user-specific subscriptions after authentication
  Future<void> initializeUserSubscriptions() async {
    try {
      print('🚀 [NotificationService] Initializing user-specific subscriptions...');

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('⚠️ [NotificationService] No authenticated user found for subscription initialization');
        return;
      }

      // Get user data from Firestore to determine user type, premium status, and language
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        print('⚠️ [NotificationService] User document not found in Firestore: ${user.uid}');
        return;
      }

      final userData = userDoc.data()!;
      final userType = userData['userType'] as String? ?? 'individual_user';
      final isPremium = userData['isPremium'] as bool? ?? false;
      final language = userData['language'] as String? ?? 'english'; // Get user's preferred language

      print('📋 [NotificationService] User details for subscription:');
      print('   User ID: ${user.uid}');
      print('   User Type: $userType');
      print('   Is Premium: $isPremium');
      print('   Language: $language');

      // Subscribe to user-specific topics with language preference
      await subscribeToUserTopics(userType, isPremium, language: language);

      print('✅ [NotificationService] User-specific subscriptions initialized successfully');
    } catch (e) {
      print('❌ [NotificationService] Error initializing user subscriptions: $e');
    }
  }

  // Update subscriptions when user status changes (e.g., premium upgrade, language change)
  Future<void> updateUserSubscriptions({
    String? newUserType,
    bool? newPremiumStatus,
    String? newLanguage,
  }) async {
    try {
      print('🔄 [NotificationService] Updating user subscriptions...');

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('⚠️ [NotificationService] No authenticated user found for subscription update');
        return;
      }

      // Get current user data
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        print('⚠️ [NotificationService] User document not found in Firestore: ${user.uid}');
        return;
      }

      final userData = userDoc.data()!;
      final currentUserType = userData['userType'] as String? ?? 'individual_user';
      final currentPremiumStatus = userData['isPremium'] as bool? ?? false;
      final currentLanguage = userData['language'] as String? ?? 'english';

      // Use new values if provided, otherwise keep current
      final userType = newUserType ?? currentUserType;
      final isPremium = newPremiumStatus ?? currentPremiumStatus;
      final language = newLanguage ?? currentLanguage;

      // Handle premium status change
      if (newPremiumStatus != null && newPremiumStatus != currentPremiumStatus) {
        if (newPremiumStatus) {
          // User upgraded to premium
          await unsubscribeFromTopic('free_users');
          await subscribeToTopic('premium_users');
          print('✅ [NotificationService] Updated to premium subscription');
        } else {
          // User downgraded from premium
          await unsubscribeFromTopic('premium_users');
          await subscribeToTopic('free_users');
          print('✅ [NotificationService] Updated to free subscription');
        }
      }

      // Handle language change
      if (newLanguage != null && newLanguage != currentLanguage) {
        // Unsubscribe from old language
        switch (currentLanguage.toLowerCase()) {
          case 'marathi':
          case 'mr':
            await unsubscribeFromTopic('lang_marathi');
            break;
          case 'hindi':
          case 'hi':
            await unsubscribeFromTopic('lang_hindi');
            break;
          case 'english':
          case 'en':
          default:
            await unsubscribeFromTopic('lang_english');
            break;
        }

        // Subscribe to new language
        switch (newLanguage.toLowerCase()) {
          case 'marathi':
          case 'mr':
            await subscribeToTopic('lang_marathi');
            break;
          case 'hindi':
          case 'hi':
            await subscribeToTopic('lang_hindi');
            break;
          case 'english':
          case 'en':
          default:
            await subscribeToTopic('lang_english');
            break;
        }
        print('✅ [NotificationService] Updated language subscription to: $newLanguage');
      }

      // Handle user type change (rare but possible)
      if (newUserType != null && newUserType != currentUserType) {
        // Unsubscribe from old user type
        switch (currentUserType) {
          case 'businessman':
            await unsubscribeFromTopic('businessman');
            break;
          case 'politician':
            await unsubscribeFromTopic('politician');
            break;
          default:
            await unsubscribeFromTopic('individual_user');
            break;
        }

        // Subscribe to new user type
        switch (newUserType) {
          case 'businessman':
            await subscribeToTopic('businessman');
            break;
          case 'politician':
            await subscribeToTopic('politician');
            break;
          default:
            await subscribeToTopic('individual_user');
            break;
        }
        print('✅ [NotificationService] Updated user type subscription to: $newUserType');
      }

      print('✅ [NotificationService] User subscription update completed successfully');
    } catch (e) {
      print('❌ [NotificationService] Error updating user subscriptions: $e');
    }
  }

  /// Update language subscription when user changes language
  Future<void> updateLanguageSubscription(String newLanguageCode) async {
    try {
      print('🔄 [NotificationService] Updating language subscription to: $newLanguageCode');

      // Unsubscribe from all language topics
      final allLanguageCodes = ['en', 'hi', 'mr', 'bn', 'gu', 'ta', 'te'];
      for (final langCode in allLanguageCodes) {
        await unsubscribeFromTopic('lang_$langCode');
      }

      // Subscribe to new language topic
      await subscribeToTopic('lang_$newLanguageCode');

      print('✅ [NotificationService] Language subscription updated to: $newLanguageCode');
    } catch (e) {
      print('❌ [NotificationService] Error updating language subscription: $e');
    }
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('📱 [NotificationService] Handling background message: ${message.messageId}');
  // Handle background message processing here
}