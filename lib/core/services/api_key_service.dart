import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// Service for managing API keys
class ApiKeyService {
  static const String _nscaleApiKeyKey = 'nscale_api_key';
  static SharedPreferences? _prefs;

  /// Initialize the API key service
  static Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      AppLogger.info('API key service initialized');
    } catch (e) {
      AppLogger.error('Failed to initialize API key service', e);
    }
  }

  /// Save the nscale API key
  static Future<bool> saveNScaleApiKey(String apiKey) async {
    try {
      if (_prefs == null) {
        await initialize();
      }
      
      final result = await _prefs!.setString(_nscaleApiKeyKey, apiKey);
      AppLogger.info('nscale API key saved: $result');
      return result;
    } catch (e) {
      AppLogger.error('Failed to save nscale API key', e);
      return false;
    }
  }

  /// Get the nscale API key
  /// Returns null if not found or on error
  static String? getNScaleApiKey() {
    try {
      if (_prefs == null) {
        AppLogger.warning('API key service not initialized when getting nscale API key');
        return null;
      }
      
      final apiKey = _prefs!.getString(_nscaleApiKeyKey);
      return apiKey;
    } catch (e) {
      AppLogger.error('Failed to get nscale API key', e);
      return null;
    }
  }

  /// Check if the nscale API key is set
  static bool hasNScaleApiKey() {
    return getNScaleApiKey() != null;
  }

  /// Clear the nscale API key
  static Future<bool> clearNScaleApiKey() async {
    try {
      if (_prefs == null) {
        await initialize();
      }
      
      final result = await _prefs!.remove(_nscaleApiKeyKey);
      AppLogger.info('nscale API key cleared: $result');
      return result;
    } catch (e) {
      AppLogger.error('Failed to clear nscale API key', e);
      return false;
    }
  }
}
