import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/logger.dart';

/// Service for handling Firebase backend data localization
class FirebaseLocalizationService {
  final FirebaseFirestore _firestore;

  FirebaseLocalizationService({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Get localized text from Firebase document
  ///
  /// This method looks for translations in the document and returns
  /// the text in the user's preferred language, falling back to English
  String getLocalizedText({
    required Map<String, dynamic> data,
    required String fieldName,
    required String languageCode,
    String fallbackLanguage = 'en',
  }) {
    try {
      // First, try to get from translations field
      final translations = data['translations'] as Map<String, dynamic>?;
      if (translations != null) {
        // Try user's preferred language
        final translatedText = translations[fieldName]?[languageCode] as String?;
        if (translatedText != null && translatedText.isNotEmpty) {
          return translatedText;
        }

        // Fallback to English
        final fallbackText = translations[fieldName]?[fallbackLanguage] as String?;
        if (fallbackText != null && fallbackText.isNotEmpty) {
          return fallbackText;
        }
      }

      // Fallback to original field (usually English)
      final originalText = data[fieldName] as String?;
      if (originalText != null && originalText.isNotEmpty) {
        return originalText;
      }

      // Last resort fallback
      return fieldName;
    } catch (e) {
      AppLogger.error('Error getting localized text for $fieldName', e);
      return data[fieldName] as String? ?? fieldName;
    }
  }

  /// Get localized list (for dropdown options, tags, etc.)
  List<String> getLocalizedList({
    required Map<String, dynamic> data,
    required String fieldName,
    required String languageCode,
    String fallbackLanguage = 'en',
  }) {
    try {
      // First, try to get from translations field
      final translations = data['translations'] as Map<String, dynamic>?;
      if (translations != null) {
        // Try user's preferred language
        final translatedList = translations[fieldName]?[languageCode] as List<dynamic>?;
        if (translatedList != null && translatedList.isNotEmpty) {
          return translatedList.cast<String>();
        }

        // Fallback to English
        final fallbackList = translations[fieldName]?[fallbackLanguage] as List<dynamic>?;
        if (fallbackList != null && fallbackList.isNotEmpty) {
          return fallbackList.cast<String>();
        }
      }

      // Fallback to original field
      final originalList = data[fieldName] as List<dynamic>?;
      if (originalList != null && originalList.isNotEmpty) {
        return originalList.cast<String>();
      }

      return [];
    } catch (e) {
      AppLogger.error('Error getting localized list for $fieldName', e);
      final originalList = data[fieldName] as List<dynamic>?;
      return originalList?.cast<String>() ?? [];
    }
  }

  /// Prepare document for translation by Firebase Extension
  ///
  /// This method structures the data so that the Firebase Extension
  /// can automatically translate it
  Map<String, dynamic> prepareForTranslation({
    required Map<String, dynamic> data,
    required List<String> fieldsToTranslate,
  }) {
    final preparedData = Map<String, dynamic>.from(data);

    // Create input object for translation
    final inputForTranslation = <String, dynamic>{};

    for (final field in fieldsToTranslate) {
      final value = data[field];
      if (value != null) {
        if (value is String && value.isNotEmpty) {
          inputForTranslation[field] = value;
        } else if (value is List && value.isNotEmpty) {
          inputForTranslation[field] = value;
        }
      }
    }

    // Add the input field that Firebase Extension will watch
    preparedData['input'] = inputForTranslation;

    return preparedData;
  }

  /// Update document with localized content
  Future<void> updateDocumentWithTranslations({
    required String collection,
    required String documentId,
    required Map<String, dynamic> translations,
  }) async {
    try {
      await _firestore.collection(collection).doc(documentId).update({
        'translations': translations,
        'lastTranslationUpdate': FieldValue.serverTimestamp(),
      });

      AppLogger.info('Updated translations for $collection/$documentId');
    } catch (e) {
      AppLogger.error('Error updating translations', e);
      rethrow;
    }
  }

  /// Get user's preferred language from their profile
  Future<String> getUserLanguage(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        return userData['languageCode'] as String? ?? 'en';
      }
      return 'en';
    } catch (e) {
      AppLogger.error('Error getting user language', e);
      return 'en';
    }
  }

  /// Batch translate multiple documents
  Future<void> batchTranslateCollection({
    required String collection,
    required List<String> fieldsToTranslate,
    int batchSize = 10,
  }) async {
    try {
      AppLogger.info('Starting batch translation for $collection');

      final querySnapshot = await _firestore.collection(collection).get();
      final docs = querySnapshot.docs;

      for (int i = 0; i < docs.length; i += batchSize) {
        final batch = _firestore.batch();
        final endIndex = (i + batchSize < docs.length) ? i + batchSize : docs.length;

        for (int j = i; j < endIndex; j++) {
          final doc = docs[j];
          final data = doc.data();

          // Check if document needs translation
          if (data['translations'] == null) {
            final preparedData = prepareForTranslation(
              data: data,
              fieldsToTranslate: fieldsToTranslate,
            );

            batch.update(doc.reference, {
              'input': preparedData['input'],
              'needsTranslation': true,
            });
          }
        }

        await batch.commit();
        AppLogger.info('Processed batch ${(i / batchSize).floor() + 1}');
      }

      AppLogger.info('Batch translation setup completed for $collection');
    } catch (e) {
      AppLogger.error('Error in batch translation', e);
      rethrow;
    }
  }
}
