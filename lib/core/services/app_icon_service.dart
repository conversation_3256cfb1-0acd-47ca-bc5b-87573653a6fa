import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dynamic_icon/flutter_dynamic_icon.dart';
import 'custom_app_icon_service.dart';

/// Service to handle dynamic app icon changes based on premium status
class AppIconService {

  /// Initialize the app icon service
  /// This should be called once at app startup
  static Future<void> initialize() async {
    try {
      if (Platform.isAndroid) {
        await CustomAppIconService.initialize();
        debugPrint('✅ Android app icon service initialized');
      } else if (Platform.isIOS) {
        // Check if iOS supports alternate icons
        final bool supportsAlternateIcons = await FlutterDynamicIcon.supportsAlternateIcons;
        debugPrint('✅ iOS app icon service initialized - Supports alternate icons: $supportsAlternateIcons');

        // Validate icon sets exist
        await _validateIconSets();
      }
    } catch (e) {
      debugPrint('❌ Failed to initialize app icon service: $e');
    }
  }

  /// Validate that the icon sets are properly configured
  static Future<void> _validateIconSets() async {
    try {
      // Get current icon to test the API
      final String? currentIcon = await FlutterDynamicIcon.getAlternateIconName();
      debugPrint('📱 Current icon during validation: $currentIcon');

      // Test setting to regular icon
      await FlutterDynamicIcon.setAlternateIconName('regular', showAlert: false);
      await Future.delayed(const Duration(milliseconds: 500));

      final String? regularIcon = await FlutterDynamicIcon.getAlternateIconName();
      debugPrint('🔍 Regular icon test result: $regularIcon');

      // Test setting to premium icon
      await FlutterDynamicIcon.setAlternateIconName('premium', showAlert: false);
      await Future.delayed(const Duration(milliseconds: 500));

      final String? premiumIcon = await FlutterDynamicIcon.getAlternateIconName();
      debugPrint('🔍 Premium icon test result: $premiumIcon');

      // Reset to original icon
      await FlutterDynamicIcon.setAlternateIconName(currentIcon, showAlert: false);

      debugPrint('✅ Icon validation completed');
    } catch (e) {
      debugPrint('❌ Icon validation failed: $e');
    }
  }

  /// Check if the device supports changing app icons
  static Future<bool> get supportsAlternateIcons async {
    try {
      if (Platform.isIOS) {
        return await FlutterDynamicIcon.supportsAlternateIcons;
      } else if (Platform.isAndroid) {
        // Android implementation always returns true since we can't easily check
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ Error checking if device supports alternate icons: $e');
      return false;
    }
  }

  /// Set the app icon based on premium status
  static Future<bool> setAppIcon(bool isPremium) async {
    debugPrint('🔄 Setting app icon for premium status: $isPremium');
    try {
      if (Platform.isIOS) {
        return await _setIOSAppIcon(isPremium);
      } else if (Platform.isAndroid) {
        return await CustomAppIconService.setAppIcon(isPremium);
      }
      return false;
    } on PlatformException catch (e) {
      debugPrint('❌ Platform exception changing app icon: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('❌ Error changing app icon: $e');
      return false;
    }
  }

  /// Set iOS app icon using flutter_dynamic_icon
  static Future<bool> _setIOSAppIcon(bool isPremium) async {
    try {
      // Check if device supports alternate icons
      if (!await FlutterDynamicIcon.supportsAlternateIcons) {
        debugPrint('⚠️ iOS device does not support alternate icons');
        return false;
      }

      // Set the appropriate icon
      final String iconName = isPremium ? 'premium' : 'regular';

      debugPrint('🍎 Setting iOS icon to: $iconName (isPremium: $isPremium)');

      // Get current icon before change
      final String? currentIconBefore = await FlutterDynamicIcon.getAlternateIconName();
      debugPrint('📱 Current icon before change: $currentIconBefore');

      // Use showAlert: false to avoid the system alert
      await FlutterDynamicIcon.setAlternateIconName(
        iconName,
        showAlert: false, // This uses private API to avoid alert
      );

      // Add a small delay to allow the system to process the change
      await Future.delayed(const Duration(milliseconds: 1000));

      // Verify the icon was set
      final String? currentIcon = await FlutterDynamicIcon.getAlternateIconName();
      final bool success = currentIcon == iconName;

      debugPrint('✅ iOS icon change ${success ? 'successful' : 'failed'} - Current icon: $currentIcon, Expected: $iconName');

      // If the change failed, try to reset to default and try again
      if (!success) {
        debugPrint('🔄 Icon change failed, attempting to reset and retry...');

        try {
          // Reset to default first
          await FlutterDynamicIcon.setAlternateIconName(null, showAlert: false);
          await Future.delayed(const Duration(milliseconds: 500));

          // Try setting the icon again
          await FlutterDynamicIcon.setAlternateIconName(iconName, showAlert: false);
          await Future.delayed(const Duration(milliseconds: 1000));

          // Verify again
          final String? retryCurrentIcon = await FlutterDynamicIcon.getAlternateIconName();
          final bool retrySuccess = retryCurrentIcon == iconName;

          debugPrint('🔄 Retry result: ${retrySuccess ? 'successful' : 'failed'} - Current icon: $retryCurrentIcon, Expected: $iconName');
          return retrySuccess;
        } catch (retryError) {
          debugPrint('❌ Error during retry: $retryError');
          return false;
        }
      }

      return success;
    } catch (e) {
      debugPrint('❌ Error setting iOS app icon: $e');
      // Try to get more details about the error
      if (e is PlatformException) {
        debugPrint('❌ Platform exception details: Code: ${e.code}, Message: ${e.message}, Details: ${e.details}');
      }
      return false;
    }
  }

  /// Reset the app icon to the default
  static Future<bool> resetAppIcon() async {
    debugPrint('🔄 Resetting app icon to default');
    try {
      if (Platform.isIOS) {
        // Set to null to use the primary icon
        await FlutterDynamicIcon.setAlternateIconName(null, showAlert: false);
        debugPrint('✅ iOS icon reset to default');
        return true;
      } else if (Platform.isAndroid) {
        // Use regular icon as default
        return await CustomAppIconService.setAppIcon(false);
      }
      return false;
    } on PlatformException catch (e) {
      debugPrint('❌ Platform exception resetting app icon: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('❌ Error resetting app icon: $e');
      return false;
    }
  }

  /// Get the current app icon name (iOS only)
  static Future<String?> getCurrentIconName() async {
    try {
      if (Platform.isIOS) {
        return await FlutterDynamicIcon.getAlternateIconName();
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting current icon name: $e');
      return null;
    }
  }
}
