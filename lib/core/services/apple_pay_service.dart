import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';
import '../../features/subscription/domain/entities/subscription_plan.dart';
import '../../features/user/domain/usecases/user_service.dart';
import '../../l10n/generated/app_localizations.dart';
import '../utils/logger.dart';

/// Service for handling Apple Pay and Google Play in-app purchases
class ApplePayService {
  static final ApplePayService _instance = ApplePayService._internal();
  factory ApplePayService() => _instance;
  ApplePayService._internal();

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;

  UserService? _userService;

  /// Product IDs for the 4 subscription plans
  static const String trialProductId = 'com.quickposters.trials';
  static const String threeMonthProductId = 'com.quickposters.three_month';
  static const String sixMonthProductId = 'com.quickposters.six_month';
  static const String annualProductId = 'com.quickposters.annual';

  static const Set<String> _productIds = {
    trialProductId,
    threeMonthProductId,
    sixMonthProductId,
    annualProductId,
  };

  /// Available products from the store
  List<ProductDetails> _products = [];

  /// Whether the service is initialized
  bool _isInitialized = false;

  /// Initialize the Apple Pay service
  Future<bool> initialize(UserService userService) async {
    try {
      _userService = userService;

      // Check if in-app purchases are available
      final bool isAvailable = await _inAppPurchase.isAvailable();
      if (!isAvailable) {
        AppLogger.error('In-app purchases not available');
        return false;
      }

      // Enable pending purchases for iOS
      if (Platform.isIOS) {
        final InAppPurchaseStoreKitPlatformAddition iosAddition =
            _inAppPurchase.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
        await iosAddition.setDelegate(ExamplePaymentQueueDelegate());
      }

      // Listen to purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => AppLogger.info('Purchase stream closed'),
        onError: (error) => AppLogger.error('Purchase stream error', error),
      );

      // Load products
      await _loadProducts();

      _isInitialized = true;
      AppLogger.info('Apple Pay service initialized successfully');
      return true;
    } catch (e) {
      AppLogger.error('Failed to initialize Apple Pay service', e);
      return false;
    }
  }

  /// Load available products from the store
  Future<void> _loadProducts() async {
    try {
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds);

      if (response.notFoundIDs.isNotEmpty) {
        AppLogger.warning('Products not found: ${response.notFoundIDs}');
      }

      _products = response.productDetails;
      AppLogger.info('Loaded ${_products.length} products');

      for (final product in _products) {
        AppLogger.info('Product: ${product.id} - ${product.title} - ${product.price}');
      }
    } catch (e) {
      AppLogger.error('Failed to load products', e);
    }
  }

  /// Get product details by subscription plan
  ProductDetails? getProductForPlan(SubscriptionPlan plan) {
    final String productId = _getProductIdForPlan(plan);
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      AppLogger.warning('Product not found for plan: ${plan.id}');
      return null;
    }
  }

  /// Get product ID for a subscription plan
  String _getProductIdForPlan(SubscriptionPlan plan) {
    switch (plan.id) {
      case 'trial':
        return trialProductId;
      case 'three_month':
        return threeMonthProductId;
      case 'six_month':
        return sixMonthProductId;
      case 'annual':
        return annualProductId;
      default:
        throw ArgumentError('Unknown plan ID: ${plan.id}');
    }
  }

  /// Purchase a subscription plan
  Future<bool> purchaseSubscription(SubscriptionPlan plan, BuildContext context) async {
    if (!_isInitialized) {
      AppLogger.error('Apple Pay service not initialized');
      return false;
    }

    try {
      final ProductDetails? product = getProductForPlan(plan);
      if (product == null) {
        AppLogger.error('Product not found for plan: ${plan.id}');
        _showError(context, 'Product not available');
        return false;
      }

      AppLogger.info('Initiating purchase for: ${product.id}');

      // Create purchase param
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: product,
        applicationUserName: await _getUserId(),
      );

      // Start the purchase (for auto-renewable subscriptions)
      AppLogger.info('Starting subscription purchase for: ${product.id}');

      // For iOS subscriptions, we still use buyNonConsumable but the product type in App Store Connect determines behavior
      final bool success = await _inAppPurchase.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      if (!success) {
        AppLogger.error('Failed to initiate purchase');
        _showError(context, 'Failed to start purchase');
        return false;
      }

      return true;
    } catch (e) {
      AppLogger.error('Error purchasing subscription', e);
      _showError(context, 'Purchase failed: ${e.toString()}');
      return false;
    }
  }

  /// Handle purchase updates
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      await _handlePurchase(purchaseDetails);
    }
  }

  /// Handle individual purchase
  Future<void> _handlePurchase(PurchaseDetails purchaseDetails) async {
    try {
      AppLogger.info('Handling purchase: ${purchaseDetails.productID} - ${purchaseDetails.status}');

      if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {

        // Verify the purchase (in production, do this server-side)
        final bool isValid = await _verifyPurchase(purchaseDetails);

        if (isValid) {
          // Grant premium access
          await _grantPremiumAccess(purchaseDetails);
          AppLogger.info('Premium access granted for: ${purchaseDetails.productID}');
        } else {
          AppLogger.error('Purchase verification failed');
        }
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        AppLogger.error('Purchase error: ${purchaseDetails.error}');
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        AppLogger.info('Purchase canceled by user');
      }

      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
      }
    } catch (e) {
      AppLogger.error('Error handling purchase', e);
    }
  }

  /// Verify purchase (simplified - use server-side verification in production)
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    try {
      // In production, send the receipt to your server for verification
      // For now, we'll do basic validation
      return purchaseDetails.verificationData.localVerificationData.isNotEmpty &&
             purchaseDetails.verificationData.serverVerificationData.isNotEmpty;
    } catch (e) {
      AppLogger.error('Purchase verification error', e);
      return false;
    }
  }

  /// Grant premium access to user
  Future<void> _grantPremiumAccess(PurchaseDetails purchaseDetails) async {
    try {
      if (_userService != null) {
        await _userService!.updatePremiumStatus(true);
        AppLogger.info('Premium status updated successfully');
      }
    } catch (e) {
      AppLogger.error('Failed to grant premium access', e);
    }
  }

  /// Get user ID for purchase attribution
  Future<String?> _getUserId() async {
    try {
      if (_userService != null) {
        final user = await _userService!.getCurrentUser();
        return user?.uid;
      }
      return null;
    } catch (e) {
      AppLogger.error('Failed to get user ID', e);
      return null;
    }
  }

  /// Restore previous purchases
  Future<bool> restorePurchases(BuildContext context) async {
    if (!_isInitialized) {
      AppLogger.error('Apple Pay service not initialized');
      return false;
    }

    try {
      AppLogger.info('Restoring purchases...');
      await _inAppPurchase.restorePurchases();
      return true;
    } catch (e) {
      AppLogger.error('Failed to restore purchases', e);
      _showError(context, AppLocalizations.of(context)!.failedToRestorePurchases);
      return false;
    }
  }

  /// Show error message
  void _showError(BuildContext context, String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Get all available products
  List<ProductDetails> get products => _products;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Dispose the service
  void dispose() {
    _subscription.cancel();
  }
}

/// Payment queue delegate for iOS
class ExamplePaymentQueueDelegate implements SKPaymentQueueDelegateWrapper {
  @override
  bool shouldContinueTransaction(
      SKPaymentTransactionWrapper transaction, SKStorefrontWrapper storefront) {
    return true;
  }

  @override
  bool shouldShowPriceConsent() {
    return false;
  }
}
