import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'local_storage_service.dart';

/// Custom implementation of app icon changing functionality
/// This is used as a replacement for the android_dynamic_icon package
class CustomAppIconService {
  static const MethodChannel _channel = MethodChannel('com.heavystacksol.quickposters/app_icon');

  /// Initialize the app icon service
  static Future<void> initialize() async {
    if (Platform.isAndroid) {
      try {
        // Get the premium status from local storage
        final isPremium = LocalStorageService.getPremiumStatus();

        // Set the initial app icon based on premium status
        await setAppIcon(isPremium);

        debugPrint('Custom app icon service initialized with premium status: $isPremium');
      } catch (e) {
        debugPrint('Failed to initialize custom app icon service: $e');
      }
    }
  }

  /// Set the app icon based on premium status
  static Future<bool> setAppIcon(bool isPremium) async {
    if (!Platform.isAndroid) {
      debugPrint('Custom app icon service only supports Android');
      return false;
    }

    try {
      final String iconType = isPremium ? 'premium' : 'regular';
      debugPrint('Setting app icon to $iconType');

      final bool result = await _channel.invokeMethod('changeIcon', {
        'iconType': iconType,
      });

      debugPrint('App icon change result: $result');
      return result;
    } on PlatformException catch (e) {
      debugPrint('Platform exception changing app icon: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('Error changing app icon: $e');
      return false;
    }
  }
}
