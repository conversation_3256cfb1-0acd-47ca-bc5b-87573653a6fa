import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../utils/logger.dart';

/// Service for handling language-related operations
class LanguageService {
  static const String _languageKey = 'selected_language';
  static const String _defaultLanguage = 'en';
  
  /// Supported languages with their display names
  static const Map<String, String> supportedLanguages = {
    'en': 'English',
    'hi': 'हिंदी',
    'mr': 'मराठी',
    'bn': 'বাংলা',
    'gu': 'ગુજરાતી',
    'ta': 'தமிழ்',
    'te': 'తెలుగు',
  };

  /// Language codes mapped to their English names for internal use
  static const Map<String, String> languageNames = {
    'en': 'English',
    'hi': 'Hindi',
    'mr': 'Marathi',
    'bn': 'Bengali',
    'gu': 'Gujarati',
    'ta': 'Tamil',
    'te': 'Telugu',
  };

  /// Get the saved language from local storage
  /// Returns the default language if none is saved
  static Future<String> getSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey) ?? _defaultLanguage;
      
      // Validate that the saved language is supported
      if (supportedLanguages.containsKey(savedLanguage)) {
        AppLogger.info('Retrieved saved language: $savedLanguage');
        return savedLanguage;
      } else {
        AppLogger.warning('Saved language $savedLanguage is not supported, using default');
        return _defaultLanguage;
      }
    } catch (e) {
      AppLogger.error('Error getting saved language', e);
      return _defaultLanguage;
    }
  }

  /// Save language preference to local storage
  static Future<void> saveLanguage(String languageCode) async {
    try {
      if (!supportedLanguages.containsKey(languageCode)) {
        throw ArgumentError('Unsupported language code: $languageCode');
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
      AppLogger.info('Language saved to local storage: $languageCode');
    } catch (e) {
      AppLogger.error('Error saving language to local storage', e);
      rethrow;
    }
  }

  /// Update user's language preference in Firestore
  static Future<void> updateUserLanguage(String languageCode) async {
    try {
      if (!supportedLanguages.containsKey(languageCode)) {
        throw ArgumentError('Unsupported language code: $languageCode');
      }

      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .update({
          'language': languageCode,
          'languageName': languageNames[languageCode],
          'lastLanguageUpdate': FieldValue.serverTimestamp(),
        });
        AppLogger.info('User language updated in Firestore: $languageCode for user ${user.uid}');
      } else {
        AppLogger.warning('Cannot update user language - no authenticated user');
      }
    } catch (e) {
      AppLogger.error('Error updating user language in Firestore', e);
      rethrow;
    }
  }

  /// Get user's language from Firestore
  static Future<String> getUserLanguageFromFirestore() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        AppLogger.warning('Cannot get user language - no authenticated user');
        return _defaultLanguage;
      }

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        final language = userData['language'] as String? ?? _defaultLanguage;
        
        // Validate that the language is supported
        if (supportedLanguages.containsKey(language)) {
          AppLogger.info('Retrieved user language from Firestore: $language');
          return language;
        } else {
          AppLogger.warning('User language $language is not supported, using default');
          return _defaultLanguage;
        }
      } else {
        AppLogger.warning('User document not found in Firestore');
        return _defaultLanguage;
      }
    } catch (e) {
      AppLogger.error('Error getting user language from Firestore', e);
      return _defaultLanguage;
    }
  }

  /// Initialize language service - loads language from Firestore if user is logged in,
  /// otherwise uses local storage
  static Future<String> initializeLanguage() async {
    try {
      AppLogger.info('Initializing language service...');
      
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // User is logged in, try to get language from Firestore
        final firestoreLanguage = await getUserLanguageFromFirestore();
        
        // Save to local storage for faster access
        await saveLanguage(firestoreLanguage);
        
        AppLogger.info('Language initialized from Firestore: $firestoreLanguage');
        return firestoreLanguage;
      } else {
        // User not logged in, use local storage
        final localLanguage = await getSavedLanguage();
        AppLogger.info('Language initialized from local storage: $localLanguage');
        return localLanguage;
      }
    } catch (e) {
      AppLogger.error('Error initializing language service', e);
      return _defaultLanguage;
    }
  }

  /// Sync language between local storage and Firestore
  /// This is useful when user logs in and we want to sync their preferences
  static Future<void> syncLanguagePreferences() async {
    try {
      AppLogger.info('Syncing language preferences...');
      
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        AppLogger.warning('Cannot sync language preferences - no authenticated user');
        return;
      }

      final localLanguage = await getSavedLanguage();
      final firestoreLanguage = await getUserLanguageFromFirestore();

      if (localLanguage != firestoreLanguage) {
        // Prefer Firestore language if it exists and is different
        if (firestoreLanguage != _defaultLanguage) {
          await saveLanguage(firestoreLanguage);
          AppLogger.info('Synced language from Firestore to local: $firestoreLanguage');
        } else {
          // Firestore doesn't have a language set, use local preference
          await updateUserLanguage(localLanguage);
          AppLogger.info('Synced language from local to Firestore: $localLanguage');
        }
      } else {
        AppLogger.info('Language preferences already in sync: $localLanguage');
      }
    } catch (e) {
      AppLogger.error('Error syncing language preferences', e);
    }
  }

  /// Get the locale object for a language code
  static Locale getLocaleForLanguage(String languageCode) {
    if (supportedLanguages.containsKey(languageCode)) {
      return Locale(languageCode);
    } else {
      AppLogger.warning('Unsupported language code: $languageCode, using default');
      return Locale(_defaultLanguage);
    }
  }

  /// Get the display name for a language code
  static String getDisplayName(String languageCode) {
    return supportedLanguages[languageCode] ?? supportedLanguages[_defaultLanguage]!;
  }

  /// Get the English name for a language code
  static String getEnglishName(String languageCode) {
    return languageNames[languageCode] ?? languageNames[_defaultLanguage]!;
  }

  /// Check if a language code is supported
  static bool isLanguageSupported(String languageCode) {
    return supportedLanguages.containsKey(languageCode);
  }

  /// Get all supported language codes
  static List<String> getSupportedLanguageCodes() {
    return supportedLanguages.keys.toList();
  }

  /// Get all supported languages as a list of maps with code and display name
  static List<Map<String, String>> getSupportedLanguagesList() {
    return supportedLanguages.entries
        .map((entry) => {
              'code': entry.key,
              'displayName': entry.value,
              'englishName': languageNames[entry.key]!,
            })
        .toList();
  }

  /// Clear saved language (useful for logout)
  static Future<void> clearSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_languageKey);
      AppLogger.info('Cleared saved language from local storage');
    } catch (e) {
      AppLogger.error('Error clearing saved language', e);
    }
  }
}
