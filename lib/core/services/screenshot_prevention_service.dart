import 'dart:async';
import 'package:flutter/material.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:no_screenshot/screenshot_snapshot.dart';
import '../utils/logger.dart';

/// Service to manage screenshot prevention throughout the app
class ScreenshotPreventionService {
  static final ScreenshotPreventionService _instance = ScreenshotPreventionService._internal();

  /// Singleton instance
  factory ScreenshotPreventionService() => _instance;

  ScreenshotPreventionService._internal();

  final NoScreenshot _noScreenshot = NoScreenshot.instance;
  bool _isInitialized = false;
  bool _isScreenshotPreventionEnabled = false;

  /// Stream of screenshot events
  Stream<ScreenshotSnapshot> get screenshotStream => _noScreenshot.screenshotStream;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Start with screenshot prevention disabled
      await enableScreenshotPrevention();

      // Listen for screenshot events
      await _noScreenshot.startScreenshotListening();

      _noScreenshot.screenshotStream.listen((event) {
        if (event.wasScreenshotTaken) {
          AppLogger.warning('Screenshot detected: ${event.screenshotPath}');
        }
      });

      _isInitialized = true;
      AppLogger.info('Screenshot prevention service initialized');
    } catch (e) {
      AppLogger.error('Failed to initialize screenshot prevention service', e);
    }
  }

  /// Enable screenshot prevention
  Future<bool> enableScreenshotPrevention() async {
    try {
      final result = await _noScreenshot.screenshotOff();
      _isScreenshotPreventionEnabled = result;
      AppLogger.info('Screenshot prevention enabled: $result');
      return result;
    } catch (e) {
      AppLogger.error('Failed to enable screenshot prevention', e);
      return false;
    }
  }

  /// Disable screenshot prevention
  Future<bool> disableScreenshotPrevention() async {
    try {
      final result = await _noScreenshot.screenshotOn();
      _isScreenshotPreventionEnabled = !result;
      AppLogger.info('Screenshot prevention disabled: $result');
      return result;
    } catch (e) {
      AppLogger.error('Failed to disable screenshot prevention', e);
      return false;
    }
  }

  /// Toggle screenshot prevention
  Future<bool> toggleScreenshotPrevention() async {
    try {
      final result = await _noScreenshot.toggleScreenshot();
      _isScreenshotPreventionEnabled = !_isScreenshotPreventionEnabled;
      AppLogger.info('Screenshot prevention toggled: $result');
      return result;
    } catch (e) {
      AppLogger.error('Failed to toggle screenshot prevention', e);
      return false;
    }
  }

  /// Check if screenshot prevention is enabled
  bool get isScreenshotPreventionEnabled => _isScreenshotPreventionEnabled;

  /// Show a warning dialog when a screenshot is detected
  void showScreenshotWarningDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.warning_amber_rounded, color: Colors.red, size: 28),
            const SizedBox(width: 8),
            const Text('Screenshot Detected'),
          ],
        ),
        content: const Text(
          'Taking screenshots of this app is not allowed for security and privacy reasons.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
