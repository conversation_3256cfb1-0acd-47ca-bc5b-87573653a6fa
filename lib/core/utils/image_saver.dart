import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';

class ImageSaver {
  /// Save image to the device gallery
  /// Returns true if successful, false otherwise
  static Future<bool> saveImageToGallery(Uint8List imageBytes) async {
    try {
      // Request necessary permissions
      if (!await _requestPermissions()) {
        return false;
      }

      // Create a temporary file
      final tempDir = await getTemporaryDirectory();
      final fileName = 'quickposter_${DateTime.now().millisecondsSinceEpoch}.png';
      final tempFile = File('${tempDir.path}/$fileName');
      
      // Write image bytes to the file
      await tempFile.writeAsBytes(imageBytes);
      
      // Use platform-specific method to save to gallery
      final result = await _saveToPlatformGallery(tempFile.path);
      
      // Clean up the temporary file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }
      
      return result;
    } catch (e) {
      debugPrint('Error saving image to gallery: $e');
      return false;
    }
  }
  
  /// Request necessary permissions based on platform
  static Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      // For Android 13+ (API level 33+)
      if (await Permission.photos.request().isGranted) {
        return true;
      }
      
      // For older Android versions
      final status = await Permission.storage.request();
      return status.isGranted;
    } else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }
    
    return false;
  }
  
  /// Save image to gallery using platform-specific method
  static Future<bool> _saveToPlatformGallery(String filePath) async {
    try {
      // Use platform channel to call native code
      const platform = MethodChannel('com.quickposters/image_saver');
      final result = await platform.invokeMethod('saveImageToGallery', {
        'filePath': filePath,
      });
      
      return result == true;
    } on PlatformException catch (e) {
      debugPrint('Platform exception: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('Error in _saveToPlatformGallery: $e');
      return false;
    }
  }
}
