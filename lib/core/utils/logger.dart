import 'dart:convert';

/// Simple logger utility for the application
class AppLogger {
  /// Log an info message
  static void info(String message, [Object? data]) {
    // In a production app, you would use a proper logging framework
    // like logger or firebase_crashlytics
    print('[INFO] $message');
    if (data != null) {
      try {
        // Try to pretty-print JSON data
        final String prettyData = _prettyPrintObject(data);
        print('[INFO DATA] $prettyData');
      } catch (e) {
        print('[INFO DATA] $data');
      }
    }
  }

  /// Log an error message
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    // In a production app, you would use a proper logging framework
    // like logger or firebase_crashlytics
    print('[ERROR] $message');
    if (error != null) {
      try {
        // Try to pretty-print JSON data
        final String prettyError = _prettyPrintObject(error);
        print('Error details: $prettyError');
      } catch (e) {
        print('Error details: $error');
      }
    }
    if (stackTrace != null) {
      print('Stack trace: $stackTrace');
    }
  }

  /// Log a warning message
  static void warning(String message, [Object? data]) {
    // In a production app, you would use a proper logging framework
    // like logger or firebase_crashlytics
    print('[WARNING] $message');
    if (data != null) {
      try {
        // Try to pretty-print JSON data
        final String prettyData = _prettyPrintObject(data);
        print('[WARNING DATA] $prettyData');
      } catch (e) {
        print('[WARNING DATA] $data');
      }
    }
  }

  /// Log a debug message (only in debug mode)
  static void debug(String message, [Object? data]) {
    // In a production app, you would conditionally log based on build mode
    // and use a proper logging framework
    print('[DEBUG] $message');
    if (data != null) {
      try {
        // Try to pretty-print JSON data
        final String prettyData = _prettyPrintObject(data);
        print('[DEBUG DATA] $prettyData');
      } catch (e) {
        print('[DEBUG DATA] $data');
      }
    }
  }

  /// Helper method to pretty-print objects
  static String _prettyPrintObject(Object? object) {
    if (object == null) return 'null';

    if (object is Map) {
      try {
        return const JsonEncoder.withIndent('  ').convert(object);
      } catch (e) {
        // If it can't be encoded as JSON, fall back to toString
        return object.toString();
      }
    } else if (object is List) {
      try {
        return const JsonEncoder.withIndent('  ').convert(object);
      } catch (e) {
        // If it can't be encoded as JSON, fall back to toString
        return object.toString();
      }
    } else {
      return object.toString();
    }
  }
}
