import 'dart:ui';
import 'package:flutter/material.dart';

/// A custom shader warm-up implementation to help with shader compilation issues
class CustomShaderWarmUp extends ShaderWarmUp {
  // The default width and height for the warm-up canvas
  final double width;
  final double height;

  /// Constructor
  const CustomShaderWarmUp({
    this.width = 1000.0,
    this.height = 1000.0,
  });

  @override
  Future<void> warmUpOnCanvas(Canvas canvas) async {
    // Warm up the most common shaders used in the app
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 4.0
      ..color = Colors.blue;

    // Draw a rectangle
    canvas.drawRect(
      Rect.fromLTRB(0, 0, width, height),
      paint,
    );

    // Draw a rounded rectangle
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTRB(0, 0, width / 2, height / 2),
        const Radius.circular(16.0),
      ),
      paint,
    );

    // Draw a circle
    canvas.drawCircle(
      Offset(width / 2, height / 2),
      width / 4,
      paint,
    );

    // Draw a path
    final path = Path()
      ..moveTo(0, 0)
      ..lineTo(width, 0)
      ..lineTo(width, height)
      ..lineTo(0, height)
      ..close();
    canvas.drawPath(path, paint);

    // Draw a shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withAlpha(100)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10.0);
    canvas.drawRect(
      Rect.fromLTRB(0, 0, width / 2, height / 2),
      shadowPaint,
    );

    // Draw a gradient
    final gradient = LinearGradient(
      colors: [Colors.red, Colors.blue],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
    final gradientPaint = Paint()
      ..shader = gradient.createShader(
        Rect.fromLTRB(0, 0, width, height),
      );
    canvas.drawRect(
      Rect.fromLTRB(0, 0, width, height),
      gradientPaint,
    );

    // Draw text
    const text = 'Hello, Flutter!';
    final textStyle = TextStyle(
      color: Colors.black,
      fontSize: 24.0,
      fontWeight: FontWeight.bold,
    );
    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(
      minWidth: 0,
      maxWidth: width,
    );
    textPainter.paint(
      canvas,
      Offset(width / 2 - textPainter.width / 2, height / 2 - textPainter.height / 2),
    );

    // Draw a blur
    final blurPaint = Paint()
      ..imageFilter = ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0);
    canvas.saveLayer(
      Rect.fromLTRB(0, 0, width, height),
      blurPaint,
    );
    canvas.restore();
  }
}
