import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../core/providers/theme_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/screenshot_prevention_wrapper.dart';
import '../../domain/entities/generated_image.dart';
import '../../domain/usecases/image_generation_service.dart';

/// Screen for generating AI images
class AIImageGenerationPage extends StatefulWidget {
  const AIImageGenerationPage({super.key});

  @override
  State<AIImageGenerationPage> createState() => _AIImageGenerationPageState();
}

class _AIImageGenerationPageState extends State<AIImageGenerationPage> {
  final TextEditingController _promptController = TextEditingController();

  String _selectedSize = '1024x1024';
  bool _isGenerating = false;
  GeneratedImage? _generatedImage;
  String? _errorMessage;

  final List<String> _availableSizes = [
    '1024x1024',
    '1024x1792',
    '1792x1024',
  ];

  @override
  void dispose() {
    _promptController.dispose();
    super.dispose();
  }

  /// Generate an image using the provided prompt
  Future<void> _generateImage() async {
    final prompt = _promptController.text.trim();

    if (prompt.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a prompt';
      });
      return;
    }

    setState(() {
      _isGenerating = true;
      _errorMessage = null;
    });

    try {
      final imageGenerationService = Provider.of<ImageGenerationService>(context, listen: false);
      final generatedImage = await imageGenerationService.generateImage(
        prompt: prompt,
        size: _selectedSize,
      );

      setState(() {
        _generatedImage = generatedImage;
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to generate image: ${e.toString()}';
        _isGenerating = false;
      });
    }
  }

  /// Build the appropriate widget for displaying the image based on the URL format
  Widget _buildImageWidget(String imageUrl) {
    if (imageUrl.startsWith('data:image')) {
      // This is a base64 encoded image
      // Use Uri.parse to handle data URLs
      return Image.memory(
        Uri.parse(imageUrl).data!.contentAsBytes(),
        fit: BoxFit.cover,
        width: double.infinity,
        errorBuilder: (context, error, stackTrace) => const Icon(
          Icons.error,
          color: Colors.red,
          size: 48,
        ),
      );
    } else {
      // This is a regular URL
      return CachedNetworkImage(
        imageUrl: imageUrl,
        placeholder: (context, url) => const Center(
          child: CircularProgressIndicator(),
        ),
        errorWidget: (context, url, error) => const Icon(
          Icons.error,
          color: Colors.red,
          size: 48,
        ),
        fit: BoxFit.cover,
        width: double.infinity,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return ScreenshotPreventionWrapper(
      child: Scaffold(
        appBar: themeProvider.gradientAppBar(
          title: 'AI Image Generator',
        ),
        body: Container(
          decoration: isPremium
              ? BoxDecoration(
                  gradient: AppTheme.premiumGoldBlackGradient,
                )
              : BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppTheme.backgroundWhite,
                      AppTheme.lightGradientBg,
                    ],
                  ),
                ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [

                // Image Generation Section
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Generate Image',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _promptController,
                          decoration: InputDecoration(
                            hintText: 'Enter your prompt (e.g., "A city skyline at sunset")',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            filled: true,
                            fillColor: Colors.white.withAlpha(230),
                          ),
                          maxLines: 3,
                        ),
                        const SizedBox(height: 16),

                        // Image size selection
                        Text(
                          'Image Size',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          value: _selectedSize,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            filled: true,
                            fillColor: Colors.white.withAlpha(230),
                          ),
                          items: _availableSizes.map((size) {
                            return DropdownMenuItem<String>(
                              value: size,
                              child: Text(size),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedSize = value;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),

                        // Generate button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _isGenerating ? null : _generateImage,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: _isGenerating
                                ? const SizedBox(
                                    height: 24,
                                    width: 24,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : const Text(
                                    'Generate Image',
                                    style: TextStyle(fontSize: 16),
                                  ),
                          ),
                        ),

                        // Error message
                        if (_errorMessage != null) ...[
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.red.withAlpha(25),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.red),
                            ),
                            child: Text(
                              _errorMessage!,
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Generated Image Section
                if (_generatedImage != null) ...[
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Generated Image',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Prompt: ${_generatedImage!.prompt}',
                            style: const TextStyle(
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _buildImageWidget(_generatedImage!.imageUrl),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
