import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';

import '../../../../core/utils/logger.dart';
import '../../domain/entities/generated_image.dart';
import '../../domain/repositories/image_generation_repository.dart';

/// Implementation of the ImageGenerationRepository using the nscale.com API
class NScaleImageGenerationRepository implements ImageGenerationRepository {
  /// The base URL for the nscale.com API
  final String baseUrl;

  /// The API key for the nscale.com API
  final String apiKey;

  /// HTTP client for making API requests
  final http.Client _client;

  /// Constructor
  NScaleImageGenerationRepository({
    this.baseUrl = 'https://inference.api.nscale.com/v1/images/generations',
    String? apiKey,
    http.Client? client,
  }) :
    // Use the provided API key or get it from .env file
    this.apiKey = apiKey ?? dotenv.env['NSCALE_API_KEY'] ?? '',
    _client = client ?? http.Client();

  @override
  Future<GeneratedImage> generateImage({
    required String prompt,
    String model = 'black-forest-labs/FLUX.1-schnell',
    String size = '1024x1024',
    int n = 1,
  }) async {
    try {
      AppLogger.info('Generating image with prompt: $prompt');

      // Prepare request body
      final Map<String, dynamic> requestBody = {
        'model': model,
        'prompt': prompt,
        'size': size,
        'n': n,
      };

      // Make API request
      final response = await _client.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode(requestBody),
      );

      // Check response status
      if (response.statusCode == 200) {
        // Log the full response for debugging
        AppLogger.info('Response body: ${response.body}');

        // Parse response
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        // Log the parsed response data
        AppLogger.info('Parsed response data: $responseData');

        // Check if the response has the expected structure
        if (responseData.containsKey('data') && responseData['data'] is List) {
          final List<dynamic> data = responseData['data'];

          if (data.isNotEmpty) {
            // Log the first item in the data array
            AppLogger.info('First data item: ${data[0]}');

            // Check if the data item has a URL field
            if (data[0] is Map && data[0].containsKey('url') && data[0]['url'] != null) {
              final String imageUrl = data[0]['url'];

              // Create and return GeneratedImage entity
              return GeneratedImage(
                imageUrl: imageUrl,
                prompt: prompt,
                generatedAt: DateTime.now(),
                model: model,
                size: size,
              );
            } else if (data[0] is Map && data[0].containsKey('b64_json') && data[0]['b64_json'] != null) {
              // Some APIs return base64 encoded image instead of URL
              final String base64Image = data[0]['b64_json'];
              final String imageUrl = 'data:image/png;base64,$base64Image';

              return GeneratedImage(
                imageUrl: imageUrl,
                prompt: prompt,
                generatedAt: DateTime.now(),
                model: model,
                size: size,
              );
            } else {
              // Log the structure of the data item
              AppLogger.error('Data item does not contain url or b64_json field', data[0]);
              throw Exception('Image URL not found in response');
            }
          } else {
            throw Exception('No image data in response');
          }
        } else {
          // The response structure is different than expected
          // Try to find any URL-like field in the response
          String? imageUrl;

          // Try to find a URL in the response data
          responseData.forEach((key, value) {
            if (value is String && (value.startsWith('http') || value.startsWith('data:'))) {
              imageUrl = value;
            } else if (value is Map) {
              value.forEach((subKey, subValue) {
                if (subValue is String && (subValue.startsWith('http') || subValue.startsWith('data:'))) {
                  imageUrl = subValue;
                }
              });
            }
          });

          if (imageUrl != null) {
            return GeneratedImage(
              imageUrl: imageUrl!,  // This is safe now because we've checked it's not null
              prompt: prompt,
              generatedAt: DateTime.now(),
              model: model,
              size: size,
            );
          }

          AppLogger.error('Unexpected response structure', responseData);
          throw Exception('Unexpected response structure: ${response.body}');
        }
      } else {
        // Handle error response
        AppLogger.error(
          'Failed to generate image. Status: ${response.statusCode}',
          response.body,
        );
        throw Exception('Failed to generate image: ${response.body}');
      }
    } catch (e) {
      AppLogger.error('Error generating image', e);
      rethrow;
    }
  }
}
