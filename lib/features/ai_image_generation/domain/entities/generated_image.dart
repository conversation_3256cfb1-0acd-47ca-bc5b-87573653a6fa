import 'package:equatable/equatable.dart';

/// Entity representing an AI-generated image
class GeneratedImage extends Equatable {
  /// The URL of the generated image
  final String imageUrl;
  
  /// The prompt used to generate the image
  final String prompt;
  
  /// The timestamp when the image was generated
  final DateTime generatedAt;
  
  /// The model used to generate the image
  final String model;
  
  /// The size of the generated image
  final String size;

  /// Constructor
  const GeneratedImage({
    required this.imageUrl,
    required this.prompt,
    required this.generatedAt,
    required this.model,
    required this.size,
  });

  @override
  List<Object?> get props => [imageUrl, prompt, generatedAt, model, size];
}
