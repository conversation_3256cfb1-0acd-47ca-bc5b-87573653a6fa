import '../entities/generated_image.dart';

/// Repository interface for AI image generation
abstract class ImageGenerationRepository {
  /// Generate an image using the provided prompt and options
  /// 
  /// [prompt] - The text prompt to generate the image
  /// [model] - The AI model to use (default: 'black-forest-labs/FLUX.1-schnell')
  /// [size] - The size of the generated image (default: '1024x1024')
  /// [n] - The number of images to generate (default: 1)
  /// 
  /// Returns a Future that resolves to a GeneratedImage object
  Future<GeneratedImage> generateImage({
    required String prompt,
    String model = 'black-forest-labs/FLUX.1-schnell',
    String size = '1024x1024',
    int n = 1,
  });
}
