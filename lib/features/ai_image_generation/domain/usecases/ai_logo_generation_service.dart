import '../../../../core/utils/logger.dart';
import '../../domain/entities/generated_image.dart';
import '../../domain/repositories/image_generation_repository.dart';

/// Service for generating AI logos for business profiles
class AILogoGenerationService {
  /// Repository for image generation
  final ImageGenerationRepository _repository;

  /// Constructor
  AILogoGenerationService(this._repository);

  /// Generate a logo for a business using AI
  ///
  /// [businessName] - The name of the business
  /// [businessCategory] - The category or type of business
  /// [businessLocation] - The location of the business
  /// [additionalDetails] - Any additional details to include in the prompt
  ///
  /// Returns a Future that resolves to a GeneratedImage object
  Future<GeneratedImage> generateBusinessLogo({
    required String businessName,
    String? businessCategory,
    String? businessLocation,
    String? additionalDetails,
  }) async {
    // Create a detailed prompt for the logo generation
    final prompt = _createLogoPrompt(
      businessName: businessName,
      businessCategory: businessCategory,
      businessLocation: businessLocation,
      additionalDetails: additionalDetails,
    );

    AppLogger.info('Generating logo with prompt: $prompt');

    // Generate the image using the repository
    final generatedImage = await _repository.generateImage(
      prompt: prompt,
      // Use a square size for logos (smaller size for faster generation and lower resource usage)
      size: '1024x1024',
    );

    return generatedImage;
  }

  /// Create a detailed prompt for logo generation
  String _createLogoPrompt({
    required String businessName,
    String? businessCategory,
    String? businessLocation,
    String? additionalDetails,
  }) {
    // Start with a base prompt that specifies we want a logo
    String prompt = 'Create a professional, modern logo for a business named "$businessName"';

    // Add business category if available
    if (businessCategory != null && businessCategory.isNotEmpty) {
      prompt += ' in the ${businessCategory.toLowerCase()} industry';
    }

    // Add location if available
    if (businessLocation != null && businessLocation.isNotEmpty) {
      prompt += ' located in $businessLocation';
    }

    // Add additional details if available
    if (additionalDetails != null && additionalDetails.isNotEmpty) {
      prompt += '. $additionalDetails';
    }

    // Add specific instructions for the logo
    prompt += '. The logo should be clean, memorable, and work well at different sizes. ' +
              'The design should be simple yet distinctive, using colors that represent the ' +
              'business type. Include a subtle, blurred background image related to the business ' +
              'type that enhances the logo visibility. The logo should be prominently displayed ' +
              'in the foreground with high contrast against the blurred background. ' +
              'Do not include any text or border in the image.';

    return prompt;
  }
}
