import '../entities/generated_image.dart';
import '../repositories/image_generation_repository.dart';

/// Service for generating AI images
class ImageGenerationService {
  /// Repository for image generation
  final ImageGenerationRepository _repository;
  
  /// List of recently generated images
  final List<GeneratedImage> _recentImages = [];
  
  /// Constructor
  ImageGenerationService(this._repository);
  
  /// Generate an image using the provided prompt and options
  /// 
  /// [prompt] - The text prompt to generate the image
  /// [model] - The AI model to use (default: 'black-forest-labs/FLUX.1-schnell')
  /// [size] - The size of the generated image (default: '1024x1024')
  /// [n] - The number of images to generate (default: 1)
  /// 
  /// Returns a Future that resolves to a GeneratedImage object
  Future<GeneratedImage> generateImage({
    required String prompt,
    String model = 'black-forest-labs/FLUX.1-schnell',
    String size = '1024x1024',
    int n = 1,
  }) async {
    final generatedImage = await _repository.generateImage(
      prompt: prompt,
      model: model,
      size: size,
      n: n,
    );
    
    // Add to recent images
    _recentImages.add(generatedImage);
    
    // Keep only the last 10 images
    if (_recentImages.length > 10) {
      _recentImages.removeAt(0);
    }
    
    return generatedImage;
  }
  
  /// Get the list of recently generated images
  List<GeneratedImage> get recentImages => List.unmodifiable(_recentImages);
}
