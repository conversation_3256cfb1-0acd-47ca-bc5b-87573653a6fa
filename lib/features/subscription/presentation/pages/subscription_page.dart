import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../../../settings/presentation/pages/web_view_page.dart';
import '../../domain/entities/subscription_plan.dart';
import '../../domain/usecases/subscription_service.dart';

/// Page for displaying and selecting subscription plans
class SubscriptionPage extends StatefulWidget {
  const SubscriptionPage({super.key});

  @override
  State<SubscriptionPage> createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _subscriptionInfo;
  bool _hasActiveSubscription = false;
  final PageController _pageController = PageController();
  int _currentPlanIndex = 0;
  SubscriptionPlan? _selectedPlan;

  @override
  void initState() {
    super.initState();
    // Initialize service and load subscription info
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeService();
      _loadSubscriptionInfo();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Initialize the subscription service
  Future<void> _initializeService() async {
    try {
      final subscriptionService = context.read<SubscriptionService>();
      await subscriptionService.initialize();
    } catch (e) {
      // Handle initialization error silently
    }
  }

  /// Load current user's subscription information
  Future<void> _loadSubscriptionInfo() async {
    try {
      final subscriptionService = context.read<SubscriptionService>();
      final subscriptionInfo = await subscriptionService.getCurrentUserSubscriptionInfo();
      final hasActive = await subscriptionService.hasActiveSubscription();
      final plans = subscriptionService.getAvailablePlans();

      if (mounted) {
        setState(() {
          _subscriptionInfo = subscriptionInfo;
          _hasActiveSubscription = hasActive;
          // Set the recommended plan as default selected
          _selectedPlan = plans.firstWhere((plan) => plan.isRecommended, orElse: () => plans.first);
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;
    final plans = SubscriptionPlan.getLocalizedPlans(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _hasActiveSubscription ? AppLocalizations.of(context)!.mySubscription : AppLocalizations.of(context)!.premiumSubscription,
          style: TextStyle(
            color: isPremium ? AppTheme.premiumGold : null,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: isPremium ? AppTheme.premiumBlack : null,
        iconTheme: IconThemeData(
          color: isPremium ? AppTheme.premiumGold : null,
        ),
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: isPremium
              ? AppTheme.premiumDarkGradient
              : AppTheme.lightGradient,
        ),
        child: _hasActiveSubscription
            ? _buildActiveSubscriptionView(isPremium, plans)
            : _buildNewSubscriptionView(isPremium, plans),
      ),
    );
  }

  /// Build view for users with active subscription
  Widget _buildActiveSubscriptionView(bool isPremium, List<SubscriptionPlan> plans) {
    final subscriptionInfo = _subscriptionInfo;
    if (subscriptionInfo == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final statusText = subscriptionInfo['statusText'] as String? ?? '';
    final isExpiringSoon = subscriptionInfo['isExpiringSoon'] as bool? ?? false;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Current Subscription Status Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: isPremium
                  ? AppTheme.premiumDarkGradient
                  : AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(20),
              boxShadow: isPremium ? AppTheme.premiumBlackShadow : AppTheme.mediumShadow,
            ),
            child: Column(
              children: [
                Icon(
                  Icons.verified_user,
                  size: 60,
                  color: isPremium ? AppTheme.premiumGold : Colors.white,
                ),
                const SizedBox(height: 16),
                Text(
                  'Premium Active',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: isPremium ? AppTheme.premiumGold : Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  statusText,
                  style: TextStyle(
                    fontSize: 16,
                    color: isPremium ? Colors.white70 : Colors.white.withValues(alpha: 0.9),
                  ),
                  textAlign: TextAlign.center,
                ),
                if (isExpiringSoon) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.orange),
                    ),
                    child: Text(
                      'Expiring Soon!',
                      style: TextStyle(
                        color: Colors.orange[300],
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Extend Subscription Section
          Text(
            'Extend Your Subscription',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add more time to your current subscription',
            style: TextStyle(
              fontSize: 16,
              color: isPremium ? Colors.white70 : Colors.grey[700],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Extension Plans Carousel
          SizedBox(
            height: 140,
            child: PageView.builder(
              itemCount: plans.length,
              itemBuilder: (context, index) {
                final plan = plans[index];
                return _buildExtensionPlanCard(plan, isPremium);
              },
            ),
          ),

          const SizedBox(height: 24),

          // Manage Subscription Info
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isPremium
                  ? Colors.white.withAlpha(13)
                  : Colors.grey.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isPremium
                    ? AppTheme.premiumGold.withAlpha(77)
                    : Colors.grey.withAlpha(77),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.settings,
                      size: 16,
                      color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Manage your subscription anytime in iOS Settings > Apple ID > Subscriptions',
                        style: TextStyle(
                          fontSize: 12,
                          color: isPremium ? Colors.white70 : Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
       ) ],
        ),
      );
  }

  /// Build view for new users without subscription
  Widget _buildNewSubscriptionView(bool isPremium, List<SubscriptionPlan> plans) {
    return Column(
      children: [
        // Header section
        // Container(
        //   margin: const EdgeInsets.all(20),
        //   padding: const EdgeInsets.all(24),
        //   decoration: BoxDecoration(
        //     gradient: isPremium
        //         ? AppTheme.premiumDarkGradient
        //         : AppTheme.primaryGradient,
        //     borderRadius: BorderRadius.circular(20),
        //     boxShadow: isPremium ? AppTheme.premiumBlackShadow : AppTheme.mediumShadow,
        //   ),
        //   child: Column(
        //     children: [
        //       Icon(
        //         Icons.workspace_premium,
        //         size: 60,
        //         color: isPremium ? AppTheme.premiumGold : Colors.white,
        //       ),
        //       const SizedBox(height: 16),
        //       Text(
        //         'Unlock Premium Features',
        //         style: TextStyle(
        //           fontSize: 28,
        //           fontWeight: FontWeight.bold,
        //           color: isPremium ? AppTheme.premiumGold : Colors.white,
        //         ),
        //       ),
        //       const SizedBox(height: 8),
        //       Text(
        //         'Choose a plan that works for you',
        //         style: TextStyle(
        //           fontSize: 16,
        //           color: isPremium ? Colors.white70 : Colors.white.withValues(alpha: 0.9),
        //         ),
        //         textAlign: TextAlign.center,
        //       ),
        //     ],
        //   ),
        // ),

        // Subscription plans carousel
        Expanded(
          child: Column(
            children: [
              // Carousel
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: plans.length,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPlanIndex = index;
                      _selectedPlan = plans[index];
                    });
                  },
                  itemBuilder: (context, index) {
                    final plan = plans[index];
                    return _buildCarouselPlanCard(plan, isPremium);
                  },
                ),
              ),

              // Page indicators
              if (plans.length > 1) ...[
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    plans.length,
                    (index) => _buildPageIndicator(index, isPremium),
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // Subscribe button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _selectedPlan == null || _isLoading
                        ? null
                        : () => _purchaseNewSubscription(_selectedPlan!),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                      foregroundColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 8,
                    ),
                    child: _isLoading
                        ? SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                isPremium ? AppTheme.premiumBlack : Colors.white,
                              ),
                            ),
                          )
                        : Text(
                            'Subscribe Now - ${_selectedPlan?.formattedPrice ?? ''}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Terms and compliance
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isPremium
                      ? AppTheme.premiumLightGrey.withValues(alpha: 0.3)
                      : AppTheme.lightGray.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isPremium
                        ? AppTheme.premiumGold.withValues(alpha: 0.3)
                        : AppTheme.primaryBlue.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'Secure payment via Apple Pay',
                      style: TextStyle(
                        fontSize: 12,
                        color: isPremium ? AppTheme.premiumLightGold : AppTheme.secondaryText,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        GestureDetector(
                          onTap: () => _openTermsOfService(context),
                          child: Text(
                            'Terms',
                            style: TextStyle(
                              fontSize: 12,
                              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                        Text('•', style: TextStyle(color: isPremium ? AppTheme.premiumLightGold : AppTheme.secondaryText)),
                        GestureDetector(
                          onTap: () => _openPrivacyPolicy(context),
                          child: Text(
                            'Privacy',
                            style: TextStyle(
                              fontSize: 12,
                              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                        Text('•', style: TextStyle(color: isPremium ? AppTheme.premiumLightGold : AppTheme.secondaryText)),
                        GestureDetector(
                          onTap: () => _openRefundPolicy(context),
                          child: Text(
                            'Refund',
                            style: TextStyle(
                              fontSize: 12,
                              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ],
    );
  }

  /// Build carousel plan card for the PageView
  Widget _buildCarouselPlanCard(SubscriptionPlan plan, bool isPremium) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        gradient: isPremium
            ? AppTheme.premiumDarkGradient
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.backgroundWhite,
                  AppTheme.lightGradientBg,
                ],
              ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: plan.isRecommended
              ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
              : (isPremium ? AppTheme.premiumLightGrey : AppTheme.lightGray),
          width: plan.isRecommended ? 2 : 1,
        ),
        boxShadow: plan.isRecommended
            ? (isPremium ? AppTheme.premiumBlackShadow : AppTheme.heavyShadow)
            : (isPremium ? AppTheme.premiumBlackShadow : AppTheme.lightShadow),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Plan name
                Text(
                  plan.name,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // Price
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '₹',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                      ),
                    ),
                    Text(
                      '${plan.price.toInt()}',
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                      ),
                    ),
                  ],
                ),

                Text(
                  'for ${plan.formattedDuration}',
                  style: TextStyle(
                    fontSize: 16,
                    color: isPremium ? AppTheme.premiumLightGold : AppTheme.secondaryText,
                  ),
                ),

                const SizedBox(height: 24),

                // Description
                Text(
                  plan.description,
                  style: TextStyle(
                    fontSize: 16,
                    color: isPremium ? Colors.white70 : AppTheme.textRichBlack,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),

                // Features
                ...plan.features.take(3).map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        size: 20,
                        color: isPremium ? AppTheme.premiumGold : AppTheme.successGreen,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          feature,
                          style: TextStyle(
                            fontSize: 15,
                            color: isPremium ? Colors.white : AppTheme.textRichBlack,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),

                // Show more features indicator if there are more than 3
                if (plan.features.length > 3)
                  Text(
                    '+${plan.features.length - 3} more features',
                    style: TextStyle(
                      fontSize: 14,
                      color: isPremium ? AppTheme.premiumLightGold : AppTheme.secondaryText,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),

          // Recommended badge
          if (plan.isRecommended)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  gradient: isPremium
                      ? AppTheme.premiumGoldGradient
                      : AppTheme.primaryGradient,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(24),
                    bottomLeft: Radius.circular(24),
                  ),
                ),
                child: Text(
                  'BEST VALUE',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isPremium ? AppTheme.premiumBlack : Colors.white,
                  ),
                ),
              ),
            ),

          // Savings badge
          if (!plan.hasTrial && plan.savingsPercentage > 0)
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.successGreen,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Save ${plan.savingsPercentage.toInt()}%',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build page indicator for carousel
  Widget _buildPageIndicator(int index, bool isPremium) {
    final isActive = index == _currentPlanIndex;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isActive ? 24 : 8,
      height: 8,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: isActive
            ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
            : (isPremium ? AppTheme.premiumLightGrey : AppTheme.lightGray),
      ),
    );
  }

  /// Build extension plan card for existing subscribers
  Widget _buildExtensionPlanCard(SubscriptionPlan plan, bool isPremium) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: isPremium
            ? AppTheme.premiumDarkGradient
            : LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [AppTheme.backgroundWhite, AppTheme.lightGradientBg],
              ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isPremium
              ? AppTheme.premiumGold.withValues(alpha: 0.3)
              : AppTheme.primaryBlue.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: isPremium ? AppTheme.premiumBlackShadow : AppTheme.lightShadow,
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${AppLocalizations.of(context)!.add} ${plan.formattedDuration}',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    plan.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: isPremium ? AppTheme.premiumLightGold : AppTheme.secondaryText,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  plan.formattedPrice,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  ),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _extendSubscription(plan),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    foregroundColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: _isLoading
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              isPremium ? AppTheme.premiumBlack : Colors.white,
                            ),
                          ),
                        )
                      : Text(AppLocalizations.of(context)!.extend, style: const TextStyle(fontWeight: FontWeight.bold)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Extend subscription for existing users
  Future<void> _extendSubscription(SubscriptionPlan plan) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final subscriptionService = context.read<SubscriptionService>();
      final success = await subscriptionService.extendSubscription(plan, context);

      if (success && mounted) {
        // Reload subscription info
        await _loadSubscriptionInfo();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.subscriptionExtended(plan.formattedDuration)),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.failedToExtendSubscription(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Purchase new subscription for new users
  Future<void> _purchaseNewSubscription(SubscriptionPlan plan) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final subscriptionService = context.read<SubscriptionService>();
      final success = await subscriptionService.purchaseSubscription(plan, context);

      if (success && mounted) {
        // Update theme provider
        final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
        await themeProvider.updatePremiumStatus(true);

        // Navigate back
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.failedToPurchaseSubscription(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }



  /// Open Terms of Service page
  void _openTermsOfService(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          url: 'https://quickposters.in/terms-of-service.html',
          title: AppLocalizations.of(context)!.termsOfService,
        ),
      ),
    );
  }

  /// Open Privacy Policy page
  void _openPrivacyPolicy(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const WebViewPage(
          url: 'https://quickposters.in/privacy-policy.html',
          title: 'Privacy Policy',
        ),
      ),
    );
  }

  /// Open Refund Policy page
  void _openRefundPolicy(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          url: 'https://quickposters.in/refund-policy.html',
          title: AppLocalizations.of(context)!.refundPolicy,
        ),
      ),
    );
  }
}
