import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../settings/presentation/pages/web_view_page.dart';
import '../../domain/entities/subscription_plan.dart';
import '../../domain/usecases/subscription_service.dart';

/// Marketing page for the Premium trial offer
class PremiumTrialPage extends StatefulWidget {
  const PremiumTrialPage({super.key});

  @override
  State<PremiumTrialPage> createState() => _PremiumTrialPageState();
}

class _PremiumTrialPageState extends State<PremiumTrialPage> {
  bool _isLoading = false;
  SubscriptionPlan? _trialPlan;

  @override
  void initState() {
    super.initState();
    // Get the trial plan
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final subscriptionService = context.read<SubscriptionService>();
      final plans = subscriptionService.getAvailablePlans();
      _trialPlan = plans.firstWhere(
        (plan) => plan.hasTrial,
        orElse: () => plans.first,
      );
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16, top: 8),
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppTheme.premiumBlack.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppTheme.premiumGold.withValues(alpha: 0.6),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.premiumGold.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.close,
                  color: AppTheme.premiumGold,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: isPremium
              ? AppTheme.premiumDarkGradient
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.premiumBlack,
                    AppTheme.premiumDarkGrey,
                  ],
                ),
        ),
        child: SafeArea(
          child: Stack(
            children: [

              // Content
              SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),

                      // Premium logo and title
                      Image.asset(
                        'assets/icons/premium_logo.png',
                        height: 80,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'PREMIUM',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.premiumGold,
                          letterSpacing: 2.0,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Unlock the full potential of QuickPosters',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.premiumLightGold,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      // Trial offer
                      Container(
                        margin: const EdgeInsets.symmetric(vertical: 24),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: AppTheme.premiumDarkGradient,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: AppTheme.premiumGold.withValues(alpha: 0.5),
                            width: 2,
                          ),
                          boxShadow: AppTheme.premiumBlackShadow,
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'Special Offer',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.premiumGold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '₹',
                                  style: TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.premiumGold,
                                  ),
                                ),
                                Text(
                                  _trialPlan != null ? '${_trialPlan!.price.toInt()}' : '9',
                                  style: TextStyle(
                                    fontSize: 56,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.premiumGold,
                                  ),
                                ),
                              ],
                            ),
                            Text(
                              _trialPlan != null ? 'for ${_trialPlan!.formattedTrialDuration}' : 'for 7 days',
                              style: TextStyle(
                                fontSize: 18,
                                color: AppTheme.premiumLightGold,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              _trialPlan != null
                                  ? 'Then ₹${_trialPlan!.priceAfterTrial?.toInt() ?? 199} for ${_trialPlan!.formattedDurationAfterTrial}'
                                  : 'Then ₹199 for 3 months',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.premiumLightGold.withValues(alpha: 0.8),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Benefits section
                      const Text(
                        'Premium Benefits',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.premiumGold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Benefits list
                      _buildBenefitItem(
                        icon: Icons.style,
                        title: 'Exclusive Templates',
                        description: 'Access to premium templates not available in the free version',
                      ),
                      _buildBenefitItem(
                        icon: Icons.remove_red_eye,
                        title: 'No Watermarks',
                        description: 'Create posters without QuickPosters watermark',
                      ),
                      _buildBenefitItem(
                        icon: Icons.auto_awesome,
                        title: 'Advanced AI Features',
                        description: 'Enhanced AI image generation and logo creation tools',
                      ),
                      _buildBenefitItem(
                        icon: Icons.color_lens,
                        title: 'Premium Theme',
                        description: 'Elegant gold and black theme for a premium experience',
                      ),
                      _buildBenefitItem(
                        icon: Icons.cloud_upload,
                        title: 'Priority Cloud Storage',
                        description: 'Store more posters and access them from any device',
                      ),
                      _buildBenefitItem(
                        icon: Icons.support_agent,
                        title: 'Priority Support',
                        description: 'Get faster responses to your questions and issues',
                      ),

                      const SizedBox(height: 24),

                      // Mandate information
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppTheme.premiumLightGrey.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppTheme.premiumGold.withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'Subscription Details',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.premiumGold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _trialPlan != null
                                  ? 'Your subscription will automatically renew at ₹${_trialPlan!.priceAfterTrial?.toInt() ?? 199} every ${_trialPlan!.formattedDurationAfterTrial} after the trial period. You can cancel anytime before the trial ends to avoid being charged.'
                                  : 'Your subscription will automatically renew at ₹199 every 3 months after the trial period. You can cancel anytime before the trial ends to avoid being charged.',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.premiumLightGold.withValues(alpha: 0.9),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // App Store Compliance Information
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppTheme.premiumLightGrey.withValues(alpha: 0.05),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppTheme.premiumGold.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            // Subscription Management
                            Row(
                              children: [
                                Icon(
                                  Icons.settings,
                                  size: 16,
                                  color: AppTheme.premiumGold,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Manage subscription in iOS Settings > Apple ID > Subscriptions',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.premiumLightGold.withValues(alpha: 0.8),
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 12),

                            // Terms and Privacy Links
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                GestureDetector(
                                  onTap: () => _openTermsOfService(context),
                                  child: Text(
                                    'Terms of Service',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.premiumGold,
                                      fontWeight: FontWeight.w600,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ),
                                Text(
                                  '•',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: AppTheme.premiumLightGold.withValues(alpha: 0.5),
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () => _openPrivacyPolicy(context),
                                  child: Text(
                                    'Privacy Policy',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.premiumGold,
                                      fontWeight: FontWeight.w600,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 100), // Space for the button at bottom
                    ],
                  ),
                ),
              ),

              // Start Trial button at bottom
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        AppTheme.premiumBlack.withValues(alpha: 0.8),
                        AppTheme.premiumBlack,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    top: false,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.premiumGold.withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: GradientButton(
                        text: _trialPlan != null
                            ? 'Start ${_trialPlan!.formattedTrialDuration} Trial for ₹${_trialPlan!.price.toInt()}'
                            : 'Start 7-Day Trial for ₹9',
                        onPressed: _isLoading || _trialPlan == null ? () {} : () => _startTrial(context),
                        isLoading: _isLoading,
                        gradient: AppTheme.premiumGoldGradient,
                        textStyle: const TextStyle(
                          color: AppTheme.premiumBlack,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build a benefit item with icon, title and description
  Widget _buildBenefitItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppTheme.premiumDarkGradient,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.premiumGold.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: AppTheme.premiumGoldGradient,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.premiumGold.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: AppTheme.premiumBlack,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.premiumGold,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.premiumLightGold.withValues(alpha: 0.9),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Start the trial subscription
  Future<void> _startTrial(BuildContext context) async {
    if (_trialPlan == null) return;

    setState(() {
      _isLoading = true;
    });

    // Store references before async operations
    final navigator = Navigator.of(context);
    final messenger = ScaffoldMessenger.of(context);
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    try {
      final subscriptionService = context.read<SubscriptionService>();
      final success = await subscriptionService.purchaseSubscription(
        _trialPlan!,
        context,
      );

      if (success && mounted) {
        // Update premium status
        await themeProvider.updatePremiumStatus(true);

        // Check mounted again after async gap
        if (mounted) {
          // Navigate back to home
          navigator.pop();

          // Show success message
          messenger.showSnackBar(
            SnackBar(
              content: const Text('Premium trial activated successfully!'),
              backgroundColor: AppTheme.successGreen,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        messenger.showSnackBar(
          SnackBar(
            content: Text('Failed to start trial: $e'),
            backgroundColor: AppTheme.errorRed,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Open Terms of Service page
  void _openTermsOfService(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const WebViewPage(
          url: 'https://quickposters.in/terms-of-service.html',
          title: 'Terms of Service',
        ),
      ),
    );
  }

  /// Open Privacy Policy page
  void _openPrivacyPolicy(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const WebViewPage(
          url: 'https://quickposters.in/privacy-policy.html',
          title: 'Privacy Policy',
        ),
      ),
    );
  }
}
