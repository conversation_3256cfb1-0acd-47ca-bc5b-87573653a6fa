import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'dart:async';
import 'dart:io';
import '../entities/subscription_plan.dart';
import '../../../user/domain/usecases/user_service.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/services/apple_pay_service.dart';

/// Service for handling subscription-related operations
class SubscriptionService {
  final UserService _userService;
  final ApplePayService _applePayService;
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  // Product IDs for App Store Connect
  static const String _trialProductId = 'com.quickposters.trials';
  static const String _threeMonthProductId = 'com.quickposters.three_month';
  static const String _sixMonthProductId = 'com.quickposters.six_month';
  static const String _annualProductId = 'com.quickposters.annual';

  // Available product IDs
  static const Set<String> _productIds = {
    _trialProductId,
    _threeMonthProductId,
    _sixMonthProductId,
    _annualProductId,
  };

  // Available products from the store
  List<ProductDetails> _products = [];

  // Purchase stream subscription
  StreamSubscription<List<PurchaseDetails>>? _subscription;

  /// Constructor
  SubscriptionService(this._userService, this._applePayService);

  /// Initialize the subscription service
  Future<void> initialize() async {
    try {
      // Initialize Apple Pay service
      await _applePayService.initialize(_userService);
      AppLogger.info('Apple Pay service initialized');

      // Check if in-app purchases are available
      AppLogger.info('Checking if in-app purchases are available...');
      final bool available = await _inAppPurchase.isAvailable();
      AppLogger.info('In-app purchases available: $available');

      if (!available) {
        AppLogger.error('In-app purchases not available - this could be due to:');
        AppLogger.error('1. Running on simulator (use real device)');
        AppLogger.error('2. Bundle ID mismatch with App Store Connect');
        AppLogger.error('3. App not configured in App Store Connect');
        AppLogger.error('4. Network connectivity issues');
        return;
      }

      // Load products from the store (for subscriptions)
      AppLogger.info('Querying SUBSCRIPTION products with IDs: $_productIds');
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds);

      AppLogger.info('Query response - Found: ${response.productDetails.length}, Not found: ${response.notFoundIDs.length}');

      if (response.productDetails.isNotEmpty) {
        AppLogger.info('Found products:');
        for (final product in response.productDetails) {
          AppLogger.info('  - ${product.id}: ${product.title} (${product.price})');
        }
      }

      if (response.notFoundIDs.isNotEmpty) {
        AppLogger.warning('Products not found: ${response.notFoundIDs}');
      }

      if (response.error != null) {
        AppLogger.error('Product query error: ${response.error}');
      }

      _products = response.productDetails;
      AppLogger.info('Loaded ${_products.length} products');

      // Listen to purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _handlePurchaseUpdates,
        onDone: () {
          _subscription?.cancel();
        },
        onError: (error) {
          AppLogger.error('Purchase stream error', error);
        },
      );

    } catch (e) {
      AppLogger.error('Error initializing subscription service', e);
    }
  }

  /// Dispose resources
  void dispose() {
    _subscription?.cancel();
  }

  /// Handle purchase updates from the store
  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        // Handle pending purchase
        AppLogger.info('Purchase pending: ${purchaseDetails.productID}');
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        // Handle purchase error
        AppLogger.error('Purchase error: ${purchaseDetails.error}');
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
                 purchaseDetails.status == PurchaseStatus.restored) {
        // Handle successful purchase or restore
        _handleSuccessfulPurchase(purchaseDetails);
      }

      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// Handle successful purchase
  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      AppLogger.info('Purchase successful: ${purchaseDetails.productID}');

      // Update user's premium status
      await _userService.updatePremiumStatus(true);

      // Log the purchase for analytics
      AppLogger.info('Premium status updated for product: ${purchaseDetails.productID}');
    } catch (e) {
      AppLogger.error('Error handling successful purchase', e);
    }
  }

  /// Restore previous purchases using Apple Pay service
  Future<bool> restorePurchases(BuildContext context) async {
    try {
      return await _applePayService.restorePurchases(context);
    } catch (e) {
      AppLogger.error('Error restoring purchases', e);
      return false;
    }
  }

  /// Get product details for a specific plan
  ProductDetails? getProductDetails(String productId) {
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// Purchase a specific product
  Future<bool> buyProduct(ProductDetails productDetails) async {
    try {
      final PurchaseParam purchaseParam = PurchaseParam(productDetails: productDetails);

      if (Platform.isIOS) {
        await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        await _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);
      }

      return true;
    } catch (e) {
      AppLogger.error('Error purchasing product', e);
      return false;
    }
  }

  /// Get all available subscription plans
  List<SubscriptionPlan> getAvailablePlans() {
    return SubscriptionPlan.predefinedPlans;
  }

  /// Get the recommended subscription plan
  SubscriptionPlan? getRecommendedPlan() {
    final plans = getAvailablePlans();
    try {
      return plans.firstWhere((plan) => plan.isRecommended);
    } catch (e) {
      // If no plan is marked as recommended, return the first one
      return plans.isNotEmpty ? plans.first : null;
    }
  }

  /// Check if the user is currently subscribed
  Future<bool> isUserSubscribed() async {
    try {
      return await _userService.isPremiumUser();
    } catch (e) {
      AppLogger.error('Error checking subscription status', e);
      return false;
    }
  }

  /// Process a subscription purchase using Apple Pay
  Future<bool> purchaseSubscription(SubscriptionPlan plan, BuildContext context) async {
    try {
      AppLogger.info('Starting subscription purchase for plan: ${plan.id}');

      // Use Apple Pay service for the actual purchase
      final success = await _applePayService.purchaseSubscription(plan, context);

      if (success) {
        AppLogger.info('Subscription purchase successful for plan: ${plan.id}');

        // Save subscription info to Firebase
        await _saveSubscriptionToFirebase(plan);

        return true;
      } else {
        AppLogger.error('Subscription purchase failed for plan: ${plan.id}');
        return false;
      }
    } catch (e) {
      AppLogger.error('Error processing subscription purchase', e);

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to process payment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      return false;
    }
  }

  /// Cancel the current subscription
  ///
  /// This is a placeholder for actual subscription cancellation logic
  Future<bool> cancelSubscription(BuildContext context) async {
    try {
      // Store navigator and messenger references before async operations
      final navigator = Navigator.of(context);
      final messenger = ScaffoldMessenger.of(context);

      // Show a loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Simulate cancellation processing delay
      await Future.delayed(const Duration(seconds: 1));

      // Close the loading indicator
      navigator.pop();

      // Update the user's premium status
      await _userService.updatePremiumStatus(false);

      // Show success message
      messenger.showSnackBar(
        const SnackBar(
          content: Text('Subscription successfully cancelled'),
          backgroundColor: Colors.green,
        ),
      );

      return true;
    } catch (e) {
      // Close the loading indicator if it's still showing
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      AppLogger.error('Error cancelling subscription', e);

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel subscription: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      return false;
    }
  }

  /// Save subscription information to Firebase
  Future<void> _saveSubscriptionToFirebase(SubscriptionPlan plan) async {
    try {
      final endDate = DateTime.now().add(Duration(days: plan.durationDays));

      await _userService.updateSubscriptionInfo(
        planId: plan.id,
        endDate: endDate,
        purchaseDate: DateTime.now(),
      );

      AppLogger.info('Subscription info saved to Firebase: ${plan.id}');
    } catch (e) {
      AppLogger.error('Error saving subscription to Firebase', e);
      rethrow;
    }
  }

  /// Extend current subscription
  Future<bool> extendSubscription(SubscriptionPlan plan, BuildContext context) async {
    try {
      AppLogger.info('Starting subscription extension for plan: ${plan.id}');

      // Use Apple Pay service for the actual purchase
      final success = await _applePayService.purchaseSubscription(plan, context);

      if (success) {
        AppLogger.info('Subscription extension successful for plan: ${plan.id}');

        // Extend subscription in Firebase
        await _userService.extendSubscription(
          newPlanId: plan.id,
          additionalDays: plan.durationDays,
        );

        return true;
      } else {
        AppLogger.error('Subscription extension failed for plan: ${plan.id}');
        return false;
      }
    } catch (e) {
      AppLogger.error('Error extending subscription', e);

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to extend subscription: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      return false;
    }
  }

  /// Get current user's subscription information
  Future<Map<String, dynamic>?> getCurrentUserSubscriptionInfo() async {
    try {
      return await _userService.getCurrentUserSubscriptionInfo();
    } catch (e) {
      AppLogger.error('Error getting subscription info', e);
      return null;
    }
  }

  /// Check if user has an active subscription
  Future<bool> hasActiveSubscription() async {
    try {
      final subscriptionInfo = await getCurrentUserSubscriptionInfo();
      return subscriptionInfo?['hasActiveSubscription'] ?? false;
    } catch (e) {
      AppLogger.error('Error checking active subscription', e);
      return false;
    }
  }
}
