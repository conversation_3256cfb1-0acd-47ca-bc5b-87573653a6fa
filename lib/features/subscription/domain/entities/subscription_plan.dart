import 'package:flutter/material.dart';
import '../../../../l10n/generated/app_localizations.dart';

/// Represents a subscription plan in the app
class SubscriptionPlan {
  /// Unique identifier for the plan
  final String id;

  /// Display name of the plan
  final String name;

  /// Description of the plan
  final String description;

  /// Price in INR
  final double price;

  /// Duration in days
  final int durationDays;

  /// Whether this plan has a trial period
  final bool hasTrial;

  /// Trial duration in days (if applicable)
  final int? trialDays;

  /// Price after trial period (if applicable)
  final double? priceAfterTrial;

  /// Duration after trial period in days (if applicable)
  final int? durationAfterTrialDays;

  /// Features included in this plan
  final List<String> features;

  /// Whether this plan is recommended
  final bool isRecommended;

  /// Constructor
  const SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.durationDays,
    this.hasTrial = false,
    this.trialDays,
    this.priceAfterTrial,
    this.durationAfterTrialDays,
    required this.features,
    this.isRecommended = false,
  });

  /// Get the formatted price string (e.g., "₹199")
  String get formattedPrice => '₹${price.toInt()}';

  /// Get the formatted duration string (e.g., "3 months", "1 year")
  String get formattedDuration {
    if (durationDays >= 365) {
      final years = durationDays ~/ 365;
      return '$years ${years == 1 ? 'year' : 'years'}';
    } else if (durationDays >= 30) {
      final months = durationDays ~/ 30;
      return '$months ${months == 1 ? 'month' : 'months'}';
    } else {
      return '$durationDays ${durationDays == 1 ? 'day' : 'days'}';
    }
  }

  /// Get the formatted trial duration string (e.g., "8 days")
  String? get formattedTrialDuration {
    if (!hasTrial || trialDays == null) return null;

    return '$trialDays ${trialDays == 1 ? 'day' : 'days'}';
  }

  /// Get the formatted price after trial string (e.g., "₹199")
  String? get formattedPriceAfterTrial {
    if (!hasTrial || priceAfterTrial == null) return null;

    return '₹${priceAfterTrial!.toInt()}';
  }

  /// Get the formatted duration after trial string (e.g., "3 months")
  String? get formattedDurationAfterTrial {
    if (!hasTrial || durationAfterTrialDays == null) return null;

    if (durationAfterTrialDays! >= 365) {
      final years = durationAfterTrialDays! ~/ 365;
      return '$years ${years == 1 ? 'year' : 'years'}';
    } else if (durationAfterTrialDays! >= 30) {
      final months = durationAfterTrialDays! ~/ 30;
      return '$months ${months == 1 ? 'month' : 'months'}';
    } else {
      return '$durationAfterTrialDays ${durationAfterTrialDays == 1 ? 'day' : 'days'}';
    }
  }

  /// Calculate the effective price per day
  double get pricePerDay {
    if (hasTrial && trialDays != null && priceAfterTrial != null && durationAfterTrialDays != null) {
      // For trial plans, calculate the effective price including the trial period
      return (price + priceAfterTrial!) / (trialDays! + durationAfterTrialDays!);
    } else {
      return price / durationDays;
    }
  }

  /// Calculate savings percentage compared to monthly pricing (assuming monthly is ₹199 for 3 months)
  double get savingsPercentage {
    // Base monthly price (₹199 for 3 months)
    const double monthlyPriceFor3Months = 199.0;
    const int monthlyDurationDays = 90; // 3 months

    // Calculate the equivalent cost at monthly pricing
    final double equivalentMonthlyPrice = (durationDays / monthlyDurationDays) * monthlyPriceFor3Months;

    // Calculate savings
    final double savings = equivalentMonthlyPrice - price;

    // Calculate savings percentage
    return (savings / equivalentMonthlyPrice) * 100;
  }

  /// Predefined subscription plans
  static List<SubscriptionPlan> get predefinedPlans => [
    // 1-week trial plan
    const SubscriptionPlan(
      id: 'trial',
      name: 'Trial Plan',
      description: 'Try Premium features for 1 week at minimal cost',
      price: 9,
      durationDays: 7, // 1 week
      hasTrial: true,
      trialDays: 7,
      priceAfterTrial: 199,
      durationAfterTrialDays: 90, // 3 months
      features: [
        'All Premium features',
        'Auto-renews at ₹199 for 3 months',
        'Cancel anytime',
      ],
    ),

    // 3-month plan
    const SubscriptionPlan(
      id: 'three_month',
      name: '3-Month Plan',
      description: 'Perfect for short-term projects',
      price: 199,
      durationDays: 90, // 3 months
      features: [
        'All Premium features',
        'Quarterly billing',
        'Cancel anytime',
      ],
    ),

    // 6-month plan
    const SubscriptionPlan(
      id: 'six_month',
      name: '6-Month Plan',
      description: 'Great value for medium-term use',
      price: 299,
      durationDays: 180, // 6 months
      features: [
        'All Premium features',
        'Save 50% compared to 3-month plan',
        'Priority support',
      ],
    ),

    // 1-year plan
    const SubscriptionPlan(
      id: 'annual',
      name: 'Annual Plan',
      description: 'Best value for regular users',
      price: 499,
      durationDays: 365, // 1 year
      features: [
        'All Premium features',
        'Save 58% compared to 3-month plan',
        'Priority support',
        'Early access to new features',
      ],
      isRecommended: true,
    ),
  ];

  /// Get localized subscription plans
  static List<SubscriptionPlan> getLocalizedPlans(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return [
      // 1-week trial plan
      SubscriptionPlan(
        id: 'trial',
        name: localizations.trialPlan,
        description: localizations.trialPlanDescription,
        price: 9,
        durationDays: 7, // 1 week
        hasTrial: true,
        trialDays: 7,
        priceAfterTrial: 199,
        durationAfterTrialDays: 90, // 3 months
        features: [
          localizations.allPremiumFeatures,
          localizations.autoRenewsAt,
          localizations.cancelAnytime,
        ],
      ),

      // 3-month plan
      SubscriptionPlan(
        id: 'three_month',
        name: localizations.threeMonthPlan,
        description: localizations.threeMonthPlanDescription,
        price: 199,
        durationDays: 90, // 3 months
        features: [
          localizations.allPremiumFeatures,
          localizations.quarterlyBilling,
          localizations.cancelAnytime,
        ],
      ),

      // 6-month plan
      SubscriptionPlan(
        id: 'six_month',
        name: localizations.sixMonthPlan,
        description: localizations.sixMonthPlanDescription,
        price: 299,
        durationDays: 180, // 6 months
        features: [
          localizations.allPremiumFeatures,
          localizations.save50Percent,
          localizations.prioritySupport,
        ],
      ),

      // 1-year plan
      SubscriptionPlan(
        id: 'annual',
        name: localizations.annualPlan,
        description: localizations.annualPlanDescription,
        price: 499,
        durationDays: 365, // 1 year
        features: [
          localizations.allPremiumFeatures,
          localizations.save58Percent,
          localizations.prioritySupport,
          localizations.earlyAccessToNewFeatures,
        ],
        isRecommended: true,
      ),
    ];
  }
}
