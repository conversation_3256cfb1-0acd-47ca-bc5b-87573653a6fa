import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/party.dart';
import '../../domain/repositories/party_repository.dart';

/// Implementation of PartyRepository using Firebase
class FirebasePartyRepository implements PartyRepository {
  final FirebaseFirestore _firestore;
  // Make sure this matches exactly the collection name in Firebase
  final String _collection = 'politicalParties';

  FirebasePartyRepository({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  @override
  Future<List<Party>> getAllParties() async {
    try {
      AppLogger.info('Fetching all parties from collection: $_collection');

      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('name')
          .get();

      AppLogger.info('Query returned ${querySnapshot.docs.length} parties');

      // Log each document for debugging
      for (var doc in querySnapshot.docs) {
        final data = doc.data();
        AppLogger.info('Party ID: ${doc.id}, name: ${data['name']}');
      }

      return querySnapshot.docs
          .map((doc) => Party.fromFirestore(doc))
          .toList();
    } catch (e) {
      AppLogger.error('Error getting parties', e);
      return [];
    }
  }

  @override
  Future<Party?> getPartyById(String id) async {
    try {
      AppLogger.info('Getting party by ID: $id from collection: $_collection');

      final docSnapshot = await _firestore
          .collection(_collection)
          .doc(id)
          .get();

      if (docSnapshot.exists) {
        AppLogger.info('Found party with ID: $id, name: ${docSnapshot.data()?['name']}');
        final party = Party.fromFirestore(docSnapshot);
        AppLogger.info('Successfully parsed party: ${party.name} (${party.shortName})');
        return party;
      } else {
        AppLogger.info('Party with ID: $id not found');
      }
      return null;
    } catch (e) {
      AppLogger.error('Error getting party with ID: $id', e);
      return null;
    }
  }

  @override
  Future<List<Party>> getActiveParties() async {
    try {
      AppLogger.info('Fetching active parties from collection: $_collection');

      // First, check if the collection exists and has documents
      final collectionRef = _firestore.collection(_collection);
      final checkSnapshot = await collectionRef.limit(1).get();

      AppLogger.info('Collection check: exists=${checkSnapshot.docs.isNotEmpty}, size=${checkSnapshot.size}');

      // Get all parties first, then filter in memory to avoid needing a composite index
      final querySnapshot = await collectionRef.get();

      AppLogger.info('Query returned ${querySnapshot.docs.length} total documents');

      // Filter active parties and convert to Party objects
      final parties = querySnapshot.docs
          .map((doc) => Party.fromFirestore(doc))
          .where((party) => party.isActive) // Filter active parties in memory
          .toList();

      // Sort by name
      parties.sort((a, b) => a.name.compareTo(b.name));

      AppLogger.info('Found ${parties.length} active parties after filtering');

      return parties;
    } catch (e) {
      AppLogger.error('Error getting active parties', e);
      return [];
    }
  }
}
