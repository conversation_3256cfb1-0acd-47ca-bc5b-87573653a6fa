import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/user_type.dart';

class UserTypeRepository {
  final FirebaseFirestore _firestore;
  final String _collection = 'userTypes';

  UserTypeRepository({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Get all user types from Firestore
  Future<List<UserType>> getAllUserTypes() async {
    try {
      final querySnapshot = await _firestore.collection(_collection).get();
      return querySnapshot.docs
          .map((doc) => UserType.fromFirestore(doc))
          .toList();
    } catch (e) {
      // Log the error
      print('Error getting user types: $e');
      // Return an empty list if there's an error
      return [];
    }
  }

  /// Get a user type by ID
  Future<UserType?> getUserTypeById(String id) async {
    try {
      final docSnapshot = await _firestore.collection(_collection).doc(id).get();
      if (docSnapshot.exists) {
        return UserType.fromFirestore(docSnapshot);
      }
      return null;
    } catch (e) {
      // Log the error
      print('Error getting user type: $e');
      return null;
    }
  }

  /// Create default user types in Firestore if they don't exist
  Future<void> createDefaultUserTypes() async {
    try {
      // Check if user types collection exists and has documents
      final querySnapshot = await _firestore.collection(_collection).limit(1).get();
      if (querySnapshot.docs.isEmpty) {
        // Create default user types
        final batch = _firestore.batch();
        
        // Politician
        final politicianRef = _firestore.collection(_collection).doc('politician');
        batch.set(politicianRef, {
          'name': 'Politician',
          'description': 'For political campaigns and promotions',
          'iconName': 'account_balance',
        });
        
        // Businessman
        final businessmanRef = _firestore.collection(_collection).doc('businessman');
        batch.set(businessmanRef, {
          'name': 'Businessman',
          'description': 'For business promotions and advertisements',
          'iconName': 'business',
        });
        
        // Regular User
        final userRef = _firestore.collection(_collection).doc('user');
        batch.set(userRef, {
          'name': 'Regular User',
          'description': 'For personal use and general purposes',
          'iconName': 'person',
        });
        
        // Commit the batch
        await batch.commit();
      }
    } catch (e) {
      // Log the error
      print('Error creating default user types: $e');
    }
  }
}
