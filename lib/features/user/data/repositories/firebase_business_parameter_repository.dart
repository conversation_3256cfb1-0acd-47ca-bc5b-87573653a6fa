import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/business_parameter.dart';
import '../../domain/repositories/business_parameter_repository.dart';

/// Implementation of BusinessParameterRepository using Firebase
class FirebaseBusinessParameterRepository implements BusinessParameterRepository {
  final FirebaseFirestore _firestore;
  // Make sure this matches exactly the collection name in Firebase
  final String _collection = 'businessParameters';

  FirebaseBusinessParameterRepository({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  @override
  Future<List<BusinessParameter>> getAllParameters() async {
    try {
      AppLogger.info('Fetching all business parameters from collection: $_collection');

      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('displayOrder')
          .get();

      AppLogger.info('Query returned ${querySnapshot.docs.length} documents');

      // Log each document for debugging
      for (var doc in querySnapshot.docs) {
        final data = doc.data();
        AppLogger.info('Document ID: ${doc.id}, name: ${data['name']}, type: ${data['type']}');
      }

      return querySnapshot.docs
          .map((doc) => BusinessParameter.fromFirestore(doc))
          .toList();
    } catch (e) {
      AppLogger.error('Error getting business parameters', e);
      return [];
    }
  }

  @override
  Future<BusinessParameter?> getParameterById(String id) async {
    try {
      final docSnapshot = await _firestore.collection(_collection).doc(id).get();

      if (docSnapshot.exists) {
        return BusinessParameter.fromFirestore(docSnapshot);
      }
      return null;
    } catch (e) {
      AppLogger.error('Error getting business parameter', e);
      return null;
    }
  }

  @override
  Future<List<BusinessParameter>> getActiveParameters() async {
    try {
      AppLogger.info('Fetching active business parameters from collection: $_collection');

      // First, check if the collection exists and has documents
      final collectionRef = _firestore.collection(_collection);
      final checkSnapshot = await collectionRef.limit(1).get();

      AppLogger.info('Collection check: exists=${checkSnapshot.docs.isNotEmpty}, size=${checkSnapshot.size}');

      // Get all parameters first, then filter in memory to avoid needing a composite index
      final querySnapshot = await collectionRef.get();

      AppLogger.info('Query returned ${querySnapshot.docs.length} total documents');

      // Filter active parameters and convert to BusinessParameter objects
      final parameters = querySnapshot.docs
          .map((doc) => BusinessParameter.fromFirestore(doc))
          .where((param) => param.isActive) // Filter active parameters in memory
          .toList();

      // Sort by display order
      parameters.sort((a, b) => a.displayOrder.compareTo(b.displayOrder));

      AppLogger.info('Found ${parameters.length} active parameters after filtering');

      // Log each parameter for debugging
      for (var param in parameters) {
        AppLogger.info('Parameter ID: ${param.id}, name: ${param.name}, type: ${param.type}');
      }

      return parameters;
    } catch (e) {
      AppLogger.error('Error getting active business parameters', e);
      return [];
    }
  }
}
