import '../entities/business_parameter.dart';

/// Repository interface for business parameter operations
abstract class BusinessParameterRepository {
  /// Get all business parameters
  Future<List<BusinessParameter>> getAllParameters();
  
  /// Get a business parameter by ID
  Future<BusinessParameter?> getParameterById(String id);
  
  /// Get active business parameters
  Future<List<BusinessParameter>> getActiveParameters();
}
