import '../entities/user_model.dart';

/// Repository interface for user-related operations
abstract class UserRepository {
  /// Create a new user in the database
  Future<void> createUser(UserModel user);

  /// Get a user by their UID
  Future<UserModel?> getUserById(String uid);

  /// Update an existing user's information
  Future<void> updateUser(UserModel user);

  /// Delete a user from the database
  Future<void> deleteUser(String uid);

  /// Check if a user exists in the database
  Future<bool> userExists(String uid);

  /// Get the current authenticated user
  Future<UserModel?> getCurrentUser();

  /// Upload a profile photo for a user
  Future<String?> uploadProfilePhoto(String uid, String localFilePath);

  /// Upload a business logo for a user
  Future<String?> uploadBusinessLogo(String uid, String localFilePath);

  /// Upload a political photo for a user
  Future<String?> uploadPoliticalPhoto(String uid, String localFilePath);
}
