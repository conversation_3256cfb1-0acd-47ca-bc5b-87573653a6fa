import '../entities/political_parameter.dart';

/// Repository interface for political parameters
abstract class PoliticalParameterRepository {
  /// Get all political parameters
  Future<List<PoliticalParameter>> getAllParameters();
  
  /// Get a political parameter by ID
  Future<PoliticalParameter?> getParameterById(String id);
  
  /// Get all active political parameters
  Future<List<PoliticalParameter>> getActiveParameters();
}
