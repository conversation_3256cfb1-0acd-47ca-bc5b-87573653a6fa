import '../entities/business_parameter.dart';
import '../repositories/business_parameter_repository.dart';

/// Service class for business parameter operations
class BusinessParameterService {
  final BusinessParameterRepository _repository;

  BusinessParameterService(this._repository);

  /// Get all business parameters
  Future<List<BusinessParameter>> getAllParameters() async {
    return await _repository.getAllParameters();
  }

  /// Get a business parameter by ID
  Future<BusinessParameter?> getParameterById(String id) async {
    return await _repository.getParameterById(id);
  }

  /// Get active business parameters
  Future<List<BusinessParameter>> getActiveParameters() async {
    return await _repository.getActiveParameters();
  }
}
