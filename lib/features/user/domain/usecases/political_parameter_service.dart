import '../entities/political_parameter.dart';
import '../repositories/political_parameter_repository.dart';

/// Service for managing political parameters
class PoliticalParameterService {
  final PoliticalParameterRepository _repository;

  /// Creates a new political parameter service
  PoliticalParameterService(this._repository);

  /// Get all political parameters
  Future<List<PoliticalParameter>> getAllParameters() async {
    return await _repository.getAllParameters();
  }

  /// Get a political parameter by ID
  Future<PoliticalParameter?> getParameterById(String id) async {
    return await _repository.getParameterById(id);
  }

  /// Get all active political parameters
  Future<List<PoliticalParameter>> getActiveParameters() async {
    return await _repository.getActiveParameters();
  }
}
