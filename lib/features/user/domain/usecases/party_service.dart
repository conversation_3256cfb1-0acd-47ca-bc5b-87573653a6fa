import '../entities/party.dart';
import '../repositories/party_repository.dart';

/// Service for managing political parties
class PartyService {
  final PartyRepository _repository;

  /// Creates a new party service
  PartyService(this._repository);

  /// Get all political parties
  Future<List<Party>> getAllParties() async {
    return await _repository.getAllParties();
  }

  /// Get a political party by ID
  Future<Party?> getPartyById(String id) async {
    return await _repository.getPartyById(id);
  }

  /// Get all active political parties
  Future<List<Party>> getActiveParties() async {
    return await _repository.getActiveParties();
  }
}
