import '../entities/user_type.dart';
import '../../data/repositories/user_type_repository.dart';

class UserTypeService {
  final UserTypeRepository _repository;

  UserTypeService(this._repository);

  /// Get all user types
  Future<List<UserType>> getAllUserTypes() async {
    return await _repository.getAllUserTypes();
  }

  /// Get a user type by ID
  Future<UserType?> getUserTypeById(String id) async {
    return await _repository.getUserTypeById(id);
  }

  /// Initialize the user types in Firestore
  Future<void> initializeUserTypes() async {
    await _repository.createDefaultUserTypes();
  }
}
