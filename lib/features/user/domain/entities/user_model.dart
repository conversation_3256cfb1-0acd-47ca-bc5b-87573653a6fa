import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'business_profile.dart';
import 'political_profile.dart';

/// Model class representing a user in the application
class UserModel extends Equatable {
  /// Unique identifier for the user (Firebase UID)
  final String uid;

  /// User's display name
  final String name;

  /// User's email address
  final String? email;

  /// User's phone number
  final String? phoneNumber;

  /// URL to the user's profile photo
  final String? photoUrl;

  /// When the user account was created
  final DateTime createdAt;

  /// When the user profile was last updated
  final DateTime updatedAt;

  /// Whether the user has completed their profile setup
  final bool isProfileComplete;

  /// User's preferred language code (e.g., 'en', 'hi', 'mr')
  final String languageCode;

  /// List of poster IDs created by this user
  final List<String> posterIds;

  /// User type (politician, businessman, or regular user)
  final String? userType;

  /// Whether the user is a premium user
  final bool isPremium;

  /// Subscription end date (null if not subscribed)
  final DateTime? subscriptionEndDate;

  /// Current subscription plan ID (null if not subscribed)
  final String? subscriptionPlanId;

  /// Subscription purchase date (null if not subscribed)
  final DateTime? subscriptionPurchaseDate;

  /// Whether the user has admin privileges
  final bool isAdmin;

  /// Business profile for businessman user type
  final BusinessProfile? businessProfile;

  /// Political profile for politician user type
  final PoliticalProfile? politicalProfile;

  const UserModel({
    required this.uid,
    required this.name,
    this.email,
    this.phoneNumber,
    this.photoUrl,
    required this.createdAt,
    required this.updatedAt,
    this.isProfileComplete = false,
    this.languageCode = 'en',
    this.posterIds = const [],
    this.userType,
    this.isPremium = false,
    this.subscriptionEndDate,
    this.subscriptionPlanId,
    this.subscriptionPurchaseDate,
    this.isAdmin = false,
    this.businessProfile,
    this.politicalProfile,
  });

  /// Create a new user with default values
  factory UserModel.create({
    required String uid,
    required String name,
    String? email,
    String? phoneNumber,
    String? photoUrl,
    String languageCode = 'en',
    String? userType,
    bool isPremium = false,
    DateTime? subscriptionEndDate,
    String? subscriptionPlanId,
    DateTime? subscriptionPurchaseDate,
    bool isAdmin = false,
    BusinessProfile? businessProfile,
    PoliticalProfile? politicalProfile,
  }) {
    final now = DateTime.now();
    return UserModel(
      uid: uid,
      name: name,
      email: email,
      phoneNumber: phoneNumber,
      photoUrl: photoUrl,
      createdAt: now,
      updatedAt: now,
      isProfileComplete: false,
      languageCode: languageCode,
      posterIds: [],
      userType: userType,
      isPremium: isPremium,
      subscriptionEndDate: subscriptionEndDate,
      subscriptionPlanId: subscriptionPlanId,
      subscriptionPurchaseDate: subscriptionPurchaseDate,
      isAdmin: isAdmin,
      businessProfile: businessProfile,
      politicalProfile: politicalProfile,
    );
  }

  /// Create a user model from Firestore document
  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Parse business profile if it exists
    BusinessProfile? businessProfile;
    if (data['businessProfile'] != null) {
      businessProfile = BusinessProfile.fromMap(data['businessProfile']);
    }

    // Parse political profile if it exists
    PoliticalProfile? politicalProfile;
    if (data['politicalProfile'] != null) {
      politicalProfile = PoliticalProfile.fromMap(data['politicalProfile']);
    }

    return UserModel(
      uid: doc.id,
      name: data['name'] ?? '',
      email: data['email'],
      phoneNumber: data['phoneNumber'],
      photoUrl: data['photoUrl'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      isProfileComplete: data['isProfileComplete'] ?? false,
      languageCode: data['languageCode'] ?? 'en',
      posterIds: List<String>.from(data['posterIds'] ?? []),
      userType: data['userType'],
      isPremium: data['isPremium'] ?? false,
      subscriptionEndDate: data['subscriptionEndDate'] != null
          ? (data['subscriptionEndDate'] as Timestamp).toDate()
          : null,
      subscriptionPlanId: data['subscriptionPlanId'],
      subscriptionPurchaseDate: data['subscriptionPurchaseDate'] != null
          ? (data['subscriptionPurchaseDate'] as Timestamp).toDate()
          : null,
      isAdmin: data['isAdmin'] ?? false,
      businessProfile: businessProfile,
      politicalProfile: politicalProfile,
    );
  }

  /// Convert user model to a map for Firestore
  Map<String, dynamic> toFirestore() {
    final Map<String, dynamic> data = {
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'photoUrl': photoUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isProfileComplete': isProfileComplete,
      'languageCode': languageCode,
      'posterIds': posterIds,
      'userType': userType,
      'isPremium': isPremium,
      'subscriptionEndDate': subscriptionEndDate != null
          ? Timestamp.fromDate(subscriptionEndDate!)
          : null,
      'subscriptionPlanId': subscriptionPlanId,
      'subscriptionPurchaseDate': subscriptionPurchaseDate != null
          ? Timestamp.fromDate(subscriptionPurchaseDate!)
          : null,
      'isAdmin': isAdmin,
    };

    // Add business profile if it exists
    if (businessProfile != null) {
      data['businessProfile'] = businessProfile!.toMap();
    }

    // Add political profile if it exists
    if (politicalProfile != null) {
      data['politicalProfile'] = politicalProfile!.toMap();
    }

    return data;
  }

  /// Create a copy of this user with the given fields replaced with new values
  UserModel copyWith({
    String? name,
    String? email,
    String? phoneNumber,
    String? photoUrl,
    DateTime? updatedAt,
    bool? isProfileComplete,
    String? languageCode,
    List<String>? posterIds,
    String? userType,
    bool? isPremium,
    DateTime? subscriptionEndDate,
    String? subscriptionPlanId,
    DateTime? subscriptionPurchaseDate,
    bool? isAdmin,
    BusinessProfile? businessProfile,
    PoliticalProfile? politicalProfile,
  }) {
    return UserModel(
      uid: uid,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      photoUrl: photoUrl ?? this.photoUrl,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      languageCode: languageCode ?? this.languageCode,
      posterIds: posterIds ?? this.posterIds,
      userType: userType ?? this.userType,
      isPremium: isPremium ?? this.isPremium,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
      subscriptionPlanId: subscriptionPlanId ?? this.subscriptionPlanId,
      subscriptionPurchaseDate: subscriptionPurchaseDate ?? this.subscriptionPurchaseDate,
      isAdmin: isAdmin ?? this.isAdmin,
      businessProfile: businessProfile ?? this.businessProfile,
      politicalProfile: politicalProfile ?? this.politicalProfile,
    );
  }

  /// Check if user has an active subscription
  bool get hasActiveSubscription {
    if (subscriptionEndDate == null) return false;
    return DateTime.now().isBefore(subscriptionEndDate!);
  }

  /// Get remaining days in subscription
  int get remainingSubscriptionDays {
    if (subscriptionEndDate == null) return 0;
    final now = DateTime.now();
    if (now.isAfter(subscriptionEndDate!)) return 0;
    return subscriptionEndDate!.difference(now).inDays;
  }

  /// Check if subscription is expiring soon (within 7 days)
  bool get isSubscriptionExpiringSoon {
    return hasActiveSubscription && remainingSubscriptionDays <= 7;
  }

  /// Get subscription status text
  String get subscriptionStatusText {
    if (!hasActiveSubscription) return 'No active subscription';
    if (isSubscriptionExpiringSoon) {
      return 'Expires in $remainingSubscriptionDays days';
    }
    return 'Active until ${subscriptionEndDate!.day}/${subscriptionEndDate!.month}/${subscriptionEndDate!.year}';
  }

  @override
  List<Object?> get props => [
        uid,
        name,
        email,
        phoneNumber,
        photoUrl,
        createdAt,
        updatedAt,
        isProfileComplete,
        languageCode,
        posterIds,
        userType,
        isPremium,
        subscriptionEndDate,
        subscriptionPlanId,
        subscriptionPurchaseDate,
        isAdmin,
        businessProfile,
        politicalProfile,
      ];
}
