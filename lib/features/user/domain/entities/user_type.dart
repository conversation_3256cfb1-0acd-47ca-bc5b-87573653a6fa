import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// Model class representing a user type in the application
class UserType extends Equatable {
  /// Unique identifier for the user type
  final String id;
  
  /// Display name for the user type
  final String name;
  
  /// Description of the user type
  final String? description;
  
  /// Icon name for the user type
  final String? iconName;

  const UserType({
    required this.id,
    required this.name,
    this.description,
    this.iconName,
  });

  /// Create a user type model from Firestore document
  factory UserType.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserType(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'],
      iconName: data['iconName'],
    );
  }

  /// Convert user type model to a map for Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'iconName': iconName,
    };
  }

  @override
  List<Object?> get props => [id, name, description, iconName];
}
