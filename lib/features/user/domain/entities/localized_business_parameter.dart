import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/services/firebase_localization_service.dart';
import 'business_parameter.dart';

/// Localized version of BusinessParameter that supports multiple languages
class LocalizedBusinessParameter extends BusinessParameter {
  LocalizedBusinessParameter._({
    required BusinessParameter parameter,
    required FirebaseLocalizationService localizationService,
    required String userLanguageCode,
    required Map<String, dynamic> rawData,
  })  :
        super(
          id: parameter.id,
          name: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'name',
            languageCode: userLanguageCode,
          ),
          description: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'description',
            languageCode: userLanguageCode,
          ),
          type: parameter.type,
          isRequired: parameter.isRequired,
          isActive: parameter.isActive,
          defaultValue: parameter.defaultValue,
          dropdownOptions: localizationService.getLocalizedList(
            data: rawData,
            fieldName: 'dropdownOptions',
            languageCode: userLanguageCode,
          ),
          displayOrder: parameter.displayOrder,
          createdAt: parameter.createdAt,
          updatedAt: parameter.updatedAt,
        );

  /// Create a localized business parameter from Firestore document
  static LocalizedBusinessParameter fromFirestore({
    required DocumentSnapshot doc,
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) {
    final data = doc.data() as Map<String, dynamic>;
    final originalParameter = BusinessParameter.fromFirestore(doc);
    final service = localizationService ?? FirebaseLocalizationService();

    return LocalizedBusinessParameter._(
      parameter: originalParameter,
      localizationService: service,
      userLanguageCode: userLanguageCode,
      rawData: data,
    );
  }

  /// Get localized dropdown option at specific index
  String getLocalizedDropdownOption(int index) {
    if (dropdownOptions == null || index >= dropdownOptions!.length) {
      return '';
    }
    return dropdownOptions![index];
  }

  /// Get all localized dropdown options
  List<String> getLocalizedDropdownOptions() {
    return dropdownOptions ?? [];
  }

  /// Check if parameter has localized content
  bool get hasLocalizedContent {
    try {
      // This would check if the Firebase document has translations
      return true; // Simplified for now
    } catch (e) {
      return false;
    }
  }
}

/// Repository extension for localized business parameters
extension LocalizedBusinessParameterRepository on dynamic {
  /// Get all localized business parameters
  static Future<List<LocalizedBusinessParameter>> getAllLocalizedParameters({
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) async {
    final service = localizationService ?? FirebaseLocalizationService();
    final firestore = FirebaseFirestore.instance;

    try {
      final querySnapshot = await firestore
          .collection('businessParameters')
          .orderBy('displayOrder')
          .get();

      return querySnapshot.docs
          .map((doc) => LocalizedBusinessParameter.fromFirestore(
                doc: doc,
                userLanguageCode: userLanguageCode,
                localizationService: service,
              ))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Get active localized business parameters
  static Future<List<LocalizedBusinessParameter>> getActiveLocalizedParameters({
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) async {
    final service = localizationService ?? FirebaseLocalizationService();
    final firestore = FirebaseFirestore.instance;

    try {
      final querySnapshot = await firestore
          .collection('businessParameters')
          .where('isActive', isEqualTo: true)
          .get();

      final parameters = querySnapshot.docs
          .map((doc) => LocalizedBusinessParameter.fromFirestore(
                doc: doc,
                userLanguageCode: userLanguageCode,
                localizationService: service,
              ))
          .toList();

      // Sort by display order
      parameters.sort((a, b) => a.displayOrder.compareTo(b.displayOrder));

      return parameters;
    } catch (e) {
      return [];
    }
  }
}
