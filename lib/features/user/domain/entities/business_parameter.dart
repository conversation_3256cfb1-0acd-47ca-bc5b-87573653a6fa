import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class representing a business parameter from Firestore
class BusinessParameter {
  /// Unique identifier for the parameter (document ID)
  final String id;
  
  /// Display name of the parameter
  final String name;
  
  /// Description of the parameter
  final String description;
  
  /// Type of parameter (string, number, boolean, dropdown, etc.)
  final String type;
  
  /// Whether the parameter is required
  final bool isRequired;
  
  /// Whether the parameter is active
  final bool isActive;
  
  /// Default value for the parameter
  final dynamic defaultValue;
  
  /// Options for dropdown parameters
  final List<String>? dropdownOptions;
  
  /// Order in which to display the parameter
  final int displayOrder;
  
  /// When the parameter was created
  final DateTime createdAt;
  
  /// When the parameter was last updated
  final DateTime updatedAt;

  const BusinessParameter({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.isRequired,
    required this.isActive,
    this.defaultValue,
    this.dropdownOptions,
    required this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create a business parameter from Firestore document
  factory BusinessParameter.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return BusinessParameter(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      type: data['type'] ?? 'string',
      isRequired: data['isRequired'] ?? false,
      isActive: data['isActive'] ?? true,
      defaultValue: data['defaultValue'],
      dropdownOptions: data['dropdownOptions'] != null 
          ? List<String>.from(data['dropdownOptions']) 
          : null,
      displayOrder: data['displayOrder'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  /// Convert to a map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'type': type,
      'isRequired': isRequired,
      'isActive': isActive,
      'defaultValue': defaultValue,
      'dropdownOptions': dropdownOptions,
      'displayOrder': displayOrder,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }
  
  /// Create a copy with updated fields
  BusinessParameter copyWith({
    String? name,
    String? description,
    String? type,
    bool? isRequired,
    bool? isActive,
    dynamic defaultValue,
    List<String>? dropdownOptions,
    int? displayOrder,
    DateTime? updatedAt,
  }) {
    return BusinessParameter(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      isRequired: isRequired ?? this.isRequired,
      isActive: isActive ?? this.isActive,
      defaultValue: defaultValue ?? this.defaultValue,
      dropdownOptions: dropdownOptions ?? this.dropdownOptions,
      displayOrder: displayOrder ?? this.displayOrder,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
