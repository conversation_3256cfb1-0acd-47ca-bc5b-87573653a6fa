import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/services/firebase_localization_service.dart';
import 'political_parameter.dart';

/// Localized version of PoliticalParameter that supports multiple languages
class LocalizedPoliticalParameter extends PoliticalParameter {
  LocalizedPoliticalParameter._({
    required PoliticalParameter parameter,
    required FirebaseLocalizationService localizationService,
    required String userLanguageCode,
    required Map<String, dynamic> rawData,
  })  :
        super(
          id: parameter.id,
          name: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'name',
            languageCode: userLanguageCode,
          ),
          description: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'description',
            languageCode: userLanguageCode,
          ),
          type: parameter.type,
          isRequired: parameter.isRequired,
          isActive: parameter.isActive,
          displayOrder: parameter.displayOrder,
          defaultValue: parameter.defaultValue,
          dropdownOptions: localizationService.getLocalizedList(
            data: rawData,
            fieldName: 'dropdownOptions',
            languageCode: userLanguageCode,
          ),
          collectionReference: parameter.collectionReference,
          displayField: parameter.displayField,
          valueField: parameter.valueField,
          createdAt: parameter.createdAt,
          updatedAt: parameter.updatedAt,
        );

  /// Create a localized political parameter from Firestore document
  static LocalizedPoliticalParameter fromFirestore({
    required DocumentSnapshot doc,
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) {
    final data = doc.data() as Map<String, dynamic>;
    final originalParameter = PoliticalParameter.fromFirestore(doc);
    final service = localizationService ?? FirebaseLocalizationService();

    return LocalizedPoliticalParameter._(
      parameter: originalParameter,
      localizationService: service,
      userLanguageCode: userLanguageCode,
      rawData: data,
    );
  }

  /// Get localized dropdown option at specific index
  String getLocalizedDropdownOption(int index) {
    if (dropdownOptions == null || index >= dropdownOptions!.length) {
      return '';
    }
    return dropdownOptions![index];
  }

  /// Get all localized dropdown options
  List<String> getLocalizedDropdownOptions() {
    return dropdownOptions ?? [];
  }
}

/// Repository extension for localized political parameters
extension LocalizedPoliticalParameterRepository on dynamic {
  /// Get all localized political parameters
  static Future<List<LocalizedPoliticalParameter>> getAllLocalizedParameters({
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) async {
    final service = localizationService ?? FirebaseLocalizationService();
    final firestore = FirebaseFirestore.instance;

    try {
      final querySnapshot = await firestore
          .collection('politicalParameters')
          .orderBy('displayOrder')
          .get();

      return querySnapshot.docs
          .map((doc) => LocalizedPoliticalParameter.fromFirestore(
                doc: doc,
                userLanguageCode: userLanguageCode,
                localizationService: service,
              ))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Get active localized political parameters
  static Future<List<LocalizedPoliticalParameter>> getActiveLocalizedParameters({
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) async {
    final service = localizationService ?? FirebaseLocalizationService();
    final firestore = FirebaseFirestore.instance;

    try {
      final querySnapshot = await firestore
          .collection('politicalParameters')
          .where('isActive', isEqualTo: true)
          .get();

      final parameters = querySnapshot.docs
          .map((doc) => LocalizedPoliticalParameter.fromFirestore(
                doc: doc,
                userLanguageCode: userLanguageCode,
                localizationService: service,
              ))
          .toList();

      // Sort by display order
      parameters.sort((a, b) => a.displayOrder.compareTo(b.displayOrder));

      return parameters;
    } catch (e) {
      return [];
    }
  }
}
