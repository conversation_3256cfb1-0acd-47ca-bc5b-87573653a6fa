/// Model class representing a business parameter value
class BusinessParameterValue {
  /// ID of the parameter this value is for
  final String parameterId;
  
  /// Value of the parameter
  final dynamic value;

  const BusinessParameterValue({
    required this.parameterId,
    required this.value,
  });

  /// Create from a map
  factory BusinessParameterValue.fromMap(String parameterId, dynamic value) {
    return BusinessParameterValue(
      parameterId: parameterId,
      value: value,
    );
  }

  /// Convert to a map
  Map<String, dynamic> toMap() {
    return {
      'parameterId': parameterId,
      'value': value,
    };
  }
}
