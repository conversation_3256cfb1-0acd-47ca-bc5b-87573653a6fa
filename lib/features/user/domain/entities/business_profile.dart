import 'package:cloud_firestore/cloud_firestore.dart';
import 'business_parameter_value.dart';

/// Business profile model for users who are businessmen
class BusinessProfile {
  final String? businessName;
  final String? businessType;
  final String? address;
  final String? mobileNumber;
  final String? website;
  final String? logoUrl;
  final Map<String, String>? socialMediaProfiles;

  /// Dynamic parameter values stored by parameter ID
  final Map<String, dynamic>? parameterValues;

  const BusinessProfile({
    this.businessName,
    this.businessType,
    this.address,
    this.mobileNumber,
    this.website,
    this.logoUrl,
    this.socialMediaProfiles,
    this.parameterValues,
  });

  /// Create a new instance with updated fields
  BusinessProfile copyWith({
    String? businessName,
    String? businessType,
    String? address,
    String? mobileNumber,
    String? website,
    String? logoUrl,
    Map<String, String>? socialMediaProfiles,
    Map<String, dynamic>? parameterValues,
  }) {
    return BusinessProfile(
      businessName: businessName ?? this.businessName,
      businessType: businessType ?? this.businessType,
      address: address ?? this.address,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      website: website ?? this.website,
      logoUrl: logoUrl ?? this.logoUrl,
      socialMediaProfiles: socialMediaProfiles ?? this.socialMediaProfiles,
      parameterValues: parameterValues ?? this.parameterValues,
    );
  }

  /// Convert to a map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'businessName': businessName,
      'businessType': businessType,
      'address': address,
      'mobileNumber': mobileNumber,
      'website': website,
      'logoUrl': logoUrl,
      'socialMediaProfiles': socialMediaProfiles,
      'parameterValues': parameterValues,
    };
  }

  /// Create from a Firestore document
  factory BusinessProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;

    if (data == null) {
      return const BusinessProfile();
    }

    return BusinessProfile(
      businessName: data['businessName'],
      businessType: data['businessType'],
      address: data['address'],
      mobileNumber: data['mobileNumber'],
      website: data['website'],
      logoUrl: data['logoUrl'],
      socialMediaProfiles: data['socialMediaProfiles'] != null
          ? Map<String, String>.from(data['socialMediaProfiles'])
          : null,
      parameterValues: data['parameterValues'] != null
          ? Map<String, dynamic>.from(data['parameterValues'])
          : null,
    );
  }

  /// Create from a map
  factory BusinessProfile.fromMap(Map<String, dynamic>? data) {
    if (data == null) {
      return const BusinessProfile();
    }

    return BusinessProfile(
      businessName: data['businessName'],
      businessType: data['businessType'],
      address: data['address'],
      mobileNumber: data['mobileNumber'],
      website: data['website'],
      logoUrl: data['logoUrl'],
      socialMediaProfiles: data['socialMediaProfiles'] != null
          ? Map<String, String>.from(data['socialMediaProfiles'])
          : null,
      parameterValues: data['parameterValues'] != null
          ? Map<String, dynamic>.from(data['parameterValues'])
          : null,
    );
  }

  /// Check if the business profile is empty
  bool get isEmpty =>
      businessName == null &&
      businessType == null &&
      address == null &&
      mobileNumber == null &&
      website == null &&
      logoUrl == null &&
      (socialMediaProfiles == null || socialMediaProfiles!.isEmpty) &&
      (parameterValues == null || parameterValues!.isEmpty);

  /// Check if the business profile is complete
  bool get isComplete =>
      businessName != null &&
      businessName!.isNotEmpty &&
      businessType != null &&
      businessType!.isNotEmpty;

  /// Get a parameter value by ID
  dynamic getParameterValue(String parameterId) {
    if (parameterValues == null) return null;
    return parameterValues![parameterId];
  }

  /// Create a new instance with an updated parameter value
  BusinessProfile withParameterValue(String parameterId, dynamic value) {
    final updatedValues = Map<String, dynamic>.from(parameterValues ?? {});
    updatedValues[parameterId] = value;

    return copyWith(parameterValues: updatedValues);
  }
}
