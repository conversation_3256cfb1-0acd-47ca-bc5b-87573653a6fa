import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class for political party
class Party {
  final String id;
  final String name;
  final String shortName;
  final String logo;
  final String primaryColor;
  final String secondaryColor;
  final int founded;
  final List<String> ideology;
  final String description;
  final String website;
  final List<PartyLeader> leadership;
  final bool isActive;
  final List<String> states;
  final ElectoralInfo? electoralInfo;
  final SocialMedia? socialMedia;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// Creates a new party
  const Party({
    required this.id,
    required this.name,
    required this.shortName,
    required this.logo,
    required this.primaryColor,
    required this.secondaryColor,
    required this.founded,
    required this.ideology,
    required this.description,
    required this.website,
    required this.leadership,
    required this.isActive,
    required this.states,
    this.electoralInfo,
    this.socialMedia,
    this.createdAt,
    this.updatedAt,
  });

  /// Creates a party from a Firestore document
  factory Party.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final id = doc.id;

    // Parse leadership data
    final leadershipData = data['leadership'] as List<dynamic>;
    final leadership = leadershipData
        .map((leader) => PartyLeader.fromMap(leader as Map<String, dynamic>))
        .toList();

    // Parse electoral info if available
    ElectoralInfo? electoralInfo;
    if (data['electoralInfo'] != null) {
      electoralInfo = ElectoralInfo.fromMap(data['electoralInfo'] as Map<String, dynamic>);
    }

    // Parse social media if available
    SocialMedia? socialMedia;
    if (data['socialMedia'] != null) {
      socialMedia = SocialMedia.fromMap(data['socialMedia'] as Map<String, dynamic>);
    }

    // Helper function to parse DateTime from various formats
    DateTime? parseDateTime(dynamic value) {
      if (value == null) {
        return null;
      }

      if (value is Timestamp) {
        return value.toDate();
      }

      if (value is String) {
        try {
          return DateTime.parse(value);
        } catch (e) {
          return null;
        }
      }

      return null;
    }

    // Handle potentially missing fields with default values
    return Party(
      id: id,
      name: data['name'] as String? ?? 'Unknown Party',
      shortName: data['shortName'] as String? ?? 'Unknown',
      logo: data['logo'] as String? ?? '',
      primaryColor: data['primaryColor'] as String? ?? '#0000FF',
      secondaryColor: data['secondaryColor'] as String? ?? '#FFFFFF',
      founded: data['founded'] as int? ?? 0,
      ideology: data['ideology'] != null
          ? List<String>.from(data['ideology'] as List)
          : <String>[],
      description: data['description'] as String? ?? '',
      website: data['website'] as String? ?? '',
      leadership: leadership,
      isActive: data['isActive'] as bool? ?? true,
      states: data['states'] != null
          ? List<String>.from(data['states'] as List)
          : <String>[],
      electoralInfo: electoralInfo,
      socialMedia: socialMedia,
      createdAt: parseDateTime(data['createdAt']),
      updatedAt: parseDateTime(data['updatedAt']),
    );
  }

  /// Creates a copy of this party with the given fields replaced
  Party copyWith({
    String? id,
    String? name,
    String? shortName,
    String? logo,
    String? primaryColor,
    String? secondaryColor,
    int? founded,
    List<String>? ideology,
    String? description,
    String? website,
    List<PartyLeader>? leadership,
    bool? isActive,
    List<String>? states,
    ElectoralInfo? electoralInfo,
    SocialMedia? socialMedia,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Party(
      id: id ?? this.id,
      name: name ?? this.name,
      shortName: shortName ?? this.shortName,
      logo: logo ?? this.logo,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      founded: founded ?? this.founded,
      ideology: ideology ?? this.ideology,
      description: description ?? this.description,
      website: website ?? this.website,
      leadership: leadership ?? this.leadership,
      isActive: isActive ?? this.isActive,
      states: states ?? this.states,
      electoralInfo: electoralInfo ?? this.electoralInfo,
      socialMedia: socialMedia ?? this.socialMedia,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Model class for party leader
class PartyLeader {
  final String name;
  final String position;
  final String image;

  /// Creates a new party leader
  const PartyLeader({
    required this.name,
    required this.position,
    required this.image,
  });

  /// Creates a party leader from a map
  factory PartyLeader.fromMap(Map<String, dynamic> map) {
    return PartyLeader(
      name: map['name'] as String? ?? 'Unknown Leader',
      position: map['position'] as String? ?? 'Member',
      image: map['image'] as String? ?? '',
    );
  }
}

/// Model class for electoral info
class ElectoralInfo {
  final int? currentSeats;
  final String? lastElectionPerformance;

  /// Creates a new electoral info
  const ElectoralInfo({
    this.currentSeats,
    this.lastElectionPerformance,
  });

  /// Creates electoral info from a map
  factory ElectoralInfo.fromMap(Map<String, dynamic> map) {
    return ElectoralInfo(
      currentSeats: map['currentSeats'] is int ? map['currentSeats'] as int : null,
      lastElectionPerformance: map['lastElectionPerformance'] is String ? map['lastElectionPerformance'] as String : null,
    );
  }
}

/// Model class for social media
class SocialMedia {
  final String? facebook;
  final String? twitter;
  final String? instagram;

  /// Creates a new social media
  const SocialMedia({
    this.facebook,
    this.twitter,
    this.instagram,
  });

  /// Creates social media from a map
  factory SocialMedia.fromMap(Map<String, dynamic> map) {
    return SocialMedia(
      facebook: map['facebook'] is String ? map['facebook'] as String : null,
      twitter: map['twitter'] is String ? map['twitter'] as String : null,
      instagram: map['instagram'] is String ? map['instagram'] as String : null,
    );
  }
}
