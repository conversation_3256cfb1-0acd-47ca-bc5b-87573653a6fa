import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/services/firebase_localization_service.dart';
import 'party.dart';

/// Localized version of Party that supports multiple languages
class LocalizedParty extends Party {
  LocalizedParty._({
    required Party party,
    required FirebaseLocalizationService localizationService,
    required String userLanguageCode,
    required Map<String, dynamic> rawData,
  })  :
        super(
          id: party.id,
          name: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'name',
            languageCode: userLanguageCode,
          ),
          shortName: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'shortName',
            languageCode: userLanguageCode,
          ),
          logo: party.logo,
          primaryColor: party.primaryColor,
          secondaryColor: party.secondaryColor,
          founded: party.founded,
          ideology: localizationService.getLocalizedList(
            data: rawData,
            fieldName: 'ideology',
            languageCode: userLanguageCode,
          ),
          description: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'description',
            languageCode: userLanguageCode,
          ),
          website: party.website,
          leadership: party.leadership.map((leader) => _localizePartyLeader(
            leader: leader,
            localizationService: localizationService,
            userLanguageCode: userLanguageCode,
            rawData: rawData,
          )).toList(),
          isActive: party.isActive,
          states: localizationService.getLocalizedList(
            data: rawData,
            fieldName: 'states',
            languageCode: userLanguageCode,
          ),
          electoralInfo: party.electoralInfo,
          socialMedia: party.socialMedia,
          createdAt: party.createdAt,
          updatedAt: party.updatedAt,
        );

  /// Create a localized party from Firestore document
  static LocalizedParty fromFirestore({
    required DocumentSnapshot doc,
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) {
    final data = doc.data() as Map<String, dynamic>;
    final originalParty = Party.fromFirestore(doc);
    final service = localizationService ?? FirebaseLocalizationService();

    return LocalizedParty._(
      party: originalParty,
      localizationService: service,
      userLanguageCode: userLanguageCode,
      rawData: data,
    );
  }

  /// Get localized ideology at specific index
  String getLocalizedIdeology(int index) {
    if (index >= ideology.length) {
      return '';
    }
    return ideology[index];
  }

  /// Get all localized ideologies
  List<String> getLocalizedIdeologies() {
    return ideology;
  }

  /// Get localized state at specific index
  String getLocalizedState(int index) {
    if (index >= states.length) {
      return '';
    }
    return states[index];
  }

  /// Get all localized states
  List<String> getLocalizedStates() {
    return states;
  }

  /// Helper method to localize party leader
  static PartyLeader _localizePartyLeader({
    required PartyLeader leader,
    required FirebaseLocalizationService localizationService,
    required String userLanguageCode,
    required Map<String, dynamic> rawData,
  }) {
    // For now, return the original leader since the PartyLeader class
    // doesn't have the expected fields for localization
    // In a real implementation, you would need to modify the Firebase
    // data structure to include leadership translations
    return leader;
  }
}

/// Repository extension for localized parties
extension LocalizedPartyRepository on dynamic {
  /// Get all localized parties
  static Future<List<LocalizedParty>> getAllLocalizedParties({
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) async {
    final service = localizationService ?? FirebaseLocalizationService();
    final firestore = FirebaseFirestore.instance;

    try {
      final querySnapshot = await firestore
          .collection('politicalParties')
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs
          .map((doc) => LocalizedParty.fromFirestore(
                doc: doc,
                userLanguageCode: userLanguageCode,
                localizationService: service,
              ))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Get localized party by ID
  static Future<LocalizedParty?> getLocalizedPartyById({
    required String partyId,
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) async {
    final service = localizationService ?? FirebaseLocalizationService();
    final firestore = FirebaseFirestore.instance;

    try {
      final doc = await firestore
          .collection('politicalParties')
          .doc(partyId)
          .get();

      if (doc.exists) {
        return LocalizedParty.fromFirestore(
          doc: doc,
          userLanguageCode: userLanguageCode,
          localizationService: service,
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
