import 'package:cloud_firestore/cloud_firestore.dart';

/// Political profile model for users who are politicians
/// Uses dynamic parameters from Firestore
class PoliticalProfile {
  final Map<String, dynamic>? parameterValues;

  /// Creates a new political profile
  const PoliticalProfile({
    this.parameterValues,
  });

  /// Creates a copy of this political profile with the given fields replaced
  PoliticalProfile copyWith({
    Map<String, dynamic>? parameterValues,
  }) {
    return PoliticalProfile(
      parameterValues: parameterValues ?? this.parameterValues,
    );
  }

  /// Creates a political profile from a Firestore document
  factory PoliticalProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;
    if (data == null) {
      return const PoliticalProfile();
    }

    return PoliticalProfile(
      parameterValues: data['parameterValues'] as Map<String, dynamic>?,
    );
  }

  /// Creates a political profile from a map
  factory PoliticalProfile.fromMap(Map<String, dynamic>? map) {
    if (map == null) {
      return const PoliticalProfile();
    }

    return PoliticalProfile(
      parameterValues: map['parameterValues'] as Map<String, dynamic>?,
    );
  }

  /// Converts this political profile to a map
  Map<String, dynamic> toMap() {
    return {
      'parameterValues': parameterValues,
    };
  }

  /// Converts this political profile to a Firestore document
  Map<String, dynamic> toFirestore() {
    return toMap();
  }

  /// Returns true if this political profile is empty
  bool get isEmpty => parameterValues == null || parameterValues!.isEmpty;

  /// Returns true if this political profile is not empty
  bool get isNotEmpty => !isEmpty;

  /// Returns true if this political profile has all required fields
  bool get isComplete {
    if (isEmpty) return false;

    // Check for required fields (party_name, position, constituency)
    final requiredFields = ['party_name', 'position', 'constituency'];
    for (final field in requiredFields) {
      if (!parameterValues!.containsKey(field) ||
          parameterValues![field] == null ||
          parameterValues![field].toString().isEmpty) {
        return false;
      }
    }

    return true;
  }

  @override
  String toString() {
    return 'PoliticalProfile(parameterValues: $parameterValues)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PoliticalProfile &&
           _mapEquals(other.parameterValues, parameterValues);
  }

  @override
  int get hashCode => parameterValues.hashCode;

  /// Helper method to compare maps
  bool _mapEquals(Map<String, dynamic>? map1, Map<String, dynamic>? map2) {
    if (map1 == null && map2 == null) return true;
    if (map1 == null || map2 == null) return false;
    if (map1.length != map2.length) return false;

    for (final key in map1.keys) {
      if (!map2.containsKey(key) || map1[key] != map2[key]) {
        return false;
      }
    }

    return true;
  }
}
