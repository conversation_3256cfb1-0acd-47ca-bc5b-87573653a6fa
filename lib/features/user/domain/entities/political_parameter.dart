import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class for political parameters
class PoliticalParameter {
  final String id;
  final String name;
  final String description;
  final String type;
  final bool isRequired;
  final bool isActive;
  final int displayOrder;
  final dynamic defaultValue;
  final List<String>? dropdownOptions;
  final String? collectionReference;
  final String? displayField;
  final String? valueField;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// Creates a new political parameter
  const PoliticalParameter({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.isRequired,
    required this.isActive,
    required this.displayOrder,
    this.defaultValue,
    this.dropdownOptions,
    this.collectionReference,
    this.displayField,
    this.valueField,
    this.createdAt,
    this.updatedAt,
  });

  /// Creates a political parameter from a Firestore document
  factory PoliticalParameter.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final id = doc.id;

    return PoliticalParameter(
      id: id,
      name: data['name'] as String,
      description: data['description'] as String,
      type: data['type'] as String,
      isRequired: data['isRequired'] as bool,
      isActive: data['isActive'] as bool,
      displayOrder: data['displayOrder'] as int,
      defaultValue: data['defaultValue'],
      dropdownOptions: data['dropdownOptions'] != null
          ? List<String>.from(data['dropdownOptions'] as List)
          : null,
      collectionReference: data['collectionReference'] as String?,
      displayField: data['displayField'] as String?,
      valueField: data['valueField'] as String?,
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : null,
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// Creates a copy of this political parameter with the given fields replaced
  PoliticalParameter copyWith({
    String? id,
    String? name,
    String? description,
    String? type,
    bool? isRequired,
    bool? isActive,
    int? displayOrder,
    dynamic defaultValue,
    List<String>? dropdownOptions,
    String? collectionReference,
    String? displayField,
    String? valueField,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PoliticalParameter(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      isRequired: isRequired ?? this.isRequired,
      isActive: isActive ?? this.isActive,
      displayOrder: displayOrder ?? this.displayOrder,
      defaultValue: defaultValue ?? this.defaultValue,
      dropdownOptions: dropdownOptions ?? this.dropdownOptions,
      collectionReference: collectionReference ?? this.collectionReference,
      displayField: displayField ?? this.displayField,
      valueField: valueField ?? this.valueField,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'PoliticalParameter(id: $id, name: $name, type: $type, isRequired: $isRequired, isActive: $isActive)';
  }
}
