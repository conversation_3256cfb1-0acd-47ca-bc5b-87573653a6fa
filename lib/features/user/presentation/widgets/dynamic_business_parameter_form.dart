import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../../../../features/ai_image_generation/domain/usecases/ai_logo_generation_service.dart';
import '../../domain/entities/business_parameter.dart';
import '../../domain/entities/localized_business_parameter.dart';
import '../../domain/entities/business_profile.dart';
import '../../domain/usecases/business_parameter_service.dart';
import '../../domain/usecases/user_service.dart';
import '../../../../core/providers/localization_provider.dart';

class DynamicBusinessParameterForm extends StatefulWidget {
  final BusinessProfile? initialProfile;
  final VoidCallback onProfileUpdated;

  const DynamicBusinessParameterForm({
    Key? key,
    this.initialProfile,
    required this.onProfileUpdated,
  }) : super(key: key);

  @override
  State<DynamicBusinessParameterForm> createState() => _DynamicBusinessParameterFormState();
}

class _DynamicBusinessParameterFormState extends State<DynamicBusinessParameterForm> {
  final _formKey = GlobalKey<FormState>();

  bool _isLoading = true;
  bool _isSaving = false;
  List<LocalizedBusinessParameter> _parameters = [];
  Map<String, dynamic> _parameterValues = {};
  Map<String, TextEditingController> _controllers = {};

  // Logo related variables
  LocalizedBusinessParameter? _logoParameter;
  File? _logoImageFile;
  String? _logoUrl;
  bool _isUploadingLogo = false;
  bool _isGeneratingLogo = false;

  @override
  void initState() {
    super.initState();
    _loadParameters();
  }

  @override
  void dispose() {
    // Dispose all controllers
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadParameters() async {
    setState(() {
      _isLoading = true;
    });

    try {
      AppLogger.info('Loading localized business parameters...');
      final parameterService = context.read<BusinessParameterService>();
      final userLanguageCode = context.userLanguageCode;
      final parameters = await parameterService.getActiveLocalizedParameters(
        userLanguageCode: userLanguageCode,
      );

      AppLogger.info('Loaded ${parameters.length} business parameters');

      // Sort parameters by display order
      parameters.sort((a, b) => a.displayOrder.compareTo(b.displayOrder));

      // Initialize controllers for each parameter
      final controllers = <String, TextEditingController>{};
      final values = <String, dynamic>{};

      // Find logo parameter if it exists
      LocalizedBusinessParameter? logoParameter;

      for (var parameter in parameters) {
        AppLogger.info('Processing parameter: ${parameter.id} - ${parameter.name} (${parameter.type})');

        // Check if this is a logo parameter
        bool isLogoParameter = parameter.name.toLowerCase().contains('logo') &&
                              parameter.type == 'string';

        if (isLogoParameter) {
          logoParameter = parameter;
          AppLogger.info('Found logo parameter: ${parameter.id} - ${parameter.name}');
        }

        // Get existing value from profile if available
        dynamic value;
        if (widget.initialProfile != null &&
            widget.initialProfile!.parameterValues != null &&
            widget.initialProfile!.parameterValues!.containsKey(parameter.id)) {
          value = widget.initialProfile!.parameterValues![parameter.id];
          AppLogger.info('Found existing value for ${parameter.name}: $value');

          // If this is the logo parameter, store the URL
          if (isLogoParameter && value != null) {
            _logoUrl = value.toString();
          }
        } else {
          value = parameter.defaultValue;
          AppLogger.info('Using default value for ${parameter.name}: $value');
        }

        // Create controller for text fields
        if (parameter.type == 'string' || parameter.type == 'number') {
          controllers[parameter.id] = TextEditingController(
            text: value?.toString() ?? '',
          );
        }

        // Store the value
        values[parameter.id] = value;
      }

      if (mounted) {
        setState(() {
          _parameters = parameters;
          _controllers = controllers;
          _parameterValues = values;
          _logoParameter = logoParameter;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('Failed to load business parameters', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.failedToLoadBusinessProfile)),
        );
      }
    }
  }

  Future<void> _pickLogoImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70,
      );

      if (pickedFile != null && mounted) {
        setState(() {
          _logoImageFile = File(pickedFile.path);
        });

        // Upload the logo immediately
        await _uploadLogoImage();
      }
    } catch (e) {
      AppLogger.error('Failed to pick logo image', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.failedToUploadTemplate)),
        );
      }
    }
  }

  Future<void> _uploadLogoImage() async {
    if (_logoImageFile == null || _logoParameter == null) return;

    setState(() {
      _isUploadingLogo = true;
    });

    try {
      final userService = context.read<UserService>();

      // Upload the logo image
      final logoUrl = await userService.uploadBusinessLogo(_logoImageFile!.path);

      if (logoUrl != null && mounted) {
        setState(() {
          _logoUrl = logoUrl;
          // Update the parameter value
          _parameterValues[_logoParameter!.id] = logoUrl;

          // Update the text controller if it exists
          if (_controllers.containsKey(_logoParameter!.id)) {
            _controllers[_logoParameter!.id]!.text = logoUrl;
          }
        });

        // Save the parameters to update the profile
        await _saveParameters(showSuccessMessage: false);
      }
    } catch (e) {
      AppLogger.error('Failed to upload logo image', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.failedToUploadTemplate)),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploadingLogo = false;
        });
      }
    }
  }

  /// Generate a logo using AI based on business information
  Future<void> _generateLogoWithAI() async {
    if (_logoParameter == null) return;

    // Get business information from the form
    String? businessName;
    String? businessCategory;
    String? businessLocation;

    // Find the business name parameter
    for (var parameter in _parameters) {
      if (parameter.name.toLowerCase().contains('name') ||
          parameter.id.toLowerCase().contains('name')) {
        businessName = _parameterValues[parameter.id] as String?;
      } else if (parameter.name.toLowerCase().contains('type') ||
                parameter.name.toLowerCase().contains('category') ||
                parameter.id.toLowerCase().contains('type') ||
                parameter.id.toLowerCase().contains('category')) {
        businessCategory = _parameterValues[parameter.id] as String?;
      } else if (parameter.name.toLowerCase().contains('address') ||
                parameter.name.toLowerCase().contains('location') ||
                parameter.id.toLowerCase().contains('address') ||
                parameter.id.toLowerCase().contains('location')) {
        businessLocation = _parameterValues[parameter.id] as String?;
      }
    }

    // If no business name is found, show an error
    if (businessName == null || businessName.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${AppLocalizations.of(context)!.businessName} ${AppLocalizations.of(context)!.isRequired}')),
        );
      }
      return;
    }

    setState(() {
      _isGeneratingLogo = true;
    });

    try {
      final logoGenerationService = context.read<AILogoGenerationService>();

      // Generate the logo
      final generatedImage = await logoGenerationService.generateBusinessLogo(
        businessName: businessName,
        businessCategory: businessCategory,
        businessLocation: businessLocation,
      );

      if (mounted) {
        setState(() {
          _isGeneratingLogo = false;
        });

        // Show the confirmation dialog with the generated image
        final bool? confirmed = await _showLogoConfirmationDialog(
          generatedImage.imageUrl,
          businessName,
        );

        // If the user confirmed, proceed with using the logo
        if (confirmed == true && mounted) {
          setState(() {
            _isGeneratingLogo = true;
          });

          // Download the image to a temporary file
          final tempFile = await _downloadImageToTempFile(generatedImage.imageUrl);

          if (tempFile != null && mounted) {
            setState(() {
              _logoImageFile = tempFile;
              _isGeneratingLogo = false;
              _isUploadingLogo = true;
            });

            // Upload the logo
            await _uploadLogoImage();
          } else {
            if (mounted) {
              setState(() {
                _isGeneratingLogo = false;
              });

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Failed to process the generated logo')),
              );
            }
          }
        }
      }
    } catch (e) {
      AppLogger.error('Failed to generate logo with AI', e);
      if (mounted) {
        setState(() {
          _isGeneratingLogo = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to generate logo: ${e.toString()}')),
        );
      }
    }
  }

  /// Show a confirmation dialog with the generated logo
  Future<bool?> _showLogoConfirmationDialog(String imageUrl, String businessName) async {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isPremium = themeProvider.isPremium;

    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: isPremium ? AppTheme.premiumBlack : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              width: 1,
            ),
          ),
          title: Text(
            AppLocalizations.of(context)!.businessLogo,
            style: TextStyle(
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Logo for $businessName',
                style: TextStyle(
                  color: isPremium ? Colors.white : Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: _buildImagePreview(imageUrl),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)!.selectBusinessLogo,
                style: TextStyle(
                  color: isPremium ? Colors.white : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                AppLocalizations.of(context)!.cancel,
                style: TextStyle(
                  color: isPremium ? Colors.white70 : Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                foregroundColor: isPremium ? Colors.black : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(AppLocalizations.of(context)!.selectBusinessLogo),
            ),
          ],
        );
      },
    );
  }

  /// Build an image preview widget based on the image URL format
  Widget _buildImagePreview(String imageUrl) {
    if (imageUrl.startsWith('data:image')) {
      // This is a base64 encoded image
      try {
        // Extract the base64 data from the data URL
        final String base64Data = imageUrl.split(',')[1];

        // Decode the base64 data to bytes
        final Uint8List bytes = base64Decode(base64Data);

        return Image.memory(
          bytes,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) => const Icon(
            Icons.error,
            color: Colors.red,
            size: 48,
          ),
        );
      } catch (e) {
        AppLogger.error('Failed to display base64 image', e);
        return const Icon(
          Icons.error,
          color: Colors.red,
          size: 48,
        );
      }
    } else {
      // This is a URL
      return Image.network(
        imageUrl,
        fit: BoxFit.contain,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                  : null,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) => const Icon(
          Icons.error,
          color: Colors.red,
          size: 48,
        ),
      );
    }
  }

  /// Download an image from a URL or base64 data to a temporary file
  Future<File?> _downloadImageToTempFile(String imageUrl) async {
    try {
      if (imageUrl.startsWith('data:image')) {
        // This is a base64 encoded image
        // Extract the base64 data and convert it to a file
        try {
          // Extract the base64 data from the data URL
          final String base64Data = imageUrl.split(',')[1];

          // Decode the base64 data to bytes
          final Uint8List bytes = base64Decode(base64Data);

          // Get the temporary directory
          final Directory tempDir = await getTemporaryDirectory();

          // Create a temporary file
          final File tempFile = File('${tempDir.path}/temp_logo_${DateTime.now().millisecondsSinceEpoch}.png');

          // Write the bytes to the file
          await tempFile.writeAsBytes(bytes);

          return tempFile;
        } catch (e) {
          AppLogger.error('Failed to convert base64 image to file', e);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to process image: ${e.toString()}')),
            );
          }
          return null;
        }
      } else {
        // This is a URL
        // We need to download it
        // For simplicity, we'll use a placeholder approach for now
        // In a real app, you would use http.get to download the image

        // Show a message to the user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('URL-based images are not supported in this demo')),
          );
        }
        return null;
      }
    } catch (e) {
      AppLogger.error('Failed to download image', e);
    }

    return null;
  }

  Future<void> _saveParameters({bool showSuccessMessage = true}) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final userService = context.read<UserService>();

      // Get current business profile or create a new one
      final currentProfile = widget.initialProfile ?? const BusinessProfile();

      // Update the profile with the parameter values
      final updatedProfile = currentProfile.copyWith(
        parameterValues: _parameterValues,
      );

      // Get current user
      final user = await userService.getCurrentUser();
      if (user == null) {
        throw Exception('User not found');
      }

      // Update the user with the new business profile
      final updatedUser = user.copyWith(
        businessProfile: updatedProfile,
        updatedAt: DateTime.now(),
      );

      // Save the updated user
      await userService.updateUser(updatedUser);

      if (mounted) {
        if (showSuccessMessage) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Business parameters saved successfully')),
          );
        }
        widget.onProfileUpdated();
        Navigator.pop(context);
      }

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save business parameters: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_parameters.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Text(
              'Business Parameters',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
            ),
          ),

          // No parameters message
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 48,
                    color: isPremium ? AppTheme.premiumGold.withAlpha(150) : Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.noBusinessParametersAvailable,
                    style: TextStyle(
                      fontSize: 16,
                      color: isPremium ? AppTheme.premiumGold : null,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    AppLocalizations.of(context)!.businessParametersConfiguredByAdmin,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: isPremium ? Colors.white70 : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    }

    // Filter out the logo parameter from the regular parameters
    final regularParameters = _logoParameter != null
        ? _parameters.where((p) => p.id != _logoParameter!.id).toList()
        : _parameters;

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          // Padding(
          //   padding: const EdgeInsets.symmetric(vertical: 16.0),
          //   child: Text(
          //     'Business Parameters',
          //     style: TextStyle(
          //       fontSize: 18,
          //       fontWeight: FontWeight.bold,
          //       color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
          //     ),
          //   ),
          // ),

          // Logo section if logo parameter exists
          if (_logoParameter != null) _buildLogoSection(isPremium),

          // Parameter fields (excluding logo)
          ...regularParameters.map((parameter) => _buildParameterField(parameter, isPremium)),

          const SizedBox(height: 32),

          // Save button
          Center(
            child: GradientButton(
              text: AppLocalizations.of(context)!.saveBusinessParameters,
              onPressed: () => _saveParameters(),
              isLoading: _isSaving,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoSection(bool isPremium) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Logo title
          SizedBox(width: MediaQuery.of(context).size.width * 0.9,
            child: Text(
              AppLocalizations.of(context)!.businessLogo,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Logo image or placeholder
          GestureDetector(
            onTap: _pickLogoImage,
            child: Stack(
              alignment: Alignment.bottomRight,
              children: [
                // Logo container
                Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: isPremium ? AppTheme.premiumDarkGrey : Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey[300]!,
                      width: 1,
                    ),
                    image: _logoImageFile != null
                        ? DecorationImage(
                            image: FileImage(_logoImageFile!),
                            fit: BoxFit.cover,
                          )
                        : _logoUrl != null
                            ? DecorationImage(
                                image: NetworkImage(_logoUrl!),
                                fit: BoxFit.cover,
                              )
                            : null,
                  ),
                  child: _logoImageFile == null && _logoUrl == null
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.business,
                                size: 48,
                                color: isPremium ? AppTheme.premiumGold.withAlpha(150) : Colors.grey[400],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                AppLocalizations.of(context)!.selectBusinessLogo,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: isPremium ? Colors.white70 : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        )
                      : null,
                ),

                // Camera icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isPremium ? AppTheme.premiumBlack : AppTheme.primaryBlue,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isPremium ? AppTheme.premiumGold : Colors.white,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.camera_alt,
                    color: isPremium ? AppTheme.premiumGold : Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          // Loading indicator
          if (_isUploadingLogo || _isGeneratingLogo)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isGeneratingLogo ? AppLocalizations.of(context)!.generatingPreview : AppLocalizations.of(context)!.uploading,
                    style: TextStyle(
                      fontSize: 14,
                      color: isPremium ? Colors.white70 : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

          // AI Logo Generation Button
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: ElevatedButton.icon(
              onPressed: _isGeneratingLogo || _isUploadingLogo ? null : _generateLogoWithAI,
              icon: Icon(
                Icons.auto_awesome,
                color: isPremium ? AppTheme.premiumBlack : Colors.white,
              ),
              label: Text(
                AppLocalizations.of(context)!.businessLogo,
                style: TextStyle(
                  color: isPremium ? AppTheme.premiumBlack : Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),
          const Divider(),
        ],
      ),
    );
  }

  Widget _buildParameterField(BusinessParameter parameter, bool isPremium) {
    switch (parameter.type) {
      case 'string':
        return _buildTextField(parameter, isPremium);
      case 'number':
        return _buildNumberField(parameter, isPremium);
      case 'dropdown':
        return _buildDropdownField(parameter, isPremium);
      case 'boolean':
        return _buildBooleanField(parameter, isPremium);
      default:
        return _buildTextField(parameter, isPremium);
    }
  }

  Widget _buildTextField(BusinessParameter parameter, bool isPremium) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: TextFormField(
        controller: _controllers[parameter.id],
        decoration: InputDecoration(
          labelText: parameter.name,
          hintText: parameter.description,
          prefixIcon: const Icon(Icons.description),
        ),
        validator: parameter.isRequired
            ? (value) {
                if (value == null || value.isEmpty) {
                  return '${parameter.name} ${AppLocalizations.of(context)!.isRequired}';
                }
                return null;
              }
            : null,
        onChanged: (value) {
          setState(() {
            _parameterValues[parameter.id] = value;
          });
        },
      ),
    );
  }

  Widget _buildNumberField(BusinessParameter parameter, bool isPremium) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: TextFormField(
        controller: _controllers[parameter.id],
        decoration: InputDecoration(
          labelText: parameter.name,
          hintText: parameter.description,
          prefixIcon: const Icon(Icons.numbers),
        ),
        keyboardType: TextInputType.number,
        validator: parameter.isRequired
            ? (value) {
                if (value == null || value.isEmpty) {
                  return '${parameter.name} ${AppLocalizations.of(context)!.isRequired}';
                }
                if (double.tryParse(value) == null) {
                  return AppLocalizations.of(context)!.pleaseEnterValidNumber;
                }
                return null;
              }
            : (value) {
                if (value != null && value.isNotEmpty && double.tryParse(value) == null) {
                  return AppLocalizations.of(context)!.pleaseEnterValidNumber;
                }
                return null;
              },
        onChanged: (value) {
          setState(() {
            if (value.isEmpty) {
              _parameterValues[parameter.id] = null;
            } else {
              _parameterValues[parameter.id] = double.tryParse(value);
            }
          });
        },
      ),
    );
  }

  Widget _buildDropdownField(BusinessParameter parameter, bool isPremium) {
    final options = parameter.dropdownOptions ?? [];

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            parameter.name,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isPremium ? AppTheme.premiumGold : Colors.grey[700],
            ),
          ),
          // const SizedBox(height: 4),
          // Text(
          //   parameter.description,
          //   style: TextStyle(
          //     fontSize: 12,
          //     color: isPremium ? Colors.grey[400] : Colors.grey[600],
          //   ),
          // ),
          const SizedBox(height: 8),
          Container(
            // padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: isPremium ? AppTheme.premiumDarkGrey : Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isPremium ? AppTheme.premiumGold.withAlpha(77) : Colors.grey[300]!,
                width: 1,
              ),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButtonFormField<String>(
                value: _parameterValues[parameter.id] as String?,
                isExpanded: true,
                hint: Text(
                  '${AppLocalizations.of(context)!.selectUserType} ${parameter.name}',
                  style: TextStyle(
                    color: isPremium ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                icon: Icon(
                  Icons.arrow_drop_down,
                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                ),
                dropdownColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                items: options.map((String option) {
                  return DropdownMenuItem<String>(
                    value: option,
                    child: Text(
                      option,
                      style: TextStyle(
                        color: isPremium ? AppTheme.premiumGold : null,
                      ),
                    ),
                  );
                }).toList(),
                validator: parameter.isRequired
                    ? (value) {
                        if (value == null || value.isEmpty) {
                          return '${parameter.name} ${AppLocalizations.of(context)!.isRequired}';
                        }
                        return null;
                      }
                    : null,
                onChanged: (String? newValue) {
                  setState(() {
                    _parameterValues[parameter.id] = newValue;
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBooleanField(BusinessParameter parameter, bool isPremium) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  parameter.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isPremium ? AppTheme.premiumGold : Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  parameter.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: isPremium ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _parameterValues[parameter.id] as bool? ?? false,
            onChanged: (bool value) {
              setState(() {
                _parameterValues[parameter.id] = value;
              });
            },
            activeColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            activeTrackColor: isPremium ? AppTheme.premiumGold.withAlpha(77) : null,
            inactiveThumbColor: isPremium ? AppTheme.premiumDarkGrey : null,
            inactiveTrackColor: isPremium ? AppTheme.premiumLightGrey.withAlpha(77) : null,
          ),
        ],
      ),
    );
  }
}
