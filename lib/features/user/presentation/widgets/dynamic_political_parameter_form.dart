import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../../domain/entities/party.dart';
import '../../domain/entities/political_parameter.dart';
import '../../domain/entities/localized_political_parameter.dart';
import '../../domain/entities/political_profile.dart';
import '../../../../core/providers/localization_provider.dart';
import '../../domain/usecases/party_service.dart';
import '../../domain/usecases/political_parameter_service.dart';
import '../../domain/usecases/user_service.dart';
import '../pages/party_selection_page.dart';

class DynamicPoliticalParameterForm extends StatefulWidget {
  final PoliticalProfile? initialProfile;
  final VoidCallback onProfileUpdated;

  const DynamicPoliticalParameterForm({
    super.key,
    this.initialProfile,
    required this.onProfileUpdated,
  });

  @override
  State<DynamicPoliticalParameterForm> createState() => _DynamicPoliticalParameterFormState();
}

class _DynamicPoliticalParameterFormState extends State<DynamicPoliticalParameterForm> {
  final _formKey = GlobalKey<FormState>();

  bool _isLoading = true;
  bool _isSaving = false;
  List<LocalizedPoliticalParameter> _parameters = [];
  Map<String, dynamic> _parameterValues = {};
  Map<String, TextEditingController> _controllers = {};

  // Photo related variables
  LocalizedPoliticalParameter? _photoParameter;
  File? _photoImageFile;
  String? _photoUrl;
  bool _isUploadingPhoto = false;

  // Party related variables
  Map<String, String> _partyCache = {};
  bool _formSubmitted = false;

  @override
  void initState() {
    super.initState();
    _loadParameters();
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadParameters() async {
    setState(() {
      _isLoading = true;
    });

    try {
      AppLogger.info('Loading localized political parameters...');
      final parameterService = context.read<PoliticalParameterService>();
      final userLanguageCode = context.userLanguageCode;
      final parameters = await parameterService.getActiveLocalizedParameters(
        userLanguageCode: userLanguageCode,
      );

      AppLogger.info('Loaded ${parameters.length} political parameters');

      // Sort parameters by display order
      parameters.sort((a, b) => a.displayOrder.compareTo(b.displayOrder));

      // Initialize controllers for each parameter
      final controllers = <String, TextEditingController>{};
      final values = <String, dynamic>{};

      // Find photo parameter if it exists
      LocalizedPoliticalParameter? photoParameter;

      // Find party parameter if it exists
      LocalizedPoliticalParameter? partyParameter;

      for (var parameter in parameters) {
        AppLogger.info('Processing parameter: ${parameter.id} - ${parameter.name} (${parameter.type})');

        // Check if this is a photo parameter
        bool isPhotoParameter = parameter.id == 'political_photo' &&
                              parameter.type == 'string';

        // Check if this is a party parameter
        bool isPartyParameter = parameter.type == 'party_reference';

        if (isPhotoParameter) {
          photoParameter = parameter;
          AppLogger.info('Found photo parameter: ${parameter.id} - ${parameter.name}');
        }

        if (isPartyParameter) {
          partyParameter = parameter;
          AppLogger.info('Found party parameter: ${parameter.id} - ${parameter.name}');
        }

        // Get existing value from profile if available
        dynamic value;
        if (widget.initialProfile != null &&
            widget.initialProfile!.parameterValues != null &&
            widget.initialProfile!.parameterValues!.containsKey(parameter.id)) {
          value = widget.initialProfile!.parameterValues![parameter.id];
          AppLogger.info('Found existing value for ${parameter.name}: $value');

          // If this is the photo parameter, store the URL
          if (isPhotoParameter && value != null) {
            _photoUrl = value.toString();
          }
        } else {
          value = parameter.defaultValue;
          AppLogger.info('Using default value for ${parameter.name}: $value');
        }

        // Create controller for text fields
        if (parameter.type == 'string' || parameter.type == 'number') {
          controllers[parameter.id] = TextEditingController(
            text: value?.toString() ?? '',
          );
        }

        // Store the value
        values[parameter.id] = value;
      }

      // Load party names for selected parties
      if (partyParameter != null) {
        final partyId = values[partyParameter.id] as String?;
        if (partyId != null) {
          await _loadPartyName(partyId);
        }
      }

      if (mounted) {
        setState(() {
          _parameters = parameters;
          _controllers = controllers;
          _parameterValues = values;
          _photoParameter = photoParameter;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('Failed to load political parameters', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.failedToLoadPoliticalProfile)),
        );
      }
    }
  }

  Future<void> _loadPartyName(String partyId) async {
    try {
      // Skip if we already have this party in the cache
      if (_partyCache.containsKey(partyId)) {
        return;
      }

      final partyService = context.read<PartyService>();
      final party = await partyService.getPartyById(partyId);

      if (party != null && mounted) {
        setState(() {
          _partyCache[partyId] = party.shortName;
        });
        AppLogger.info('Loaded party name for $partyId: ${party.shortName}');
      }
    } catch (e) {
      AppLogger.error('Failed to load party name for $partyId', e);
    }
  }

  Future<void> _pickPhotoImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70,
      );

      if (pickedFile != null && mounted) {
        setState(() {
          _photoImageFile = File(pickedFile.path);
        });

        // Upload the photo immediately
        await _uploadPhotoImage();
      }
    } catch (e) {
      AppLogger.error('Failed to pick photo image', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.failedToUploadTemplate)),
        );
      }
    }
  }

  Future<void> _uploadPhotoImage() async {
    if (_photoImageFile == null || _photoParameter == null) return;

    setState(() {
      _isUploadingPhoto = true;
    });

    try {
      final userService = context.read<UserService>();

      // Upload the photo image
      final photoUrl = await userService.uploadPoliticalPhoto(_photoImageFile!.path);

      if (photoUrl != null && mounted) {
        setState(() {
          _photoUrl = photoUrl;
          // Update the parameter value
          _parameterValues[_photoParameter!.id] = photoUrl;

          // Update the text controller if it exists
          if (_controllers.containsKey(_photoParameter!.id)) {
            _controllers[_photoParameter!.id]!.text = photoUrl;
          }
        });

        // Save the parameters to update the profile
        await _saveParameters(showSuccessMessage: false);
      }
    } catch (e) {
      AppLogger.error('Failed to upload photo image', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.failedToUploadTemplate)),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploadingPhoto = false;
        });
      }
    }
  }

  Future<void> _saveParameters({bool showSuccessMessage = true}) async {
    setState(() {
      _formSubmitted = true;
    });

    if (!_formKey.currentState!.validate()) {
      // Check for required party parameters that aren't validated by the form
      bool hasError = false;
      for (var parameter in _parameters) {
        if (parameter.isRequired && parameter.type == 'party_reference') {
          if (_parameterValues[parameter.id] == null) {
            hasError = true;
            break;
          }
        }
      }

      if (hasError) {
        return;
      }
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final userService = context.read<UserService>();

      // Update the user's political profile with the parameter values
      await userService.updatePoliticalProfile(
        parameterValues: _parameterValues,
      );

      if (mounted && showSuccessMessage) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.politicalParametersSavedSuccessfully)),
        );
        widget.onProfileUpdated();
      } else if (mounted) {
        widget.onProfileUpdated();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.failedToSavePoliticalParameters)),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_parameters.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Text(
              AppLocalizations.of(context)!.politicalParameters,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
            ),
          ),

          // No parameters message
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 48,
                    color: isPremium ? AppTheme.premiumGold.withAlpha(150) : Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.noPoliticalParametersAvailable,
                    style: TextStyle(
                      fontSize: 16,
                      color: isPremium ? AppTheme.premiumGold : null,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    AppLocalizations.of(context)!.politicalParametersConfiguredByAdmin,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: isPremium ? Colors.white70 : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    }

    // Filter out the photo parameter from the regular parameters
    final regularParameters = _photoParameter != null
        ? _parameters.where((p) => p.id != _photoParameter!.id).toList()
        : _parameters;

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          // Padding(
          //   padding: const EdgeInsets.symmetric(vertical: 16.0),
          //   child: Text(
          //     'Political Parameters',
          //     style: TextStyle(
          //       fontSize: 18,
          //       fontWeight: FontWeight.bold,
          //       color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
          //     ),
          //   ),
          // ),

          // Photo section if photo parameter exists
          if (_photoParameter != null) _buildPhotoSection(isPremium),

          // Parameter fields (excluding photo)
          ...regularParameters.map((parameter) => _buildParameterField(parameter, isPremium)),

          const SizedBox(height: 32),

          // Save button
          Center(
            child: GradientButton(
              text: AppLocalizations.of(context)!.savePoliticalParameters,
              onPressed: () => _saveParameters(),
              isLoading: _isSaving,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoSection(bool isPremium) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Photo title
          SizedBox(width: MediaQuery.of(context).size.width * 0.9,
            child: Text(
              AppLocalizations.of(context)!.politicalPhoto,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Photo image or placeholder
          GestureDetector(
            onTap: _pickPhotoImage,
            child: Stack(
              alignment: Alignment.bottomRight,
              children: [
                // Photo container
                Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    color: isPremium ? AppTheme.premiumDarkGrey : Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey[300]!,
                      width: 1,
                    ),
                    image: _photoImageFile != null
                        ? DecorationImage(
                            image: FileImage(_photoImageFile!),
                            fit: BoxFit.cover,
                          )
                        : _photoUrl != null
                            ? DecorationImage(
                                image: NetworkImage(_photoUrl!),
                                fit: BoxFit.cover,
                              )
                            : null,
                  ),
                  child: _photoImageFile == null && _photoUrl == null
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person,
                                size: 48,
                                color: isPremium ? AppTheme.premiumGold.withAlpha(150) : Colors.grey[400],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                AppLocalizations.of(context)!.selectPoliticalPhoto,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: isPremium ? Colors.white70 : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        )
                      : null,
                ),

                // Camera icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isPremium ? AppTheme.premiumBlack : AppTheme.primaryBlue,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isPremium ? AppTheme.premiumGold : Colors.white,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.camera_alt,
                    color: isPremium ? AppTheme.premiumGold : Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          // Loading indicator
          if (_isUploadingPhoto)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Uploading photo...',
                    style: TextStyle(
                      fontSize: 14,
                      color: isPremium ? Colors.white70 : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 16),
          const Divider(),
        ],
      ),
    );
  }

  Widget _buildParameterField(PoliticalParameter parameter, bool isPremium) {
    switch (parameter.type) {
      case 'string':
        return _buildTextField(parameter, isPremium);
      case 'number':
        return _buildNumberField(parameter, isPremium);
      case 'dropdown':
        return _buildDropdownField(parameter, isPremium);
      case 'boolean':
        return _buildBooleanField(parameter, isPremium);
      case 'party_reference':
        return _buildPartyReferenceField(parameter, isPremium);
      default:
        return _buildTextField(parameter, isPremium);
    }
  }

  Widget _buildTextField(PoliticalParameter parameter, bool isPremium) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: TextFormField(
        controller: _controllers[parameter.id],
        decoration: InputDecoration(
          labelText: parameter.name,
          hintText: parameter.description,
          labelStyle: TextStyle(
            color: isPremium ? AppTheme.premiumGold : null,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey.shade300,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
          filled: true,
          fillColor: isPremium ? AppTheme.premiumDarkGrey.withAlpha(100) : Colors.white,
        ),
        style: TextStyle(
          color: isPremium ? Colors.white : null,
        ),
        validator: parameter.isRequired
            ? (value) {
                if (value == null || value.isEmpty) {
                  return '${parameter.name} ${AppLocalizations.of(context)!.isRequired}';
                }
                return null;
              }
            : null,
        onChanged: (value) {
          setState(() {
            _parameterValues[parameter.id] = value;
          });
        },
      ),
    );
  }

  Widget _buildNumberField(PoliticalParameter parameter, bool isPremium) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: TextFormField(
        controller: _controllers[parameter.id],
        decoration: InputDecoration(
          labelText: parameter.name,
          hintText: parameter.description,
          labelStyle: TextStyle(
            color: isPremium ? AppTheme.premiumGold : null,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey.shade300,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
          filled: true,
          fillColor: isPremium ? AppTheme.premiumDarkGrey.withAlpha(100) : Colors.white,
        ),
        style: TextStyle(
          color: isPremium ? Colors.white : null,
        ),
        keyboardType: TextInputType.number,
        validator: parameter.isRequired
            ? (value) {
                if (value == null || value.isEmpty) {
                  return '${parameter.name} ${AppLocalizations.of(context)!.isRequired}';
                }
                if (double.tryParse(value) == null) {
                  return AppLocalizations.of(context)!.pleaseEnterValidNumber;
                }
                return null;
              }
            : null,
        onChanged: (value) {
          setState(() {
            if (value.isEmpty) {
              _parameterValues[parameter.id] = null;
            } else {
              _parameterValues[parameter.id] = double.tryParse(value);
            }
          });
        },
      ),
    );
  }

  Widget _buildDropdownField(PoliticalParameter parameter, bool isPremium) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            parameter.name,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isPremium ? AppTheme.premiumGold : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Container(
            // padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: isPremium ? AppTheme.premiumDarkGrey.withAlpha(100) : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey.shade300,
              ),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButtonFormField<String>(
                value: _parameterValues[parameter.id] as String?,
                isExpanded: true,
                hint: Text(
                  '${AppLocalizations.of(context)!.selectUserType} ${parameter.name}',
                  style: TextStyle(
                    color: isPremium ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                icon: Icon(
                  Icons.arrow_drop_down,
                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                ),
                dropdownColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                items: parameter.dropdownOptions?.map((option) {
                  return DropdownMenuItem<String>(
                    value: option,
                    child: Text(
                      option,
                      style: TextStyle(
                        color: isPremium ? AppTheme.premiumGold : null,
                      ),
                    ),
                  );
                }).toList(),
                validator: parameter.isRequired
                    ? (value) {
                        if (value == null || value.isEmpty) {
                          return '${parameter.name} ${AppLocalizations.of(context)!.isRequired}';
                        }
                        return null;
                      }
                    : null,
                onChanged: (String? newValue) {
                  setState(() {
                    _parameterValues[parameter.id] = newValue;
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBooleanField(PoliticalParameter parameter, bool isPremium) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        children: [
          Expanded(
            child: Text(
              parameter.name,
              style: TextStyle(
                fontSize: 16,
                color: isPremium ? AppTheme.premiumGold : null,
              ),
            ),
          ),
          Switch(
            value: _parameterValues[parameter.id] as bool? ?? false,
            onChanged: (bool value) {
              setState(() {
                _parameterValues[parameter.id] = value;
              });
            },
            activeColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
          ),
        ],
      ),
    );
  }

  Widget _buildPartyReferenceField(PoliticalParameter parameter, bool isPremium) {
    // Get the selected party name to display
    String? selectedPartyId = _parameterValues[parameter.id] as String?;
    String displayText = '${AppLocalizations.of(context)!.selectUserType} ${parameter.name}';

    // If we have a selected party, try to get its name from the cache
    if (selectedPartyId != null && _partyCache.containsKey(selectedPartyId)) {
      displayText = _partyCache[selectedPartyId]!;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            parameter.name,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isPremium ? AppTheme.premiumGold : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: () => _showPartySelectionPage(parameter),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isPremium ? AppTheme.premiumDarkGrey.withAlpha(100) : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey.shade300,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      displayText,
                      style: TextStyle(
                        color: selectedPartyId != null
                            ? (isPremium ? Colors.white : Colors.black)
                            : (isPremium ? Colors.grey[400] : Colors.grey[600]),
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_drop_down,
                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  ),
                ],
              ),
            ),
          ),
          if (parameter.isRequired && _formSubmitted && selectedPartyId == null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0, left: 12.0),
              child: Text(
                '${parameter.name} ${AppLocalizations.of(context)!.isRequired}',
                style: TextStyle(
                  color: Colors.red[700],
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // This section has been moved to the class variables

  Future<void> _showPartySelectionPage(PoliticalParameter parameter) async {
    // Get the selected party ID
    final selectedPartyId = _parameterValues[parameter.id] as String?;

    AppLogger.info('Showing party selection page with selected party ID: $selectedPartyId');
    AppLogger.info('Current party cache: $_partyCache');

    // Navigate to the party selection page
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PartySelectionPage(
          selectedPartyId: selectedPartyId,
          onPartySelected: (party) {
            AppLogger.info('Party selected: ${party.id} - ${party.name}');

            setState(() {
              // Update the parameter value with the selected party ID
              _parameterValues[parameter.id] = party.id;

              // Cache the party name for display
              _partyCache[party.id] = party.name;

              AppLogger.info('Updated parameter value: ${_parameterValues[parameter.id]}');
              AppLogger.info('Updated party cache: $_partyCache');
            });
          },
        ),
      ),
    );

    // Force a rebuild to ensure the UI updates
    if (mounted) {
      setState(() {});
    }
  }
}
