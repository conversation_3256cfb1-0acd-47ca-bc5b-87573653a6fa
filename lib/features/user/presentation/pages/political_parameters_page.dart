import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../../domain/entities/political_profile.dart';
import '../../domain/usecases/user_service.dart';
import '../widgets/dynamic_political_parameter_form.dart';

class PoliticalParametersPage extends StatefulWidget {
  final PoliticalProfile? initialProfile;

  const PoliticalParametersPage({
    super.key,
    this.initialProfile,
  });

  @override
  State<PoliticalParametersPage> createState() => _PoliticalParametersPageState();
}

class _PoliticalParametersPageState extends State<PoliticalParametersPage> {
  PoliticalProfile? _politicalProfile;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _politicalProfile = widget.initialProfile;
    if (_politicalProfile == null) {
      _loadPoliticalProfile();
    }
  }

  Future<void> _loadPoliticalProfile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userService = context.read<UserService>();
      final profile = await userService.getPoliticalProfile();

      if (mounted) {
        setState(() {
          _politicalProfile = profile;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load political profile: $e')),
        );
      }
    }
  }

  Future<void> _refreshProfile() async {
    await _loadPoliticalProfile();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: localizations.politicalParameters,
      ),
      body: Container(
        decoration: themeProvider.isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: DynamicPoliticalParameterForm(
                  initialProfile: _politicalProfile,
                  onProfileUpdated: _refreshProfile,
                ),
              ),
      ),
    );
  }
}
