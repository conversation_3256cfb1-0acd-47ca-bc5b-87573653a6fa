import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/party.dart';
import '../../domain/usecases/party_service.dart';

class PartySelectionPage extends StatefulWidget {
  final String? selectedPartyId;
  final Function(Party) onPartySelected;
  
  const PartySelectionPage({
    super.key,
    this.selectedPartyId,
    required this.onPartySelected,
  });

  @override
  State<PartySelectionPage> createState() => _PartySelectionPageState();
}

class _PartySelectionPageState extends State<PartySelectionPage> {
  bool _isLoading = true;
  List<Party> _parties = [];
  String? _searchQuery;
  
  @override
  void initState() {
    super.initState();
    _loadParties();
  }
  
  Future<void> _loadParties() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final partyService = context.read<PartyService>();
      final parties = await partyService.getActiveParties();
      
      if (mounted) {
        setState(() {
          _parties = parties;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('Failed to load parties', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load parties: $e')),
        );
      }
    }
  }
  
  List<Party> get _filteredParties {
    if (_searchQuery == null || _searchQuery!.isEmpty) {
      return _parties;
    }
    
    final query = _searchQuery!.toLowerCase();
    return _parties.where((party) {
      return party.name.toLowerCase().contains(query) ||
             party.shortName.toLowerCase().contains(query);
    }).toList();
  }
  
  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;
    
    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Select Political Party',
      ),
      body: Container(
        decoration: themeProvider.isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: Column(
          children: [
            // Search bar
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Search parties...',
                  prefixIcon: Icon(
                    Icons.search,
                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  ),
                  filled: true,
                  fillColor: isPremium ? AppTheme.premiumDarkGrey.withAlpha(150) : Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.grey.shade300,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    ),
                  ),
                ),
                style: TextStyle(
                  color: isPremium ? Colors.white : Colors.black,
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),
            
            // Party list
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredParties.isEmpty
                      ? Center(
                          child: Text(
                            'No parties found',
                            style: TextStyle(
                              color: isPremium ? Colors.white : Colors.black,
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16.0),
                          itemCount: _filteredParties.length,
                          itemBuilder: (context, index) {
                            final party = _filteredParties[index];
                            final isSelected = party.id == widget.selectedPartyId;
                            
                            // Convert hex color to Color
                            Color primaryColor;
                            try {
                              primaryColor = Color(int.parse(party.primaryColor.replaceAll('#', '0xFF')));
                            } catch (e) {
                              primaryColor = isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue;
                            }
                            
                            return Card(
                              margin: const EdgeInsets.only(bottom: 16.0),
                              color: isPremium ? AppTheme.premiumDarkGrey.withAlpha(150) : Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(
                                  color: isSelected
                                      ? primaryColor
                                      : isPremium
                                          ? AppTheme.premiumGold.withAlpha(100)
                                          : Colors.grey.shade300,
                                  width: isSelected ? 2 : 1,
                                ),
                              ),
                              child: InkWell(
                                onTap: () {
                                  widget.onPartySelected(party);
                                  Navigator.pop(context);
                                },
                                borderRadius: BorderRadius.circular(12),
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    children: [
                                      // Party logo
                                      Container(
                                        width: 60,
                                        height: 60,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(8),
                                          border: Border.all(
                                            color: isPremium
                                                ? AppTheme.premiumGold.withAlpha(100)
                                                : Colors.grey.shade300,
                                          ),
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(8),
                                          child: Image.network(
                                            party.logo,
                                            fit: BoxFit.contain,
                                            errorBuilder: (context, error, stackTrace) {
                                              return Center(
                                                child: Icon(
                                                  Icons.image_not_supported,
                                                  color: Colors.grey,
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      
                                      // Party details
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              party.name,
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: isPremium ? AppTheme.premiumGold : primaryColor,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              party.shortName,
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: isPremium ? Colors.white70 : Colors.grey[600],
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Founded: ${party.founded}',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: isPremium ? Colors.white70 : Colors.grey[600],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      
                                      // Selected indicator
                                      if (isSelected)
                                        Icon(
                                          Icons.check_circle,
                                          color: primaryColor,
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
            ),
          ],
        ),
      ),
    );
  }
}
