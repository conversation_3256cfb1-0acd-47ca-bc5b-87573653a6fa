import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class NotificationStatsCard extends StatelessWidget {
  final Map<String, int> stats;
  final bool isPremium;

  const NotificationStatsCard({
    super.key,
    required this.stats,
    required this.isPremium,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: isPremium
            ? LinearGradient(
                colors: [
                  AppTheme.premiumDarkGrey,
                  AppTheme.premiumDarkGrey.withOpacity(0.8),
                ],
              )
            : LinearGradient(
                colors: [
                  Colors.white,
                  Colors.grey[50]!,
                ],
              ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notification Statistics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Total',
                  stats['total']?.toString() ?? '0',
                  Icons.notifications,
                  isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Sent',
                  stats['sent']?.toString() ?? '0',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Scheduled',
                  stats['scheduled']?.toString() ?? '0',
                  Icons.schedule,
                  Colors.orange,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Failed',
                  stats['failed']?.toString() ?? '0',
                  Icons.error,
                  Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Recipients',
                  stats['totalRecipients']?.toString() ?? '0',
                  Icons.people,
                  isPremium ? Colors.white70 : Colors.grey[600]!,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Delivered',
                  stats['totalDelivered']?.toString() ?? '0',
                  Icons.done_all,
                  isPremium ? Colors.white70 : Colors.grey[600]!,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Opened',
                  stats['totalOpened']?.toString() ?? '0',
                  Icons.visibility,
                  isPremium ? Colors.white70 : Colors.grey[600]!,
                ),
              ),
              const Expanded(child: SizedBox()), // Empty space for alignment
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isPremium ? Colors.white70 : Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
} 