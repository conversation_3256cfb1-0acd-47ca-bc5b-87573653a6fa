import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../domain/entities/notification_model.dart';
import '../../domain/services/admin_notification_service.dart';

class CreateNotificationDialog extends StatefulWidget {
  final VoidCallback onNotificationCreated;

  const CreateNotificationDialog({
    super.key,
    required this.onNotificationCreated,
  });

  @override
  State<CreateNotificationDialog> createState() => _CreateNotificationDialogState();
}

class _CreateNotificationDialogState extends State<CreateNotificationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _bodyController = TextEditingController();
  final _imageUrlController = TextEditingController();
  
  NotificationTargetType _targetType = NotificationTargetType.all;
  final List<String> _selectedUserTypes = [];
  final List<String> _selectedTopics = [];
  bool _includePremium = true;
  bool _includeRegular = true;
  bool _isLoading = false;

  final AdminNotificationService _notificationService = AdminNotificationService();

  final List<String> _availableUserTypes = [
    'businessman',
    'politician',
    'individual_user',
    'regular', // Legacy support
  ];

  final List<String> _availableTopics = [
    // 🔵 USER TYPE-BASED TOPICS
    'all_users',
    'businessman',
    'politician', 
    'individual_user',
    
    // 🟡 PREMIUM STATUS TOPICS
    'premium_users',
    'free_users',
    
    // 🟢 LANGUAGE-BASED TOPICS
    'lang_marathi',
    'lang_hindi',
    'lang_english',
    
    // 🔴 FEATURE-BASED TOPICS
    'new_templates',
    'offers',
    'app_updates',
    
    // 🟠 ENGAGEMENT-BASED TOPICS
    'active_users',
    'inactive_users',
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _bodyController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Dialog(
      backgroundColor: isPremium ? AppTheme.premiumBlack : Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isPremium
            ? BorderSide(color: AppTheme.premiumGold, width: 1)
            : BorderSide.none,
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Text(
                    'Create Notification',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: isPremium ? Colors.white : Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Form fields
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title field
                      _buildTextField(
                        controller: _titleController,
                        label: 'Title',
                        hint: 'Enter notification title',
                        isPremium: isPremium,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Title is required';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Body field
                      _buildTextField(
                        controller: _bodyController,
                        label: 'Message',
                        hint: 'Enter notification message',
                        isPremium: isPremium,
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Message is required';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Image URL field (optional)
                      _buildTextField(
                        controller: _imageUrlController,
                        label: 'Image URL (Optional)',
                        hint: 'Enter image URL',
                        isPremium: isPremium,
                      ),
                      const SizedBox(height: 24),

                      // Target selection
                      Text(
                        'Target Audience',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Target type dropdown
                      DropdownButtonFormField<NotificationTargetType>(
                        value: _targetType,
                        decoration: InputDecoration(
                          labelText: 'Target Type',
                          labelStyle: TextStyle(
                            color: isPremium ? Colors.white70 : Colors.grey[600],
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: isPremium ? AppTheme.premiumGold : Colors.grey,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: isPremium ? AppTheme.premiumGold.withOpacity(0.5) : Colors.grey,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                              width: 2,
                            ),
                          ),
                        ),
                        dropdownColor: isPremium ? AppTheme.premiumDarkGrey : Colors.white,
                        style: TextStyle(
                          color: isPremium ? Colors.white : Colors.black,
                        ),
                        items: NotificationTargetType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Text(_getTargetTypeLabel(type)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _targetType = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Additional options based on target type
                      if (_targetType == NotificationTargetType.userType) ...[
                        Text(
                          'Select User Types',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isPremium ? Colors.white : AppTheme.textRichBlack,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ..._availableUserTypes.map((userType) {
                          return CheckboxListTile(
                            title: Text(
                              _getUserTypeLabel(userType),
                              style: TextStyle(
                                color: isPremium ? Colors.white : Colors.black,
                              ),
                            ),
                            value: _selectedUserTypes.contains(userType),
                            activeColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            checkColor: isPremium ? Colors.black : Colors.white,
                            onChanged: (value) {
                              setState(() {
                                if (value == true) {
                                  _selectedUserTypes.add(userType);
                                } else {
                                  _selectedUserTypes.remove(userType);
                                }
                              });
                            },
                          );
                        }),
                      ],

                      if (_targetType == NotificationTargetType.topic) ...[
                        Text(
                          'Select Topics',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isPremium ? Colors.white : AppTheme.textRichBlack,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ..._availableTopics.map((topic) {
                          return CheckboxListTile(
                            title: Text(
                              _getTopicLabel(topic),
                              style: TextStyle(
                                color: isPremium ? Colors.white : Colors.black,
                              ),
                            ),
                            value: _selectedTopics.contains(topic),
                            activeColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            checkColor: isPremium ? Colors.black : Colors.white,
                            onChanged: (value) {
                              setState(() {
                                if (value == true) {
                                  _selectedTopics.add(topic);
                                } else {
                                  _selectedTopics.remove(topic);
                                }
                              });
                            },
                          );
                        }),
                      ],
                    ],
                  ),
                ),
              ),

              // Action buttons
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                        foregroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _createNotification,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        foregroundColor: isPremium ? Colors.black : Colors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Create'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required bool isPremium,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      style: TextStyle(
        color: isPremium ? Colors.white : Colors.black,
      ),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: TextStyle(
          color: isPremium ? Colors.white70 : Colors.grey[600],
        ),
        hintStyle: TextStyle(
          color: isPremium ? Colors.white54 : Colors.grey[400],
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isPremium ? AppTheme.premiumGold : Colors.grey,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isPremium ? AppTheme.premiumGold.withOpacity(0.5) : Colors.grey,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
      ),
      validator: validator,
    );
  }

  String _getTargetTypeLabel(NotificationTargetType type) {
    switch (type) {
      case NotificationTargetType.all:
        return 'All Users';
      case NotificationTargetType.userType:
        return 'Specific User Types';
      case NotificationTargetType.specific:
        return 'Specific Users';
      case NotificationTargetType.premium:
        return 'Premium Users';
      case NotificationTargetType.topic:
        return 'Topics';
    }
  }

  String _getUserTypeLabel(String userType) {
    switch (userType) {
      case 'businessman':
        return 'Business Users';
      case 'politician':
        return 'Political Users';
      case 'individual_user':
        return 'Individual Users';
      case 'regular':
        return 'Regular Users';
      default:
        return userType;
    }
  }

  String _getTopicLabel(String topic) {
    switch (topic) {
      case 'all_users':
        return 'All Users';
      case 'businessman':
        return 'Business Users';
      case 'politician':
        return 'Political Users';
      case 'individual_user':
        return 'Individual Users';
      case 'premium_users':
        return 'Premium Users';
      case 'free_users':
        return 'Free Users';
      case 'lang_marathi':
        return 'Marathi Language';
      case 'lang_hindi':
        return 'Hindi Language';
      case 'lang_english':
        return 'English Language';
      case 'new_templates':
        return 'New Templates';
      case 'offers':
        return 'Offers';
      case 'app_updates':
        return 'App Updates';
      case 'active_users':
        return 'Active Users';
      case 'inactive_users':
        return 'Inactive Users';
      default:
        return topic;
    }
  }

  Future<void> _createNotification() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate target-specific requirements
    if (_targetType == NotificationTargetType.userType && _selectedUserTypes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one user type'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_targetType == NotificationTargetType.topic && _selectedTopics.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one topic'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final target = NotificationTarget(
        type: _targetType,
        userTypes: _selectedUserTypes,
        topics: _selectedTopics,
        includePremium: _includePremium,
        includeRegular: _includeRegular,
      );

      final notification = NotificationModel(
        id: '', // Will be set by Firestore
        title: _titleController.text.trim(),
        body: _bodyController.text.trim(),
        imageUrl: _imageUrlController.text.trim().isEmpty ? null : _imageUrlController.text.trim(),
        data: {
          'type': 'admin_notification',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
        target: target,
        status: NotificationStatus.draft,
        createdAt: DateTime.now(),
        createdBy: 'admin', // TODO: Get actual admin user ID
      );

      await _notificationService.createNotification(notification);

      if (mounted) {
        Navigator.pop(context);
        widget.onNotificationCreated();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create notification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
} 