import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../widgets/dashboard_card.dart';

class AnalyticsPage extends StatefulWidget {
  const AnalyticsPage({super.key});

  @override
  State<AnalyticsPage> createState() => _AnalyticsPageState();
}

class _AnalyticsPageState extends State<AnalyticsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = '7d';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Analytics',
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: Column(
          children: [
            _buildPeriodSelector(),
            Container(
              color: Colors.white,
              child: TabBar(
                controller: _tabController,
                labelColor: AppTheme.primaryBlue,
                unselectedLabelColor: AppTheme.secondaryText,
                indicatorColor: AppTheme.primaryBlue,
                tabs: const [
                  Tab(text: 'Users'),
                  Tab(text: 'Content'),
                  Tab(text: 'Revenue'),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildUserAnalytics(),
                  _buildContentAnalytics(),
                  _buildRevenueAnalytics(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          Text(
            'Period:',
            style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildPeriodChip('7d', '7 Days'),
                  _buildPeriodChip('30d', '30 Days'),
                  _buildPeriodChip('90d', '90 Days'),
                  _buildPeriodChip('1y', '1 Year'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodChip(String value, String label) {
    final isSelected = _selectedPeriod == value;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedPeriod = value;
          });
        },
        selectedColor: AppTheme.primaryBlue.withValues(alpha: 0.2),
        checkmarkColor: AppTheme.primaryBlue,
      ),
    );
  }

  Widget _buildUserAnalytics() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'User Analytics',
            style: AppTheme.headingMedium.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              DashboardCard(
                title: 'New Users',
                value: '156',
                icon: Icons.person_add,
                color: AppTheme.successGreen,
                subtitle: '+12% from last period',
              ),
              DashboardCard(
                title: 'Active Users',
                value: '1,234',
                icon: Icons.people,
                color: AppTheme.primaryBlue,
                subtitle: '+8% from last period',
              ),
              DashboardCard(
                title: 'Premium Conversions',
                value: '23',
                icon: Icons.star,
                color: AppTheme.premiumGold,
                subtitle: '+15% from last period',
              ),
              DashboardCard(
                title: 'User Retention',
                value: '78%',
                icon: Icons.trending_up,
                color: AppTheme.successGreen,
                subtitle: '+3% from last period',
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildComingSoonChart('User Growth Chart'),
          const SizedBox(height: 24),
          _buildComingSoonChart('User Type Distribution'),
        ],
      ),
    );
  }

  Widget _buildContentAnalytics() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Content Analytics',
            style: AppTheme.headingMedium.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              DashboardCard(
                title: 'Template Views',
                value: '5,678',
                icon: Icons.visibility,
                color: AppTheme.primaryBlue,
                subtitle: '+22% from last period',
              ),
              DashboardCard(
                title: 'Downloads',
                value: '2,345',
                icon: Icons.download,
                color: AppTheme.successGreen,
                subtitle: '+18% from last period',
              ),
              DashboardCard(
                title: 'Shares',
                value: '1,890',
                icon: Icons.share,
                color: AppTheme.warningOrange,
                subtitle: '+25% from last period',
              ),
              DashboardCard(
                title: 'Popular Templates',
                value: '45',
                icon: Icons.trending_up,
                color: AppTheme.errorRed,
                subtitle: 'Top performing',
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildComingSoonChart('Template Usage Trends'),
          const SizedBox(height: 24),
          _buildComingSoonChart('Most Popular Templates'),
        ],
      ),
    );
  }

  Widget _buildRevenueAnalytics() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Revenue Analytics',
            style: AppTheme.headingMedium.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              DashboardCard(
                title: 'Total Revenue',
                value: '₹12,345',
                icon: Icons.currency_rupee,
                color: AppTheme.successGreen,
                subtitle: '+28% from last period',
              ),
              DashboardCard(
                title: 'Subscriptions',
                value: '89',
                icon: Icons.subscriptions,
                color: AppTheme.primaryBlue,
                subtitle: '+15% from last period',
              ),
              DashboardCard(
                title: 'ARPU',
                value: '₹139',
                icon: Icons.person,
                color: AppTheme.premiumGold,
                subtitle: 'Average revenue per user',
              ),
              DashboardCard(
                title: 'Churn Rate',
                value: '5.2%',
                icon: Icons.trending_down,
                color: AppTheme.errorRed,
                subtitle: '-2% from last period',
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildComingSoonChart('Revenue Trends'),
          const SizedBox(height: 24),
          _buildComingSoonChart('Subscription Plans Performance'),
        ],
      ),
    );
  }

  Widget _buildComingSoonChart(String title) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        height: 200,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 48,
              color: AppTheme.secondaryText,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: AppTheme.headingMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Chart visualization coming soon',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.secondaryText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
