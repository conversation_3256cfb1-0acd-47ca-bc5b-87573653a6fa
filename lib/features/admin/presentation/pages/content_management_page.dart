import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';

class ContentManagementPage extends StatefulWidget {
  const ContentManagementPage({super.key});

  @override
  State<ContentManagementPage> createState() => _ContentManagementPageState();
}

class _ContentManagementPageState extends State<ContentManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Content Management',
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: Column(
          children: [
            Container(
              color: Colors.white,
              child: TabBar(
                controller: _tabController,
                labelColor: AppTheme.primaryBlue,
                unselectedLabelColor: AppTheme.secondaryText,
                indicatorColor: AppTheme.primaryBlue,
                tabs: const [
                  Tab(text: 'Templates'),
                  Tab(text: 'Banners'),
                  Tab(text: 'Business'),
                  Tab(text: 'Political'),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildTemplatesTab(),
                  _buildBannersTab(),
                  _buildBusinessTab(),
                  _buildPoliticalTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplatesTab() {
    return _buildComingSoonTab(
      icon: Icons.image,
      title: 'Template Management',
      description: 'Upload, organize, and manage poster templates',
      features: [
        'Upload new templates',
        'Organize by categories',
        'Set premium templates',
        'View usage statistics',
      ],
    );
  }

  Widget _buildBannersTab() {
    return _buildComingSoonTab(
      icon: Icons.flag,
      title: 'Banner Management',
      description: 'Manage promotional banners and advertisements',
      features: [
        'Upload banner images',
        'Schedule banner display',
        'Track banner performance',
        'Manage banner placement',
      ],
    );
  }

  Widget _buildBusinessTab() {
    return _buildComingSoonTab(
      icon: Icons.business,
      title: 'Business Parameters',
      description: 'Configure business user profile parameters',
      features: [
        'Add custom fields',
        'Set field types',
        'Manage dropdown options',
        'Configure validation rules',
      ],
    );
  }

  Widget _buildPoliticalTab() {
    return _buildComingSoonTab(
      icon: Icons.account_balance,
      title: 'Political Management',
      description: 'Manage political parties and parameters',
      features: [
        'Add political parties',
        'Manage party leadership',
        'Configure political fields',
        'Update party information',
      ],
    );
  }

  Widget _buildComingSoonTab({
    required IconData icon,
    required String title,
    required String description,
    required List<String> features,
  }) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.primaryBlue.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 64,
              color: AppTheme.primaryBlue,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: AppTheme.headingLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: AppTheme.bodyLarge.copyWith(
              color: AppTheme.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Coming Soon Features:',
                    style: AppTheme.headingMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...features.map((feature) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              color: AppTheme.successGreen,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                feature,
                                style: AppTheme.bodyMedium,
                              ),
                            ),
                          ],
                        ),
                      )),
                ],
              ),
            ),
          ),
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.warningOrange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.warningOrange.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.construction,
                  color: AppTheme.warningOrange,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'This feature is currently under development and will be available in a future update.',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.warningOrange,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
