import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/widgets/fancy_card.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../templates/domain/entities/template_item.dart';
import '../../../banners/domain/entities/banner_item.dart';
import '../../domain/usecases/admin_service.dart';
import 'template_upload_page.dart';

class ContentManagementWithUploadPage extends StatefulWidget {
  const ContentManagementWithUploadPage({super.key});

  @override
  State<ContentManagementWithUploadPage> createState() => _ContentManagementWithUploadPageState();
}

class _ContentManagementWithUploadPageState extends State<ContentManagementWithUploadPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ImagePicker _picker = ImagePicker();

  List<TemplateItem> _templates = [];
  List<BannerItem> _banners = [];
  bool _isLoadingTemplates = false;
  bool _isLoadingBanners = false;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadTemplates();
    _loadBanners();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTemplates() async {
    setState(() => _isLoadingTemplates = true);
    try {
      final adminService = Provider.of<AdminService>(context, listen: false);
      final templates = await adminService.getAllTemplates();
      setState(() => _templates = templates);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading templates: $e')),
        );
      }
    } finally {
      setState(() => _isLoadingTemplates = false);
    }
  }

  Future<void> _loadBanners() async {
    setState(() => _isLoadingBanners = true);
    try {
      final adminService = Provider.of<AdminService>(context, listen: false);
      final banners = await adminService.getAllBanners();
      setState(() => _banners = banners);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading banners: $e')),
        );
      }
    } finally {
      setState(() => _isLoadingBanners = false);
    }
  }

  Future<void> _uploadTemplate() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image == null) return;

      setState(() => _isUploading = true);

      if (!mounted) return;
      final adminService = Provider.of<AdminService>(context, listen: false);
      final fileName = 'template_${DateTime.now().millisecondsSinceEpoch}.jpg';

      await adminService.uploadTemplate(image.path, fileName);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Template uploaded successfully!')),
        );
        _loadTemplates(); // Refresh the list
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error uploading template: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isUploading = false);
      }
    }
  }

  Future<void> _navigateToDetailedUpload() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const TemplateUploadPage(),
      ),
    );

    // Refresh templates if upload was successful
    if (result == true) {
      _loadTemplates();
    }
  }

  Future<void> _editTemplate(TemplateItem template) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => TemplateUploadPage(template: template),
      ),
    );

    // Refresh templates if edit was successful
    if (result == true) {
      _loadTemplates();
    }
  }

  Future<void> _uploadBanner() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image == null) return;

      setState(() => _isUploading = true);

      if (!mounted) return;
      final adminService = Provider.of<AdminService>(context, listen: false);
      final fileName = 'banner_${DateTime.now().millisecondsSinceEpoch}.jpg';

      await adminService.uploadBanner(image.path, fileName);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Banner uploaded successfully!')),
        );
        _loadBanners(); // Refresh the list
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error uploading banner: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isUploading = false);
      }
    }
  }

  Future<void> _deleteTemplate(TemplateItem template) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Template'),
        content: Text('Are you sure you want to delete "${template.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final adminService = Provider.of<AdminService>(context, listen: false);
        await adminService.deleteTemplate(template.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Template deleted successfully')),
          );
          _loadTemplates(); // Refresh the list
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting template: $e')),
          );
        }
      }
    }
  }

  Future<void> _deleteBanner(BannerItem banner) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Banner'),
        content: const Text('Are you sure you want to delete this banner?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final adminService = Provider.of<AdminService>(context, listen: false);
        await adminService.deleteBanner(banner.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Banner deleted successfully')),
          );
          _loadBanners(); // Refresh the list
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting banner: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Content Management',
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: Column(
          children: [
            Container(
              color: isPremium ? AppTheme.premiumBlack : AppTheme.backgroundWhite,
              child: TabBar(
                controller: _tabController,
                labelColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                unselectedLabelColor: isPremium ? AppTheme.premiumLightGrey : AppTheme.secondaryText,
                indicatorColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                isScrollable: true,
                tabs: const [
                  Tab(text: 'Templates', icon: Icon(Icons.image)),
                  Tab(text: 'Banners', icon: Icon(Icons.flag)),
                  Tab(text: 'Business', icon: Icon(Icons.business)),
                  Tab(text: 'Political', icon: Icon(Icons.account_balance)),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildTemplatesTab(),
                  _buildBannersTab(),
                  _buildBusinessTab(),
                  _buildPoliticalTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplatesTab() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    if (_isLoadingTemplates) {
      return Center(
        child: CircularProgressIndicator(
          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
        ),
      );
    }

    if (_templates.isEmpty) {
      return SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: [
            // Upload section at the top (scrollable)
            _buildUploadSection(isPremium),

            // Empty state content
            Container(
              padding: const EdgeInsets.all(40),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image_not_supported,
                    size: 64,
                    color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No templates found',
                    style: AppTheme.headingMedium.copyWith(
                      color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Upload your first template to get started',
                    style: AppTheme.bodyMedium.copyWith(
                      color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Bottom spacing
            const SizedBox(height: 100),
          ],
        ),
      );
    }

    // Use LayoutBuilder to determine grid configuration, then CustomScrollView
    return LayoutBuilder(
      builder: (context, constraints) {
        int crossAxisCount;
        double childAspectRatio;

        if (constraints.maxWidth > 1200) {
          // Large desktop
          crossAxisCount = 4;
          childAspectRatio = 0.75;
        } else if (constraints.maxWidth > 800) {
          // Desktop/tablet
          crossAxisCount = 3;
          childAspectRatio = 0.75;
        } else if (constraints.maxWidth > 600) {
          // Small tablet
          crossAxisCount = 2;
          childAspectRatio = 0.8;
        } else {
          // Mobile
          crossAxisCount = 1;
          childAspectRatio = 1.2;
        }

        return CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // Upload section as a sliver
            SliverToBoxAdapter(
              child: _buildUploadSection(isPremium),
            ),

            // Templates grid as a sliver with padding
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              sliver: SliverGrid(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: 20,
                  mainAxisSpacing: 20,
                  childAspectRatio: childAspectRatio,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final template = _templates[index];
                    return AnimatedContainer(
                      duration: Duration(milliseconds: 300 + (index * 50)),
                      curve: Curves.easeOutBack,
                      child: _buildTemplateCard(template, isPremium),
                    );
                  },
                  childCount: _templates.length,
                ),
              ),
            ),

            // Bottom spacing as a sliver
            const SliverToBoxAdapter(
              child: SizedBox(height: 100),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUploadSection(bool isPremium) {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: isPremium
            ? LinearGradient(
                colors: [
                  AppTheme.premiumGold.withValues(alpha: 0.1),
                  AppTheme.premiumBlack.withValues(alpha: 0.05),
                ],
              )
            : LinearGradient(
                colors: [
                  AppTheme.primaryBlue.withValues(alpha: 0.1),
                  AppTheme.backgroundWhite,
                ],
              ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isPremium
              ? AppTheme.premiumGold.withValues(alpha: 0.3)
              : AppTheme.primaryBlue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.cloud_upload_outlined,
                  color: isPremium ? AppTheme.premiumBlack : Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Upload Templates',
                      style: AppTheme.headingMedium.copyWith(
                        color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Add new templates to your collection',
                      style: AppTheme.bodyMedium.copyWith(
                        color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth < 400) {
                // Stack vertically on very small screens
                return Column(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: GradientButton(
                        text: _isUploading ? 'Uploading...' : 'Quick Upload',
                        onPressed: _isUploading ? () {} : () => _uploadTemplate(),
                        icon: Icon(
                          _isUploading ? Icons.hourglass_empty : Icons.flash_on,
                          size: 20,
                        ),
                        isLoading: _isUploading,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            width: 2,
                          ),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _navigateToDetailedUpload(),
                            borderRadius: BorderRadius.circular(14),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.tune,
                                    size: 20,
                                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                  ),
                                  const SizedBox(width: 8),
                                  Flexible(
                                    child: Text(
                                      'Detailed Upload',
                                      style: AppTheme.bodyMedium.copyWith(
                                        color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              } else {
                // Side by side on larger screens
                return Row(
                  children: [
                    Expanded(
                      child: GradientButton(
                        text: _isUploading ? 'Uploading...' : 'Quick Upload',
                        onPressed: _isUploading ? () {} : () => _uploadTemplate(),
                        icon: Icon(
                          _isUploading ? Icons.hourglass_empty : Icons.flash_on,
                          size: 20,
                        ),
                        isLoading: _isUploading,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            width: 2,
                          ),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _navigateToDetailedUpload(),
                            borderRadius: BorderRadius.circular(14),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.tune,
                                    size: 18,
                                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                  ),
                                  const SizedBox(width: 6),
                                  Flexible(
                                    child: Text(
                                      'Detailed',
                                      style: AppTheme.bodyMedium.copyWith(
                                        color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBannersTab() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Column(
      children: [
        // Upload button
        Container(
          padding: const EdgeInsets.all(16),
          child: SizedBox(
            width: double.infinity,
            child: GradientButton(
              text: _isUploading ? 'Uploading...' : 'Upload Banner',
              onPressed: _isUploading ? () {} : () => _uploadBanner(),
              icon: const Icon(Icons.upload, size: 20),
              isLoading: _isUploading,
            ),
          ),
        ),
        // Banners list
        Expanded(
          child: _isLoadingBanners
              ? const Center(child: CircularProgressIndicator())
              : _banners.isEmpty
                  ? _buildEmptyState(
                      icon: Icons.flag,
                      title: 'No Banners',
                      subtitle: 'Upload your first banner to get started',
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _banners.length,
                      itemBuilder: (context, index) {
                        final banner = _banners[index];
                        return _buildBannerCard(banner, isPremium);
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildTemplateCard(TemplateItem template, bool isPremium) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: isPremium
                ? AppTheme.premiumGold.withValues(alpha: 0.1)
                : AppTheme.primaryBlue.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _editTemplate(template),
          borderRadius: BorderRadius.circular(20),
          splashColor: isPremium
              ? AppTheme.premiumGold.withValues(alpha: 0.1)
              : AppTheme.primaryBlue.withValues(alpha: 0.1),
          highlightColor: isPremium
              ? AppTheme.premiumGold.withValues(alpha: 0.05)
              : AppTheme.primaryBlue.withValues(alpha: 0.05),
          child: FancyCard(
            margin: EdgeInsets.zero,
            backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
            borderRadius: 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
            // Image section with overlay badges
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            isPremium ? AppTheme.premiumBlack : AppTheme.lightGray,
                            isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
                          ],
                        ),
                      ),
                      child: Image.network(
                        template.imageUrl,
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                                  : null,
                              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) => Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.errorRed.withValues(alpha: 0.1),
                                AppTheme.errorRed.withValues(alpha: 0.05),
                              ],
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.broken_image_outlined,
                                size: 32,
                                color: AppTheme.errorRed.withValues(alpha: 0.7),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Failed to load',
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppTheme.errorRed.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Premium badge
                  if (template.isPremium)
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [AppTheme.premiumGold, AppTheme.premiumLightGold],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.premiumGold.withValues(alpha: 0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.star,
                              size: 12,
                              color: AppTheme.premiumBlack,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'PREMIUM',
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.premiumBlack,
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Active/Inactive status
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                      decoration: BoxDecoration(
                        color: template.isActive
                            ? AppTheme.successGreen.withValues(alpha: 0.9)
                            : AppTheme.errorRed.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        template.isActive ? 'ACTIVE' : 'INACTIVE',
                        style: AppTheme.bodySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 9,
                        ),
                      ),
                    ),
                  ),

                  // Action buttons overlay
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        GestureDetector(
                          onTap: () => _editTemplate(template),
                          child: _buildActionButton(
                            icon: Icons.edit_outlined,
                            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            onPressed: () => _editTemplate(template),
                            tooltip: 'Edit Template',
                          ),
                        ),
                        const SizedBox(width: 8),
                        GestureDetector(
                          onTap: () => _deleteTemplate(template),
                          child: _buildActionButton(
                            icon: Icons.delete_outline,
                            color: AppTheme.errorRed,
                            onPressed: () => _deleteTemplate(template),
                            tooltip: 'Delete Template',
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Content section
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Template name
                    Flexible(
                      child: Text(
                        template.name,
                        style: AppTheme.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    const SizedBox(height: 6),

                    // Category and subcategory
                    Flexible(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                        decoration: BoxDecoration(
                          color: isPremium
                              ? AppTheme.premiumGold.withValues(alpha: 0.1)
                              : AppTheme.primaryBlue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: isPremium
                                ? AppTheme.premiumGold.withValues(alpha: 0.3)
                                : AppTheme.primaryBlue.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          template.category ?? 'General',
                          style: AppTheme.bodySmall.copyWith(
                            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            fontWeight: FontWeight.w600,
                            fontSize: 11,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),

                    const SizedBox(height: 6),

                    // Stats row with click hint
                    Flexible(
                      child: Row(
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  _buildStatChip(
                                    icon: Icons.download_outlined,
                                    value: template.downloadCount.toString(),
                                    isPremium: isPremium,
                                  ),
                                  const SizedBox(width: 6),
                                  _buildStatChip(
                                    icon: Icons.visibility_outlined,
                                    value: template.viewCount.toString(),
                                    isPremium: isPremium,
                                  ),
                                  if (template.rating != null) ...[
                                    const SizedBox(width: 6),
                                    _buildStatChip(
                                      icon: Icons.star_outline,
                                      value: template.rating!.toStringAsFixed(1),
                                      isPremium: isPremium,
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: isPremium
                                  ? AppTheme.premiumGold.withValues(alpha: 0.1)
                                  : AppTheme.primaryBlue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: isPremium
                                    ? AppTheme.premiumGold.withValues(alpha: 0.3)
                                    : AppTheme.primaryBlue.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.touch_app,
                                  size: 10,
                                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  'EDIT',
                                  style: AppTheme.bodySmall.copyWith(
                                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 8,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return GestureDetector(
      onTap: () {
        // Prevent event bubbling to parent InkWell
        onPressed();
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Icon(
            icon,
            size: 18,
            color: color,
          ),
        ),
      ),
    );
  }

  Widget _buildStatChip({
    required IconData icon,
    required String value,
    required bool isPremium,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: isPremium
            ? AppTheme.premiumBlack.withValues(alpha: 0.1)
            : AppTheme.lightGray.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 10,
            color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
          ),
          const SizedBox(width: 2),
          Text(
            value,
            style: AppTheme.bodySmall.copyWith(
              color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
              fontWeight: FontWeight.w600,
              fontSize: 9,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBannerCard(BannerItem banner, bool isPremium) {
    return FancyCard(
      margin: const EdgeInsets.only(bottom: 12),
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Row(
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.horizontal(left: Radius.circular(16)),
            child: Image.network(
              banner.imageUrl,
              width: 100,
              height: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: 100,
                height: 80,
                color: AppTheme.lightGray,
                child: const Icon(Icons.error, color: AppTheme.errorRed),
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Banner ${banner.id}',
                    style: AppTheme.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    banner.isActive ? 'Active' : 'Inactive',
                    style: AppTheme.bodySmall.copyWith(
                      color: banner.isActive ? AppTheme.successGreen : AppTheme.errorRed,
                    ),
                  ),
                ],
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.delete, color: AppTheme.errorRed),
            onPressed: () => _deleteBanner(banner),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.primaryBlue.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 64,
              color: AppTheme.primaryBlue,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: AppTheme.bodyMedium.copyWith(
              color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessTab() {
    return _buildComingSoonTab(
      icon: Icons.business,
      title: 'Business Parameters',
      description: 'Manage business user parameters and settings',
      features: [
        'Add business categories',
        'Manage parameter types',
        'Set default values',
        'Configure validation rules',
      ],
    );
  }

  Widget _buildPoliticalTab() {
    return _buildComingSoonTab(
      icon: Icons.account_balance,
      title: 'Political Parameters',
      description: 'Manage political user parameters and parties',
      features: [
        'Add political parties',
        'Manage party information',
        'Set party colors and logos',
        'Configure political parameters',
      ],
    );
  }

  Widget _buildComingSoonTab({
    required IconData icon,
    required String title,
    required String description,
    required List<String> features,
  }) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.primaryBlue.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 64,
              color: AppTheme.primaryBlue,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: AppTheme.headingLarge.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            description,
            style: AppTheme.bodyLarge.copyWith(
              color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Text(
            'Coming Soon Features:',
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          ...features.map((feature) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      color: AppTheme.successGreen,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        feature,
                        style: AppTheme.bodyMedium.copyWith(
                          color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.warningOrange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.warningOrange.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.construction,
                  color: AppTheme.warningOrange,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'This feature is currently under development and will be available in a future update.',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.warningOrange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
