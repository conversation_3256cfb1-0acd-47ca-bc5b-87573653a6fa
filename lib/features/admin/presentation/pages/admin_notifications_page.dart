import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../domain/entities/notification_model.dart';
import '../../domain/services/admin_notification_service.dart';
import '../widgets/create_notification_dialog.dart';
import '../widgets/notification_stats_card.dart';

class AdminNotificationsPage extends StatefulWidget {
  const AdminNotificationsPage({super.key});

  @override
  State<AdminNotificationsPage> createState() => _AdminNotificationsPageState();
}

class _AdminNotificationsPageState extends State<AdminNotificationsPage> {
  final AdminNotificationService _notificationService = AdminNotificationService();
  Map<String, int>? _stats;
  bool _isLoadingStats = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    try {
      final stats = await _notificationService.getNotificationStats();
      setState(() {
        _stats = stats;
        _isLoadingStats = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingStats = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading stats: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Push Notifications',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStats,
          ),
        ],
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: Column(
          children: [
            // Stats Section
            if (_isLoadingStats)
              const Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              )
            else if (_stats != null)
              NotificationStatsCard(stats: _stats!, isPremium: isPremium),

            // Notifications List
            Expanded(
              child: StreamBuilder<List<NotificationModel>>(
                stream: _notificationService.getNotifications(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading notifications',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            snapshot.error.toString(),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  final notifications = snapshot.data ?? [];

                  if (notifications.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.notifications_none,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No notifications yet',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Create your first notification to get started',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: notifications.length,
                    itemBuilder: (context, index) {
                      final notification = notifications[index];
                      return _buildNotificationCard(notification, isPremium);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showCreateNotificationDialog(context),
        backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'Create Notification',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildNotificationCard(NotificationModel notification, bool isPremium) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: isPremium
              ? LinearGradient(
                  colors: [
                    AppTheme.premiumDarkGrey,
                    AppTheme.premiumDarkGrey.withOpacity(0.8),
                  ],
                )
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      notification.title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isPremium ? Colors.white : AppTheme.textRichBlack,
                      ),
                    ),
                  ),
                  _buildStatusChip(notification.status, isPremium),
                ],
              ),
              const SizedBox(height: 8),

              // Body text
              Text(
                notification.body,
                style: TextStyle(
                  fontSize: 14,
                  color: isPremium ? Colors.white70 : Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // Target info
              _buildTargetInfo(notification.target, isPremium),
              const SizedBox(height: 12),

              // Stats row
              if (notification.status == NotificationStatus.sent)
                _buildStatsRow(notification, isPremium),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (notification.status == NotificationStatus.draft ||
                      notification.status == NotificationStatus.scheduled)
                    TextButton.icon(
                      onPressed: () => _sendNotification(notification.id),
                      icon: const Icon(Icons.send, size: 16),
                      label: const Text('Send'),
                      style: TextButton.styleFrom(
                        foregroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                      ),
                    ),
                  
                  if (notification.status == NotificationStatus.scheduled)
                    TextButton.icon(
                      onPressed: () => _cancelNotification(notification.id),
                      icon: const Icon(Icons.cancel, size: 16),
                      label: const Text('Cancel'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.orange,
                      ),
                    ),

                  TextButton.icon(
                    onPressed: () => _deleteNotification(notification.id),
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('Delete'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(NotificationStatus status, bool isPremium) {
    Color color;
    String text;

    switch (status) {
      case NotificationStatus.draft:
        color = Colors.grey;
        text = 'Draft';
        break;
      case NotificationStatus.scheduled:
        color = Colors.orange;
        text = 'Scheduled';
        break;
      case NotificationStatus.sending:
        color = Colors.blue;
        text = 'Sending';
        break;
      case NotificationStatus.sent:
        color = Colors.green;
        text = 'Sent';
        break;
      case NotificationStatus.failed:
        color = Colors.red;
        text = 'Failed';
        break;
      case NotificationStatus.cancelled:
        color = Colors.grey;
        text = 'Cancelled';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTargetInfo(NotificationTarget target, bool isPremium) {
    String targetText;
    IconData icon;

    switch (target.type) {
      case NotificationTargetType.all:
        targetText = 'All Users';
        icon = Icons.people;
        break;
      case NotificationTargetType.userType:
        targetText = 'User Types: ${target.userTypes.join(', ')}';
        icon = Icons.group;
        break;
      case NotificationTargetType.specific:
        targetText = '${target.specificUsers.length} Specific Users';
        icon = Icons.person;
        break;
      case NotificationTargetType.premium:
        targetText = 'Premium Users';
        icon = Icons.star;
        break;
      case NotificationTargetType.topic:
        targetText = 'Topics: ${target.topics.join(', ')}';
        icon = Icons.topic;
        break;
    }

    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            targetText,
            style: TextStyle(
              fontSize: 12,
              color: isPremium ? Colors.white70 : Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatsRow(NotificationModel notification, bool isPremium) {
    return Row(
      children: [
        _buildStatItem(
          'Recipients',
          notification.recipientCount?.toString() ?? '0',
          Icons.people_outline,
          isPremium,
        ),
        const SizedBox(width: 16),
        _buildStatItem(
          'Delivered',
          notification.deliveredCount?.toString() ?? '0',
          Icons.check_circle_outline,
          isPremium,
        ),
        const SizedBox(width: 16),
        _buildStatItem(
          'Opened',
          notification.openedCount?.toString() ?? '0',
          Icons.visibility_outlined,
          isPremium,
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, bool isPremium) {
    return Row(
      children: [
        Icon(
          icon,
          size: 14,
          color: isPremium ? Colors.white54 : Colors.grey[500],
        ),
        const SizedBox(width: 4),
        Text(
          '$label: $value',
          style: TextStyle(
            fontSize: 11,
            color: isPremium ? Colors.white54 : Colors.grey[500],
          ),
        ),
      ],
    );
  }

  void _showCreateNotificationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CreateNotificationDialog(
        onNotificationCreated: () {
          _loadStats(); // Refresh stats after creating notification
        },
      ),
    );
  }

  Future<void> _sendNotification(String notificationId) async {
    try {
      await _notificationService.sendNotification(notificationId);
      _loadStats();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification sent successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print(e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send notification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _cancelNotification(String notificationId) async {
    try {
      await _notificationService.cancelNotification(notificationId);
      _loadStats();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification cancelled'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel notification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteNotification(String notificationId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notification'),
        content: const Text('Are you sure you want to delete this notification?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _notificationService.deleteNotification(notificationId);
        _loadStats();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Notification deleted'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete notification: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
} 