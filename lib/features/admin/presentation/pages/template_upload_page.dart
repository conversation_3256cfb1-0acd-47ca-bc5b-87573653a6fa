import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/widgets/fancy_card.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../../../templates/domain/entities/template_item.dart';
import '../../domain/usecases/admin_service.dart';

class TemplateUploadPage extends StatefulWidget {
  final TemplateItem? template; // For editing existing template

  const TemplateUploadPage({
    super.key,
    this.template,
  });

  @override
  State<TemplateUploadPage> createState() => _TemplateUploadPageState();
}

class _TemplateUploadPageState extends State<TemplateUploadPage> {
  final _formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();

  // Form controllers
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _creatorController = TextEditingController();
  final _dimensionsController = TextEditingController();
  final _tagsController = TextEditingController();
  final _colorsController = TextEditingController();
  final _fontStylesController = TextEditingController();
  final _fileFormatsController = TextEditingController();

  // Selected values
  String? _selectedCategory;
  String? _selectedSubCategory;
  String? _selectedLayoutType;
  String? _selectedTargetAudience;
  String? _selectedDesignStyle;
  String? _selectedUsageType;
  String? _selectedResolution;
  String? _selectedLicenseType;
  bool _isPremium = false;
  bool _isActive = true;

  // Image
  XFile? _selectedImage;
  String? _imageUrl;

  // Loading state
  bool _isUploading = false;

  // Dropdown options (mutable to allow adding new values)
  List<String> _categories = [
    'Business', 'Event', 'Festival', 'Party', 'Education', 'Health',
    'Technology', 'Food', 'Travel', 'Sports', 'Fashion', 'Real Estate'
  ];

  Map<String, List<String>> _subCategories = {
    'Business': ['Flyers', 'Brochures', 'Business Cards', 'Presentations', 'Logos'],
    'Event': ['Invitations', 'Tickets', 'Programs', 'Banners', 'Posters'],
    'Festival': ['Celebration', 'Religious', 'Cultural', 'Seasonal'],
    'Party': ['Birthday', 'Wedding', 'Anniversary', 'Graduation'],
    'Education': ['Certificates', 'Presentations', 'Worksheets', 'Posters'],
    'Health': ['Awareness', 'Medical', 'Fitness', 'Nutrition'],
    'Technology': ['App Promotion', 'Software', 'Digital Services'],
    'Food': ['Menu', 'Recipe', 'Restaurant', 'Catering'],
    'Travel': ['Tourism', 'Hotels', 'Transportation', 'Adventure'],
    'Sports': ['Team', 'Tournament', 'Fitness', 'Equipment'],
    'Fashion': ['Clothing', 'Accessories', 'Beauty', 'Style'],
    'Real Estate': ['Property', 'Rental', 'Commercial', 'Residential'],
  };

  List<String> get _layoutTypes => [
    AppLocalizations.of(context)!.horizontal,
    AppLocalizations.of(context)!.vertical,
    AppLocalizations.of(context)!.square
  ];

  List<String> get _targetAudiences => [
    AppLocalizations.of(context)!.smallBusiness,
    AppLocalizations.of(context)!.eventOrganizers,
    AppLocalizations.of(context)!.educators,
    AppLocalizations.of(context)!.marketers,
    AppLocalizations.of(context)!.students,
    AppLocalizations.of(context)!.professionals,
    AppLocalizations.of(context)!.generalPublic
  ];

  List<String> get _designStyles => [
    AppLocalizations.of(context)!.modern,
    AppLocalizations.of(context)!.minimalist,
    AppLocalizations.of(context)!.retro,
    AppLocalizations.of(context)!.elegant,
    AppLocalizations.of(context)!.creative,
    AppLocalizations.of(context)!.professional,
    AppLocalizations.of(context)!.playful
  ];

  List<String> get _usageTypes => [
    AppLocalizations.of(context)!.print,
    AppLocalizations.of(context)!.socialMedia,
    AppLocalizations.of(context)!.onlineAdvertising,
    AppLocalizations.of(context)!.email,
    AppLocalizations.of(context)!.web
  ];

  List<String> get _resolutions => [
    AppLocalizations.of(context)!.high,
    AppLocalizations.of(context)!.medium,
    AppLocalizations.of(context)!.low
  ];

  List<String> get _licenseTypes => [
    AppLocalizations.of(context)!.freeForPersonalUse,
    AppLocalizations.of(context)!.commercialUseAllowed,
    AppLocalizations.of(context)!.premiumLicenseRequired
  ];

  @override
  void initState() {
    super.initState();
    if (widget.template != null) {
      _populateFormWithTemplate(widget.template!);
    } else {
      // Set default values
      _creatorController.text = 'Admin';
      _dimensionsController.text = '1080x1080';
      // Default values will be set in build method when context is available
    }
  }

  void _populateFormWithTemplate(TemplateItem template) {
    _nameController.text = template.name;
    _descriptionController.text = template.description ?? '';
    _creatorController.text = template.creator ?? 'Admin';
    _dimensionsController.text = template.dimensions ?? '1080x1080';
    _tagsController.text = template.tags?.join(', ') ?? '';
    _colorsController.text = template.colors?.join(', ') ?? '';
    _fontStylesController.text = template.fontStyles?.join(', ') ?? '';
    _fileFormatsController.text = template.fileFormats?.join(', ') ?? '';

    // Safely set dropdown values, adding to lists if they don't exist
    _selectedCategory = template.category;
    if (template.category != null && !_categories.contains(template.category)) {
      _categories.add(template.category!);
    }

    // Handle subcategory after category is set
    if (template.subCategory != null && _selectedCategory != null) {
      if (_subCategories[_selectedCategory] == null) {
        _subCategories[_selectedCategory!] = [];
      }
      if (!_subCategories[_selectedCategory]!.contains(template.subCategory)) {
        _subCategories[_selectedCategory]!.add(template.subCategory!);
      }
      _selectedSubCategory = template.subCategory;
    } else {
      _selectedSubCategory = null;
    }

    _selectedLayoutType = template.layoutType;
    _selectedTargetAudience = template.targetAudience;
    _selectedDesignStyle = template.designStyle;
    _selectedUsageType = template.usageType;
    _selectedResolution = template.resolution;
    _selectedLicenseType = template.licenseType;

    _isPremium = template.isPremium;
    _isActive = template.isActive;
    _imageUrl = template.imageUrl;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _creatorController.dispose();
    _dimensionsController.dispose();
    _tagsController.dispose();
    _colorsController.dispose();
    _fontStylesController.dispose();
    _fileFormatsController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = image;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.failedToUploadTemplate)),
        );
      }
    }
  }

  Future<void> _uploadTemplate() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedImage == null && widget.template == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context)!.pleaseSelectImage)),
      );
      return;
    }

    setState(() => _isUploading = true);

    try {
      if (!mounted) return;
      final adminService = Provider.of<AdminService>(context, listen: false);

      String imageUrl = _imageUrl ?? '';

      // Upload new image if selected
      if (_selectedImage != null) {
        final fileName = 'template_${DateTime.now().millisecondsSinceEpoch}.jpg';
        imageUrl = await adminService.uploadTemplate(_selectedImage!.path, fileName);
      }

      // Create template data
      final templateData = TemplateItem(
        id: widget.template?.id ?? '',
        imageUrl: imageUrl,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null : _descriptionController.text.trim(),
        category: _selectedCategory,
        subCategory: _selectedSubCategory,
        isPremium: _isPremium,
        tags: _tagsController.text.trim().isEmpty
            ? null : _tagsController.text.split(',').map((e) => e.trim()).toList(),
        layoutType: _selectedLayoutType,
        targetAudience: _selectedTargetAudience,
        designStyle: _selectedDesignStyle,
        colors: _colorsController.text.trim().isEmpty
            ? null : _colorsController.text.split(',').map((e) => e.trim()).toList(),
        fontStyles: _fontStylesController.text.trim().isEmpty
            ? null : _fontStylesController.text.split(',').map((e) => e.trim()).toList(),
        usageType: _selectedUsageType,
        resolution: _selectedResolution,
        licenseType: _selectedLicenseType,
        uploadDate: widget.template?.uploadDate ?? DateTime.now(),
        lastModified: DateTime.now(),
        creator: _creatorController.text.trim().isEmpty
            ? null : _creatorController.text.trim(),
        fileFormats: _fileFormatsController.text.trim().isEmpty
            ? ['JPEG'] : _fileFormatsController.text.split(',').map((e) => e.trim()).toList(),
        downloadCount: widget.template?.downloadCount ?? 0,
        viewCount: widget.template?.viewCount ?? 0,
        rating: widget.template?.rating,
        ratingCount: widget.template?.ratingCount ?? 0,
        isActive: _isActive,
        dimensions: _dimensionsController.text.trim().isEmpty
            ? null : _dimensionsController.text.trim(),
        fileSize: widget.template?.fileSize,
        uploadedBy: 'admin', // TODO: Get actual admin user ID
      );

      // Save or update template
      if (widget.template == null) {
        // This is a new template - it was already saved during upload
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.templateUploadedSuccessfully)),
          );
          Navigator.pop(context, true);
        }
      } else {
        // This is an update - call update method
        await adminService.updateTemplate(templateData.copyWith(id: widget.template!.id));

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.templateUpdatedSuccessfully)),
          );
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.failedToUploadTemplate)),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isUploading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: widget.template == null
            ? AppLocalizations.of(context)!.uploadTemplate
            : AppLocalizations.of(context)!.updateTemplate,
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildImageSection(isPremium),
                      const SizedBox(height: 24),
                      _buildBasicInfoSection(isPremium),
                      const SizedBox(height: 24),
                      _buildCategorySection(isPremium),
                      const SizedBox(height: 24),
                      _buildDesignSection(isPremium),
                      const SizedBox(height: 24),
                      _buildTechnicalSection(isPremium),
                      const SizedBox(height: 24),
                      _buildSettingsSection(isPremium),
                    ],
                  ),
                ),
              ),
              _buildBottomActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection(bool isPremium) {
    return FancyCard(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.selectImage,
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                color: isPremium
                    ? AppTheme.premiumBlack.withValues(alpha: 0.3)
                    : AppTheme.lightGray.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  style: BorderStyle.solid,
                  width: 2,
                ),
              ),
              child: _selectedImage != null || _imageUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: _selectedImage != null
                          ? Image.file(
                              File(_selectedImage!.path),
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  _buildImagePlaceholder(isPremium),
                            )
                          : Image.network(
                              _imageUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  _buildImagePlaceholder(isPremium),
                            ),
                    )
                  : _buildImagePlaceholder(isPremium),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context)!.selectImage,
            style: AppTheme.bodySmall.copyWith(
              color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder(bool isPremium) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.add_photo_alternate,
          size: 48,
          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context)!.selectImage,
          style: AppTheme.bodyMedium.copyWith(
            color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection(bool isPremium) {
    return FancyCard(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.templateName,
              hintText: AppLocalizations.of(context)!.pleaseEnterTemplateName,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return AppLocalizations.of(context)!.pleaseEnterTemplateName;
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.templateDescription,
              hintText: AppLocalizations.of(context)!.templateDescription,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _creatorController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.creator,
              hintText: AppLocalizations.of(context)!.creator,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection(bool isPremium) {
    return FancyCard(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Category & Classification',
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedCategory,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.category,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
            ),
            items: _categories.map((category) => DropdownMenuItem(
              value: category,
              child: Text(category),
            )).toList(),
            onChanged: (value) {
              setState(() {
                _selectedCategory = value;
                _selectedSubCategory = null; // Reset subcategory
              });
            },
            isExpanded: true,
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedCategory != null &&
                   _subCategories[_selectedCategory] != null &&
                   _subCategories[_selectedCategory]!.contains(_selectedSubCategory)
                   ? _selectedSubCategory : null,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.subCategory,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
            ),
            items: _selectedCategory != null && _subCategories[_selectedCategory] != null
                ? _subCategories[_selectedCategory]!.map((subCategory) => DropdownMenuItem(
                    value: subCategory,
                    child: Text(subCategory),
                  )).toList()
                : <DropdownMenuItem<String>>[],
            onChanged: (value) {
              setState(() {
                _selectedSubCategory = value;
              });
            },
            isExpanded: true,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _tagsController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.tags,
              hintText: AppLocalizations.of(context)!.tags,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesignSection(bool isPremium) {
    return FancyCard(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Design & Style',
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          // Layout Type and Design Style - Responsive Layout
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth < 600) {
                // Stack vertically on small screens
                return Column(
                  children: [
                    DropdownButtonFormField<String>(
                      value: _selectedLayoutType,
                      decoration: InputDecoration(
                        labelText: 'Layout Type',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      ),
                      items: _layoutTypes.map((type) => DropdownMenuItem(
                        value: type,
                        child: Text(type),
                      )).toList(),
                      onChanged: (value) => setState(() => _selectedLayoutType = value),
                      isExpanded: true,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedDesignStyle,
                      decoration: InputDecoration(
                        labelText: 'Design Style',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      ),
                      items: _designStyles.map((style) => DropdownMenuItem(
                        value: style,
                        child: Text(style),
                      )).toList(),
                      onChanged: (value) => setState(() => _selectedDesignStyle = value),
                      isExpanded: true,
                    ),
                  ],
                );
              } else {
                // Side by side on larger screens
                return Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedLayoutType,
                        decoration: InputDecoration(
                          labelText: 'Layout Type',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                          filled: true,
                          fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                        ),
                        items: _layoutTypes.map((type) => DropdownMenuItem(
                          value: type,
                          child: Text(type),
                        )).toList(),
                        onChanged: (value) => setState(() => _selectedLayoutType = value),
                        isExpanded: true,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedDesignStyle,
                        decoration: InputDecoration(
                          labelText: 'Design Style',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                          filled: true,
                          fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                        ),
                        items: _designStyles.map((style) => DropdownMenuItem(
                          value: style,
                          child: Text(style),
                        )).toList(),
                        onChanged: (value) => setState(() => _selectedDesignStyle = value),
                        isExpanded: true,
                      ),
                    ),
                  ],
                );
              }
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _colorsController,
            decoration: InputDecoration(
              labelText: 'Colors',
              hintText: 'Enter colors separated by commas (e.g., Blue, Red, White)',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _fontStylesController,
            decoration: InputDecoration(
              labelText: 'Font Styles',
              hintText: 'Enter font styles separated by commas',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              filled: true,
              fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTechnicalSection(bool isPremium) {
    return FancyCard(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Technical Details',
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          // Usage Type and Resolution - Responsive Layout
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth < 600) {
                // Stack vertically on small screens
                return Column(
                  children: [
                    DropdownButtonFormField<String>(
                      value: _selectedUsageType,
                      decoration: InputDecoration(
                        labelText: 'Usage Type',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      ),
                      items: _usageTypes.map((type) => DropdownMenuItem(
                        value: type,
                        child: Text(type),
                      )).toList(),
                      onChanged: (value) => setState(() => _selectedUsageType = value),
                      isExpanded: true,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedResolution,
                      decoration: InputDecoration(
                        labelText: 'Resolution',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      ),
                      items: _resolutions.map((resolution) => DropdownMenuItem(
                        value: resolution,
                        child: Text(resolution),
                      )).toList(),
                      onChanged: (value) => setState(() => _selectedResolution = value),
                      isExpanded: true,
                    ),
                  ],
                );
              } else {
                // Side by side on larger screens
                return Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedUsageType,
                        decoration: InputDecoration(
                          labelText: 'Usage Type',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                          filled: true,
                          fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                        ),
                        items: _usageTypes.map((type) => DropdownMenuItem(
                          value: type,
                          child: Text(type),
                        )).toList(),
                        onChanged: (value) => setState(() => _selectedUsageType = value),
                        isExpanded: true,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedResolution,
                        decoration: InputDecoration(
                          labelText: 'Resolution',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                          filled: true,
                          fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                        ),
                        items: _resolutions.map((resolution) => DropdownMenuItem(
                          value: resolution,
                          child: Text(resolution),
                        )).toList(),
                        onChanged: (value) => setState(() => _selectedResolution = value),
                        isExpanded: true,
                      ),
                    ),
                  ],
                );
              }
            },
          ),
          const SizedBox(height: 16),
          // Dimensions and File Formats - Responsive Layout
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth < 600) {
                // Stack vertically on small screens
                return Column(
                  children: [
                    TextFormField(
                      controller: _dimensionsController,
                      decoration: InputDecoration(
                        labelText: 'Dimensions',
                        hintText: 'e.g., 1080x1080',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _fileFormatsController,
                      decoration: InputDecoration(
                        labelText: 'File Formats',
                        hintText: 'e.g., JPEG, PNG',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      ),
                    ),
                  ],
                );
              } else {
                // Side by side on larger screens
                return Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _dimensionsController,
                        decoration: InputDecoration(
                          labelText: 'Dimensions',
                          hintText: 'e.g., 1080x1080',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                          filled: true,
                          fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _fileFormatsController,
                        decoration: InputDecoration(
                          labelText: 'File Formats',
                          hintText: 'e.g., JPEG, PNG',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                          filled: true,
                          fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                        ),
                      ),
                    ),
                  ],
                );
              }
            },
          ),
          const SizedBox(height: 16),
          // Target Audience and License Type - Responsive Layout
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth < 600) {
                // Stack vertically on small screens
                return Column(
                  children: [
                    DropdownButtonFormField<String>(
                      value: _selectedTargetAudience,
                      decoration: InputDecoration(
                        labelText: 'Target Audience',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      ),
                      items: _targetAudiences.map((audience) => DropdownMenuItem(
                        value: audience,
                        child: Text(audience),
                      )).toList(),
                      onChanged: (value) => setState(() => _selectedTargetAudience = value),
                      isExpanded: true,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedLicenseType,
                      decoration: InputDecoration(
                        labelText: 'License Type',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                      ),
                      items: _licenseTypes.map((license) => DropdownMenuItem(
                        value: license,
                        child: Text(license),
                      )).toList(),
                      onChanged: (value) => setState(() => _selectedLicenseType = value),
                      isExpanded: true,
                    ),
                  ],
                );
              } else {
                // Side by side on larger screens
                return Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedTargetAudience,
                        decoration: InputDecoration(
                          labelText: 'Target Audience',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                          filled: true,
                          fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                        ),
                        items: _targetAudiences.map((audience) => DropdownMenuItem(
                          value: audience,
                          child: Text(audience),
                        )).toList(),
                        onChanged: (value) => setState(() => _selectedTargetAudience = value),
                        isExpanded: true,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedLicenseType,
                        decoration: InputDecoration(
                          labelText: 'License Type',
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                          filled: true,
                          fillColor: isPremium ? AppTheme.premiumBlack : Colors.white,
                        ),
                        items: _licenseTypes.map((license) => DropdownMenuItem(
                          value: license,
                          child: Text(license),
                        )).toList(),
                        onChanged: (value) => setState(() => _selectedLicenseType = value),
                        isExpanded: true,
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(bool isPremium) {
    return FancyCard(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Settings',
            style: AppTheme.headingMedium.copyWith(
              color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Premium Template Setting
          Container(
            decoration: BoxDecoration(
              color: isPremium
                  ? AppTheme.premiumBlack.withValues(alpha: 0.3)
                  : AppTheme.lightGray.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isPremium ? AppTheme.premiumGold.withValues(alpha: 0.3) : AppTheme.primaryBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: SwitchListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              title: Text(
                'Premium Template',
                style: AppTheme.bodyMedium.copyWith(
                  color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                'Requires premium subscription to use',
                style: AppTheme.bodySmall.copyWith(
                  color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
                ),
              ),
              value: _isPremium,
              onChanged: (value) => setState(() => _isPremium = value),
              activeColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              activeTrackColor: isPremium
                  ? AppTheme.premiumGold.withValues(alpha: 0.3)
                  : AppTheme.primaryBlue.withValues(alpha: 0.3),
              inactiveThumbColor: isPremium ? AppTheme.premiumDarkGrey : Colors.grey[400],
              inactiveTrackColor: isPremium
                  ? AppTheme.premiumBlack.withValues(alpha: 0.5)
                  : Colors.grey[300],
            ),
          ),

          const SizedBox(height: 16),

          // Active Setting
          Container(
            decoration: BoxDecoration(
              color: isPremium
                  ? AppTheme.premiumBlack.withValues(alpha: 0.3)
                  : AppTheme.lightGray.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isPremium ? AppTheme.premiumGold.withValues(alpha: 0.3) : AppTheme.primaryBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: SwitchListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              title: Text(
                'Active Template',
                style: AppTheme.bodyMedium.copyWith(
                  color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                'Template is visible to users',
                style: AppTheme.bodySmall.copyWith(
                  color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
                ),
              ),
              value: _isActive,
              onChanged: (value) => setState(() => _isActive = value),
              activeColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              activeTrackColor: isPremium
                  ? AppTheme.premiumGold.withValues(alpha: 0.3)
                  : AppTheme.primaryBlue.withValues(alpha: 0.3),
              inactiveThumbColor: isPremium ? AppTheme.premiumDarkGrey : Colors.grey[400],
              inactiveTrackColor: isPremium
                  ? AppTheme.premiumBlack.withValues(alpha: 0.5)
                  : Colors.grey[300],
            ),
          ),

          const SizedBox(height: 8),

          // Additional info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isPremium
                  ? AppTheme.premiumGold.withValues(alpha: 0.1)
                  : AppTheme.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isPremium
                    ? AppTheme.premiumGold.withValues(alpha: 0.3)
                    : AppTheme.primaryBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Premium templates are only available to users with active subscriptions. Inactive templates are hidden from all users.',
                    style: AppTheme.bodySmall.copyWith(
                      color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: GradientButton(
              text: _isUploading
                  ? 'Uploading...'
                  : (widget.template == null ? 'Upload Template' : 'Update Template'),
              onPressed: _isUploading ? () {} : () => _uploadTemplate(),
              isLoading: _isUploading,
            ),
          ),
        ],
      ),
    );
  }
}
