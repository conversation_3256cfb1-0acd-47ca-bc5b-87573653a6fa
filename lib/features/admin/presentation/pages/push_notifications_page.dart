import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/widgets/fancy_card.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../domain/services/admin_notification_service.dart';
import '../../domain/entities/notification_model.dart';
import '../widgets/notification_stats_card.dart';
import '../widgets/create_notification_dialog.dart';

class PushNotificationsPage extends StatefulWidget {
  const PushNotificationsPage({super.key});

  @override
  State<PushNotificationsPage> createState() => _PushNotificationsPageState();
}

class _PushNotificationsPageState extends State<PushNotificationsPage> {
  final AdminNotificationService _notificationService = AdminNotificationService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppTheme.gradientAppBar(
        title: 'Push Notifications',
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.backgroundWhite,
              AppTheme.lightGradientBg,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Notification Methods Info Card
              _buildNotificationMethodsCard(),
              const SizedBox(height: 16),
              
              // User Segment Instructions Card
              _buildUserSegmentCard(),
              const SizedBox(height: 16),
              
              // Topic-Based Notifications Card
              _buildTopicNotificationsCard(),
              const SizedBox(height: 16),
              
              // Statistics Card
              _buildStatsCard(),
              const SizedBox(height: 16),
              
              // Recent Notifications
              _buildRecentNotifications(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationMethodsCard() {
    return FancyCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: AppTheme.primaryBlue),
              const SizedBox(width: 8),
              Text(
                'Notification Delivery Methods',
                style: AppTheme.headingSmall.copyWith(
                  color: AppTheme.primaryBlue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildMethodItem(
            icon: Icons.groups,
            title: 'User Segment (Recommended)',
            subtitle: 'Reaches ALL users via Firebase Console',
            color: Colors.green,
            isRecommended: true,
          ),
          const SizedBox(height: 8),
          _buildMethodItem(
            icon: Icons.topic,
            title: 'Topic-Based (This App)',
            subtitle: 'Reaches subscribed users only',
            color: AppTheme.primaryBlue,
            isRecommended: false,
          ),
        ],
      ),
    );
  }

  Widget _buildMethodItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required bool isRecommended,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: AppTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                    if (isRecommended) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          'BEST',
                          style: AppTheme.bodySmall.copyWith(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                Text(
                  subtitle,
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.secondaryText),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserSegmentCard() {
    return FancyCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.groups, color: Colors.green),
              const SizedBox(width: 8),
              Text(
                'User Segment Notifications',
                style: AppTheme.headingSmall.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Important note about User Segment limitations
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'User Segment notifications can ONLY be created through Firebase Console. No API exists for programmatic creation.',
                    style: AppTheme.bodySmall.copyWith(
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '✅ Reaches 100% of users (no topic subscription required)',
                  style: AppTheme.bodyMedium.copyWith(color: Colors.green.shade700),
                ),
                const SizedBox(height: 4),
                Text(
                  '✅ Uses Firebase Analytics audiences for targeting',
                  style: AppTheme.bodyMedium.copyWith(color: Colors.green.shade700),
                ),
                const SizedBox(height: 4),
                Text(
                  '✅ Best for critical announcements and updates',
                  style: AppTheme.bodyMedium.copyWith(color: Colors.green.shade700),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'How to send via User Segment:',
            style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          _buildStepItem('1', 'Open Firebase Console'),
          _buildStepItem('2', 'Go to Messaging → New notification'),
          _buildStepItem('3', 'Select "User segment" as target'),
          _buildStepItem('4', 'Choose audience (e.g., "All users")'),
          _buildStepItem('5', 'Compose and send notification'),
          const SizedBox(height: 16),
          GradientButton(
            text: 'Open Firebase Console',
            onPressed: () {
              _copyFirebaseConsoleUrl();
            },
            icon: const Icon(Icons.open_in_new),
          ),
        ],
      ),
    );
  }

  Widget _buildStepItem(String number, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: AppTheme.bodySmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: AppTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopicNotificationsCard() {
    return FancyCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.topic, color: AppTheme.primaryBlue),
              const SizedBox(width: 8),
              Text(
                'Topic-Based Notifications',
                style: AppTheme.headingSmall.copyWith(
                  color: AppTheme.primaryBlue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.primaryBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.primaryBlue.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '⚠️ Only reaches users subscribed to topics',
                  style: AppTheme.bodyMedium.copyWith(color: AppTheme.primaryBlue),
                ),
                const SizedBox(height: 4),
                Text(
                  '✅ Good for targeted campaigns and user segments',
                  style: AppTheme.bodyMedium.copyWith(color: AppTheme.primaryBlue),
                ),
                const SizedBox(height: 4),
                Text(
                  '✅ Programmatic control via this admin panel',
                  style: AppTheme.bodyMedium.copyWith(color: AppTheme.primaryBlue),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          GradientButton(
            text: 'Create Topic Notification',
            onPressed: () {
              _showCreateNotificationDialog();
            },
            icon: const Icon(Icons.add_circle_outline),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    return FutureBuilder<Map<String, int>>(
      future: _notificationService.getNotificationStats(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const FancyCard(
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final stats = snapshot.data ?? {};
        final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
        return NotificationStatsCard(
          stats: stats,
          isPremium: themeProvider.isPremium,
        );
      },
    );
  }

  Widget _buildRecentNotifications() {
    return FancyCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Topic Notifications',
            style: AppTheme.headingSmall.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          StreamBuilder<List<NotificationModel>>(
            stream: _notificationService.getNotifications(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Error loading notifications: ${snapshot.error}',
                    style: AppTheme.bodyMedium.copyWith(color: AppTheme.errorRed),
                  ),
                );
              }

              final notifications = snapshot.data ?? [];
              if (notifications.isEmpty) {
                return Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.notifications_none,
                        size: 48,
                        color: AppTheme.secondaryText,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'No topic notifications sent yet',
                        style: AppTheme.bodyMedium.copyWith(color: AppTheme.secondaryText),
                      ),
                    ],
                  ),
                );
              }

              return ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: notifications.take(5).length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final notification = notifications[index];
                  return _buildNotificationItem(notification);
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(NotificationModel notification) {
    Color statusColor;
    IconData statusIcon;
    
    switch (notification.status) {
      case NotificationStatus.sent:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case NotificationStatus.sending:
        statusColor = Colors.orange;
        statusIcon = Icons.schedule;
        break;
      case NotificationStatus.failed:
        statusColor = AppTheme.errorRed;
        statusIcon = Icons.error;
        break;
      case NotificationStatus.scheduled:
        statusColor = AppTheme.primaryBlue;
        statusIcon = Icons.schedule;
        break;
      default:
        statusColor = AppTheme.secondaryText;
        statusIcon = Icons.drafts;
    }

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(statusIcon, color: statusColor),
      title: Text(
        notification.title,
        style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            notification.body,
            style: AppTheme.bodySmall,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            'Target: ${_getTargetDescription(notification.target)} • ${_getStatusText(notification.status)}',
            style: AppTheme.bodySmall.copyWith(color: AppTheme.secondaryText),
          ),
        ],
      ),
      trailing: notification.status == NotificationStatus.draft
          ? IconButton(
              icon: const Icon(Icons.send),
              onPressed: () => _sendNotification(notification.id!),
            )
          : null,
    );
  }

  String _getTargetDescription(NotificationTarget target) {
    switch (target.type) {
      case NotificationTargetType.all:
        return 'All Users';
      case NotificationTargetType.userType:
        return 'User Types: ${target.userTypes.join(', ')}';
      case NotificationTargetType.premium:
        return 'Premium Users';
      case NotificationTargetType.specific:
        return '${target.specificUsers.length} Specific Users';
      case NotificationTargetType.topic:
        return 'Topics: ${target.topics.join(', ')}';
    }
  }

  String _getStatusText(NotificationStatus status) {
    switch (status) {
      case NotificationStatus.draft:
        return 'Draft';
      case NotificationStatus.scheduled:
        return 'Scheduled';
      case NotificationStatus.sending:
        return 'Sending...';
      case NotificationStatus.sent:
        return 'Sent';
      case NotificationStatus.failed:
        return 'Failed';
      case NotificationStatus.cancelled:
        return 'Cancelled';
    }
  }

  void _copyFirebaseConsoleUrl() {
    const url = 'https://console.firebase.google.com/project/quickposters-app/messaging';
    Clipboard.setData(const ClipboardData(text: url));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Firebase Console URL copied to clipboard'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'Open',
          textColor: Colors.white,
          onPressed: () {
            // In a real app, you might use url_launcher here
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Please paste the URL in your browser'),
                duration: Duration(seconds: 2),
              ),
            );
          },
        ),
      ),
    );
  }

  void _showCreateNotificationDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateNotificationDialog(
        onNotificationCreated: () {
          // Refresh the notifications list
          setState(() {});
        },
      ),
    );
  }

  Future<void> _sendNotification(String notificationId) async {
    setState(() => _isLoading = true);
    
    try {
      await _notificationService.sendNotification(notificationId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification sent successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send notification: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
} 