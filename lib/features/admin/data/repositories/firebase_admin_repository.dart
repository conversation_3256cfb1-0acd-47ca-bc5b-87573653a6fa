import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../domain/entities/admin_user.dart';
import '../../domain/entities/dashboard_stats.dart';
import '../../domain/repositories/admin_repository.dart';
import '../../../user/domain/entities/user_model.dart';
import '../../../user/domain/entities/business_parameter.dart';
import '../../../user/domain/entities/political_parameter.dart';
import '../../../user/domain/entities/party.dart';
import '../../../templates/domain/entities/template_item.dart';
import '../../../banners/domain/entities/banner_item.dart';
import '../../../../core/utils/logger.dart';

/// Firebase implementation of AdminRepository
class FirebaseAdminRepository implements AdminRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;

  FirebaseAdminRepository({
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance;

  @override
  Future<DashboardStats> getDashboardStats() async {
    try {
      // Get user statistics
      final usersSnapshot = await _firestore.collection('users').get();
      final users = usersSnapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();

      final totalUsers = users.length;
      final premiumUsers = users.where((u) => u.isPremium).length;
      final businessUsers = users.where((u) => u.userType == 'businessman').length;
      final politicianUsers = users.where((u) => u.userType == 'politician').length;
      final regularUsers = users.where((u) => u.userType == 'user' || u.userType == null).length;

      // Get active users (logged in within last 30 days)
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      final activeUsers = users.where((u) => u.updatedAt.isAfter(thirtyDaysAgo)).length;

      // Get new users today
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final newUsersToday = users.where((u) => u.createdAt.isAfter(startOfDay)).length;

      // Get templates count
      final templatesRef = _storage.ref().child('Templates');
      final templatesResult = await templatesRef.listAll();
      final totalTemplates = templatesResult.items.length;

      // Get banners count
      final bannersRef = _storage.ref().child('banners');
      final bannersResult = await bannersRef.listAll();
      final totalBanners = bannersResult.items.length;

      // Get business parameters count
      final businessParamsSnapshot = await _firestore.collection('bissness').get();
      final businessParameters = businessParamsSnapshot.docs.length;

      // Get political parameters count
      final politicalParamsSnapshot = await _firestore.collection('politicalParameters').get();
      final politicalParameters = politicalParamsSnapshot.docs.length;

      // Get political parties count
      final partiesSnapshot = await _firestore.collection('politicalParties').get();
      final politicalParties = partiesSnapshot.docs.length;

      return DashboardStats(
        totalUsers: totalUsers,
        premiumUsers: premiumUsers,
        regularUsers: regularUsers,
        businessUsers: businessUsers,
        politicianUsers: politicianUsers,
        totalTemplates: totalTemplates,
        totalBanners: totalBanners,
        activeUsers: activeUsers,
        newUsersToday: newUsersToday,
        businessParameters: businessParameters,
        politicalParameters: politicalParameters,
        politicalParties: politicalParties,
        // TODO: Implement revenue calculation when subscription system is ready
        totalRevenue: 0.0,
        postersCreatedToday: 0, // TODO: Implement when poster tracking is added
      );
    } catch (e) {
      AppLogger.error('Error getting dashboard stats', e);
      return const DashboardStats();
    }
  }

  @override
  Future<Map<String, dynamic>> getUserAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query query = _firestore.collection('users');

      if (startDate != null) {
        query = query.where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      if (endDate != null) {
        query = query.where('createdAt', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final snapshot = await query.get();
      final users = snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();

      // Calculate analytics
      final Map<String, int> usersByType = {};
      final Map<String, int> usersByMonth = {};
      int premiumCount = 0;

      for (final user in users) {
        // Count by type
        final type = user.userType ?? 'regular';
        usersByType[type] = (usersByType[type] ?? 0) + 1;

        // Count by month
        final monthKey = '${user.createdAt.year}-${user.createdAt.month.toString().padLeft(2, '0')}';
        usersByMonth[monthKey] = (usersByMonth[monthKey] ?? 0) + 1;

        // Count premium users
        if (user.isPremium) premiumCount++;
      }

      return {
        'totalUsers': users.length,
        'premiumUsers': premiumCount,
        'usersByType': usersByType,
        'usersByMonth': usersByMonth,
        'averageUsersPerMonth': usersByMonth.values.isNotEmpty
            ? usersByMonth.values.reduce((a, b) => a + b) / usersByMonth.length
            : 0,
      };
    } catch (e) {
      AppLogger.error('Error getting user analytics', e);
      return {};
    }
  }

  @override
  Future<Map<String, dynamic>> getContentAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Get template usage data (placeholder - implement when usage tracking is added)
      final templatesRef = _storage.ref().child('Templates');
      final templatesResult = await templatesRef.listAll();

      final bannersRef = _storage.ref().child('banners');
      final bannersResult = await bannersRef.listAll();

      return {
        'totalTemplates': templatesResult.items.length,
        'totalBanners': bannersResult.items.length,
        'templateUsage': {}, // TODO: Implement template usage tracking
        'bannerViews': {}, // TODO: Implement banner view tracking
      };
    } catch (e) {
      AppLogger.error('Error getting content analytics', e);
      return {};
    }
  }

  @override
  Future<List<UserModel>> getAllUsers({
    int limit = 20,
    String? lastUserId,
    String? searchQuery,
    String? userType,
    bool? isPremium,
  }) async {
    try {
      Query query = _firestore.collection('users').orderBy('createdAt', descending: true);

      // Apply filters
      if (userType != null) {
        query = query.where('userType', isEqualTo: userType);
      }
      if (isPremium != null) {
        query = query.where('isPremium', isEqualTo: isPremium);
      }

      // Apply pagination
      if (lastUserId != null) {
        final lastDoc = await _firestore.collection('users').doc(lastUserId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      List<UserModel> users = snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();

      // Apply search filter (client-side for now)
      if (searchQuery != null && searchQuery.isNotEmpty) {
        users = users.where((user) {
          return user.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
                 (user.email?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
                 (user.phoneNumber?.contains(searchQuery) ?? false);
        }).toList();
      }

      return users;
    } catch (e) {
      AppLogger.error('Error getting all users', e);
      return [];
    }
  }

  @override
  Future<UserModel?> getUserById(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      AppLogger.error('Error getting user by ID', e);
      return null;
    }
  }

  @override
  Future<void> updateUser(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.uid).update(user.toFirestore());
    } catch (e) {
      AppLogger.error('Error updating user', e);
      throw Exception('Failed to update user: $e');
    }
  }

  @override
  Future<void> deleteUser(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).delete();
    } catch (e) {
      AppLogger.error('Error deleting user', e);
      throw Exception('Failed to delete user: $e');
    }
  }

  @override
  Future<void> toggleUserPremium(String userId, bool isPremium) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'isPremium': isPremium,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      AppLogger.error('Error toggling user premium status', e);
      throw Exception('Failed to toggle user premium status: $e');
    }
  }

  @override
  Future<Map<String, int>> getUsersCountByType() async {
    try {
      final snapshot = await _firestore.collection('users').get();
      final users = snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();

      final Map<String, int> counts = {
        'total': users.length,
        'premium': 0,
        'regular': 0,
        'businessman': 0,
        'politician': 0,
      };

      for (final user in users) {
        if (user.isPremium) counts['premium'] = counts['premium']! + 1;

        final type = user.userType ?? 'regular';
        if (type == 'businessman') {
          counts['businessman'] = counts['businessman']! + 1;
        } else if (type == 'politician') {
          counts['politician'] = counts['politician']! + 1;
        } else {
          counts['regular'] = counts['regular']! + 1;
        }
      }

      return counts;
    } catch (e) {
      AppLogger.error('Error getting users count by type', e);
      return {};
    }
  }

  // Admin Management methods will be implemented in the next part
  @override
  Future<List<AdminUser>> getAllAdmins() async {
    // TODO: Implement admin user management
    return [];
  }

  @override
  Future<void> createAdmin(AdminUser admin) async {
    // TODO: Implement admin creation
  }

  @override
  Future<void> updateAdmin(AdminUser admin) async {
    // TODO: Implement admin update
  }

  @override
  Future<void> removeAdmin(String userId) async {
    // TODO: Implement admin removal
  }

  @override
  Future<bool> isUserAdmin(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        final user = UserModel.fromFirestore(doc);
        return user.isAdmin;
      }
      return false;
    } catch (e) {
      AppLogger.error('Error checking if user is admin', e);
      return false;
    }
  }

  // Content Management methods
  @override
  Future<List<TemplateItem>> getAllTemplates({
    int limit = 20,
    String? lastTemplateId,
  }) async {
    try {
      // Simple query without ordering to avoid index requirement
      Query query = _firestore.collection('templates').limit(limit * 2); // Get more to filter

      final querySnapshot = await query.get();

      // Filter active templates and sort in code
      final templates = querySnapshot.docs
          .map((doc) => TemplateItem.fromFirestore(doc))
          .where((template) => template.isActive)
          .toList();

      // Sort by upload date (newest first)
      templates.sort((a, b) => b.uploadDate.compareTo(a.uploadDate));

      // Apply limit after filtering and sorting
      return templates.take(limit).toList();
    } catch (e) {
      AppLogger.error('Error getting templates', e);
      return [];
    }
  }

  @override
  Future<String> uploadTemplate(String filePath, String fileName) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        throw Exception('File does not exist');
      }

      // Create storage reference
      final storageRef = _storage.ref().child('Templates').child(fileName);

      // Upload file
      final uploadTask = await storageRef.putFile(
        file,
        SettableMetadata(contentType: 'image/jpeg'),
      );

      // Get download URL
      final downloadUrl = await uploadTask.ref.getDownloadURL();

      // Get file size
      final fileSize = await file.length();

      // Create template document in Firestore
      final templateData = TemplateItem(
        id: '', // Will be set by Firestore
        imageUrl: downloadUrl,
        name: fileName.split('.').first.replaceAll('_', ' '),
        description: 'Template uploaded via admin panel',
        category: 'General',
        subCategory: null,
        isPremium: false,
        tags: ['admin-upload'],
        layoutType: 'Vertical',
        targetAudience: 'General',
        designStyle: 'Modern',
        colors: ['Multi-color'],
        fontStyles: ['Sans-serif'],
        usageType: 'Social Media',
        resolution: 'High',
        licenseType: 'Free for personal use',
        uploadDate: DateTime.now(),
        creator: 'Admin',
        fileFormats: [fileName.split('.').last.toUpperCase()],
        downloadCount: 0,
        viewCount: 0,
        ratingCount: 0,
        isActive: true,
        dimensions: '1080x1080',
        fileSize: fileSize,
        uploadedBy: 'admin', // TODO: Get actual admin user ID
      );

      // Add to Firestore
      await _firestore.collection('templates').add(templateData.toFirestore());

      AppLogger.info('Template uploaded successfully: $fileName');
      return downloadUrl;
    } catch (e) {
      AppLogger.error('Error uploading template', e);
      throw Exception('Failed to upload template: $e');
    }
  }

  @override
  Future<void> updateTemplate(TemplateItem template) async {
    try {
      if (template.id.isEmpty) {
        throw Exception('Template ID is required for update');
      }

      // Update template document in Firestore
      await _firestore.collection('templates').doc(template.id).update(
        template.copyWith(lastModified: DateTime.now()).toFirestore(),
      );

      AppLogger.info('Template updated successfully: ${template.id}');
    } catch (e) {
      AppLogger.error('Error updating template', e);
      throw Exception('Failed to update template: $e');
    }
  }

  @override
  Future<void> deleteTemplate(String templateId) async {
    try {
      // Get template document
      final templateDoc = await _firestore.collection('templates').doc(templateId).get();

      if (!templateDoc.exists) {
        throw Exception('Template not found in database');
      }

      final template = TemplateItem.fromFirestore(templateDoc);

      // Delete from Firebase Storage
      try {
        final storageRef = _storage.refFromURL(template.imageUrl);
        await storageRef.delete();
        AppLogger.info('Template file deleted from storage: ${template.imageUrl}');
      } catch (storageError) {
        AppLogger.warning('Could not delete file from storage: $storageError');
        // Continue with Firestore deletion even if storage deletion fails
      }

      // Delete from Firestore
      await _firestore.collection('templates').doc(templateId).delete();

      AppLogger.info('Template deleted successfully: $templateId');
    } catch (e) {
      AppLogger.error('Error deleting template', e);
      throw Exception('Failed to delete template: $e');
    }
  }

  @override
  Future<List<BannerItem>> getAllBanners({
    int limit = 20,
    String? lastBannerId,
  }) async {
    try {
      final storageRef = _storage.ref().child('banners');
      final result = await storageRef.list(ListOptions(maxResults: limit));

      List<BannerItem> banners = [];
      for (var item in result.items) {
        final url = await item.getDownloadURL();
        banners.add(BannerItem.fromStorageUrl(url));
      }

      return banners;
    } catch (e) {
      AppLogger.error('Error getting banners', e);
      return [];
    }
  }

  @override
  Future<String> uploadBanner(String filePath, String fileName) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        throw Exception('File does not exist');
      }

      // Create storage reference
      final storageRef = _storage.ref().child('banners').child(fileName);

      // Upload file
      await storageRef.putFile(
        file,
        SettableMetadata(contentType: 'image/jpeg'),
      );

      // Get download URL
      final downloadUrl = await storageRef.getDownloadURL();

      AppLogger.info('Banner uploaded successfully: $fileName');
      return downloadUrl;
    } catch (e) {
      AppLogger.error('Error uploading banner', e);
      throw Exception('Failed to upload banner: $e');
    }
  }

  @override
  Future<void> deleteBanner(String bannerId) async {
    try {
      // Find and delete the banner file from storage
      final storageRef = _storage.ref().child('banners');
      final result = await storageRef.list();

      for (var item in result.items) {
        final url = await item.getDownloadURL();
        final banner = BannerItem.fromStorageUrl(url);
        if (banner.id == bannerId) {
          await item.delete();
          AppLogger.info('Banner deleted successfully: $bannerId');
          return;
        }
      }

      throw Exception('Banner not found');
    } catch (e) {
      AppLogger.error('Error deleting banner', e);
      throw Exception('Failed to delete banner: $e');
    }
  }

  // Parameter Management methods will be implemented in the next part
  @override
  Future<List<BusinessParameter>> getAllBusinessParameters() async {
    // TODO: Implement business parameter management
    return [];
  }

  @override
  Future<void> createBusinessParameter(BusinessParameter parameter) async {
    // TODO: Implement business parameter creation
  }

  @override
  Future<void> updateBusinessParameter(BusinessParameter parameter) async {
    // TODO: Implement business parameter update
  }

  @override
  Future<void> deleteBusinessParameter(String parameterId) async {
    // TODO: Implement business parameter deletion
  }

  @override
  Future<List<PoliticalParameter>> getAllPoliticalParameters() async {
    // TODO: Implement political parameter management
    return [];
  }

  @override
  Future<void> createPoliticalParameter(PoliticalParameter parameter) async {
    // TODO: Implement political parameter creation
  }

  @override
  Future<void> updatePoliticalParameter(PoliticalParameter parameter) async {
    // TODO: Implement political parameter update
  }

  @override
  Future<void> deletePoliticalParameter(String parameterId) async {
    // TODO: Implement political parameter deletion
  }

  // Party Management methods will be implemented in the next part
  @override
  Future<List<Party>> getAllPoliticalParties() async {
    // TODO: Implement political party management
    return [];
  }

  @override
  Future<void> createPoliticalParty(Party party) async {
    // TODO: Implement political party creation
  }

  @override
  Future<void> updatePoliticalParty(Party party) async {
    // TODO: Implement political party update
  }

  @override
  Future<void> deletePoliticalParty(String partyId) async {
    // TODO: Implement political party deletion
  }

  // System Operations methods will be implemented in the next part
  @override
  Future<void> backupData() async {
    // TODO: Implement data backup
  }

  @override
  Future<Map<String, dynamic>> getSystemHealth() async {
    // TODO: Implement system health check
    return {};
  }

  @override
  Future<void> sendBroadcastNotification(String title, String message) async {
    // TODO: Implement broadcast notification
  }

  @override
  Future<Map<String, dynamic>> getAppUsageStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // TODO: Implement app usage statistics
    return {};
  }
}
