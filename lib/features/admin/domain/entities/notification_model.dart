import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationModel {
  final String id;
  final String title;
  final String body;
  final String? imageUrl;
  final Map<String, dynamic> data;
  final NotificationTarget target;
  final NotificationStatus status;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  final DateTime? sentAt;
  final String createdBy;
  final int? recipientCount;
  final int? deliveredCount;
  final int? openedCount;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    this.imageUrl,
    required this.data,
    required this.target,
    required this.status,
    required this.createdAt,
    this.scheduledAt,
    this.sentAt,
    required this.createdBy,
    this.recipientCount,
    this.deliveredCount,
    this.openedCount,
  });

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel(
      id: doc.id,
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      imageUrl: data['imageUrl'],
      data: Map<String, dynamic>.from(data['data'] ?? {}),
      target: NotificationTarget.fromMap(data['target'] ?? {}),
      status: NotificationStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => NotificationStatus.draft,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      scheduledAt: data['scheduledAt'] != null 
          ? (data['scheduledAt'] as Timestamp).toDate() 
          : null,
      sentAt: data['sentAt'] != null 
          ? (data['sentAt'] as Timestamp).toDate() 
          : null,
      createdBy: data['createdBy'] ?? '',
      recipientCount: data['recipientCount'],
      deliveredCount: data['deliveredCount'],
      openedCount: data['openedCount'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'body': body,
      'imageUrl': imageUrl,
      'data': data,
      'target': target.toMap(),
      'status': status.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'scheduledAt': scheduledAt != null ? Timestamp.fromDate(scheduledAt!) : null,
      'sentAt': sentAt != null ? Timestamp.fromDate(sentAt!) : null,
      'createdBy': createdBy,
      'recipientCount': recipientCount,
      'deliveredCount': deliveredCount,
      'openedCount': openedCount,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    String? imageUrl,
    Map<String, dynamic>? data,
    NotificationTarget? target,
    NotificationStatus? status,
    DateTime? createdAt,
    DateTime? scheduledAt,
    DateTime? sentAt,
    String? createdBy,
    int? recipientCount,
    int? deliveredCount,
    int? openedCount,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      imageUrl: imageUrl ?? this.imageUrl,
      data: data ?? this.data,
      target: target ?? this.target,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      sentAt: sentAt ?? this.sentAt,
      createdBy: createdBy ?? this.createdBy,
      recipientCount: recipientCount ?? this.recipientCount,
      deliveredCount: deliveredCount ?? this.deliveredCount,
      openedCount: openedCount ?? this.openedCount,
    );
  }
}

class NotificationTarget {
  final NotificationTargetType type;
  final List<String> userTypes;
  final List<String> specificUsers;
  final bool includePremium;
  final bool includeRegular;
  final List<String> topics;

  NotificationTarget({
    required this.type,
    this.userTypes = const [],
    this.specificUsers = const [],
    this.includePremium = true,
    this.includeRegular = true,
    this.topics = const [],
  });

  factory NotificationTarget.fromMap(Map<String, dynamic> map) {
    return NotificationTarget(
      type: NotificationTargetType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => NotificationTargetType.all,
      ),
      userTypes: List<String>.from(map['userTypes'] ?? []),
      specificUsers: List<String>.from(map['specificUsers'] ?? []),
      includePremium: map['includePremium'] ?? true,
      includeRegular: map['includeRegular'] ?? true,
      topics: List<String>.from(map['topics'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'userTypes': userTypes,
      'specificUsers': specificUsers,
      'includePremium': includePremium,
      'includeRegular': includeRegular,
      'topics': topics,
    };
  }
}

enum NotificationTargetType {
  all,
  userType,
  specific,
  premium,
  topic,
}

enum NotificationStatus {
  draft,
  scheduled,
  sending,
  sent,
  failed,
  cancelled,
}

enum NotificationType {
  announcement,
  newTemplate,
  newBanner,
  promotion,
  update,
  reminder,
} 