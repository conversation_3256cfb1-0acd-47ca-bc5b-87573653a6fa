import 'package:equatable/equatable.dart';
import '../../../user/domain/entities/user_model.dart';

/// Model class representing an admin user with additional admin-specific data
class AdminUser extends Equatable {
  /// The base user model
  final UserModel user;

  /// Admin role level (super_admin, admin, moderator)
  final String role;

  /// List of permissions granted to this admin
  final List<String> permissions;

  /// When the admin privileges were granted
  final DateTime adminSince;

  /// Who granted the admin privileges (admin user ID)
  final String? grantedBy;

  /// Whether the admin account is currently active
  final bool isActive;

  /// Last login timestamp
  final DateTime? lastLogin;

  /// Number of actions performed by this admin
  final int actionsCount;

  const AdminUser({
    required this.user,
    this.role = 'admin',
    this.permissions = const [],
    required this.adminSince,
    this.grantedBy,
    this.isActive = true,
    this.lastLogin,
    this.actionsCount = 0,
  });

  /// Create an admin user from a regular user
  factory AdminUser.fromUser({
    required UserModel user,
    String role = 'admin',
    List<String> permissions = const [],
    String? grantedBy,
  }) {
    return AdminUser(
      user: user.copyWith(isAdmin: true),
      role: role,
      permissions: permissions,
      adminSince: DateTime.now(),
      grantedBy: grantedBy,
      isActive: true,
      actionsCount: 0,
    );
  }

  /// Get default permissions for a role
  static List<String> getDefaultPermissions(String role) {
    switch (role.toLowerCase()) {
      case 'super_admin':
        return [
          'manage_users',
          'manage_admins',
          'manage_content',
          'manage_templates',
          'manage_banners',
          'manage_parameters',
          'manage_parties',
          'view_analytics',
          'manage_subscriptions',
          'system_settings',
        ];
      case 'admin':
        return [
          'manage_users',
          'manage_content',
          'manage_templates',
          'manage_banners',
          'manage_parameters',
          'manage_parties',
          'view_analytics',
        ];
      case 'moderator':
        return [
          'manage_content',
          'manage_templates',
          'manage_banners',
          'view_analytics',
        ];
      default:
        return [];
    }
  }

  /// Check if admin has a specific permission
  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  /// Check if admin can perform an action
  bool canPerform(String action) {
    if (!isActive) return false;
    return hasPermission(action);
  }

  /// Create a copy with updated values
  AdminUser copyWith({
    UserModel? user,
    String? role,
    List<String>? permissions,
    DateTime? adminSince,
    String? grantedBy,
    bool? isActive,
    DateTime? lastLogin,
    int? actionsCount,
  }) {
    return AdminUser(
      user: user ?? this.user,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      adminSince: adminSince ?? this.adminSince,
      grantedBy: grantedBy ?? this.grantedBy,
      isActive: isActive ?? this.isActive,
      lastLogin: lastLogin ?? this.lastLogin,
      actionsCount: actionsCount ?? this.actionsCount,
    );
  }

  /// Convert to map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'user': user.toFirestore(),
      'role': role,
      'permissions': permissions,
      'adminSince': adminSince.toIso8601String(),
      'grantedBy': grantedBy,
      'isActive': isActive,
      'lastLogin': lastLogin?.toIso8601String(),
      'actionsCount': actionsCount,
    };
  }

  /// Create from map
  factory AdminUser.fromMap(Map<String, dynamic> map, String uid) {
    // Create UserModel directly from the user data map
    final userData = map['user'] as Map<String, dynamic>;
    final user = UserModel(
      uid: uid,
      name: userData['name'] ?? '',
      email: userData['email'],
      phoneNumber: userData['phoneNumber'],
      photoUrl: userData['photoUrl'],
      createdAt: userData['createdAt'] != null
          ? DateTime.parse(userData['createdAt'])
          : DateTime.now(),
      updatedAt: userData['updatedAt'] != null
          ? DateTime.parse(userData['updatedAt'])
          : DateTime.now(),
      isProfileComplete: userData['isProfileComplete'] ?? false,
      languageCode: userData['languageCode'] ?? 'en',
      posterIds: List<String>.from(userData['posterIds'] ?? []),
      userType: userData['userType'],
      isPremium: userData['isPremium'] ?? false,
      isAdmin: userData['isAdmin'] ?? false,
    );

    return AdminUser(
      user: user,
      role: map['role'] ?? 'admin',
      permissions: List<String>.from(map['permissions'] ?? []),
      adminSince: DateTime.parse(map['adminSince']),
      grantedBy: map['grantedBy'],
      isActive: map['isActive'] ?? true,
      lastLogin: map['lastLogin'] != null ? DateTime.parse(map['lastLogin']) : null,
      actionsCount: map['actionsCount'] ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        user,
        role,
        permissions,
        adminSince,
        grantedBy,
        isActive,
        lastLogin,
        actionsCount,
      ];
}
