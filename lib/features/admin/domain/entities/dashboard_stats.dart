import 'package:equatable/equatable.dart';

/// Model class representing dashboard statistics for admin
class DashboardStats extends Equatable {
  /// Total number of users
  final int totalUsers;

  /// Number of premium users
  final int premiumUsers;

  /// Number of regular users
  final int regularUsers;

  /// Number of business users
  final int businessUsers;

  /// Number of politician users
  final int politicianUsers;

  /// Total number of templates
  final int totalTemplates;

  /// Total number of banners
  final int totalBanners;

  /// Number of active users (logged in within last 30 days)
  final int activeUsers;

  /// Total revenue from subscriptions
  final double totalRevenue;

  /// Number of posters created today
  final int postersCreatedToday;

  /// Number of new users registered today
  final int newUsersToday;

  /// Number of business parameters
  final int businessParameters;

  /// Number of political parameters
  final int politicalParameters;

  /// Number of political parties
  final int politicalParties;

  const DashboardStats({
    this.totalUsers = 0,
    this.premiumUsers = 0,
    this.regularUsers = 0,
    this.businessUsers = 0,
    this.politicianUsers = 0,
    this.totalTemplates = 0,
    this.totalBanners = 0,
    this.activeUsers = 0,
    this.totalRevenue = 0.0,
    this.postersCreatedToday = 0,
    this.newUsersToday = 0,
    this.businessParameters = 0,
    this.politicalParameters = 0,
    this.politicalParties = 0,
  });

  /// Create a copy with updated values
  DashboardStats copyWith({
    int? totalUsers,
    int? premiumUsers,
    int? regularUsers,
    int? businessUsers,
    int? politicianUsers,
    int? totalTemplates,
    int? totalBanners,
    int? activeUsers,
    double? totalRevenue,
    int? postersCreatedToday,
    int? newUsersToday,
    int? businessParameters,
    int? politicalParameters,
    int? politicalParties,
  }) {
    return DashboardStats(
      totalUsers: totalUsers ?? this.totalUsers,
      premiumUsers: premiumUsers ?? this.premiumUsers,
      regularUsers: regularUsers ?? this.regularUsers,
      businessUsers: businessUsers ?? this.businessUsers,
      politicianUsers: politicianUsers ?? this.politicianUsers,
      totalTemplates: totalTemplates ?? this.totalTemplates,
      totalBanners: totalBanners ?? this.totalBanners,
      activeUsers: activeUsers ?? this.activeUsers,
      totalRevenue: totalRevenue ?? this.totalRevenue,
      postersCreatedToday: postersCreatedToday ?? this.postersCreatedToday,
      newUsersToday: newUsersToday ?? this.newUsersToday,
      businessParameters: businessParameters ?? this.businessParameters,
      politicalParameters: politicalParameters ?? this.politicalParameters,
      politicalParties: politicalParties ?? this.politicalParties,
    );
  }

  /// Convert to map for JSON serialization
  Map<String, dynamic> toMap() {
    return {
      'totalUsers': totalUsers,
      'premiumUsers': premiumUsers,
      'regularUsers': regularUsers,
      'businessUsers': businessUsers,
      'politicianUsers': politicianUsers,
      'totalTemplates': totalTemplates,
      'totalBanners': totalBanners,
      'activeUsers': activeUsers,
      'totalRevenue': totalRevenue,
      'postersCreatedToday': postersCreatedToday,
      'newUsersToday': newUsersToday,
      'businessParameters': businessParameters,
      'politicalParameters': politicalParameters,
      'politicalParties': politicalParties,
    };
  }

  /// Create from map
  factory DashboardStats.fromMap(Map<String, dynamic> map) {
    return DashboardStats(
      totalUsers: map['totalUsers']?.toInt() ?? 0,
      premiumUsers: map['premiumUsers']?.toInt() ?? 0,
      regularUsers: map['regularUsers']?.toInt() ?? 0,
      businessUsers: map['businessUsers']?.toInt() ?? 0,
      politicianUsers: map['politicianUsers']?.toInt() ?? 0,
      totalTemplates: map['totalTemplates']?.toInt() ?? 0,
      totalBanners: map['totalBanners']?.toInt() ?? 0,
      activeUsers: map['activeUsers']?.toInt() ?? 0,
      totalRevenue: map['totalRevenue']?.toDouble() ?? 0.0,
      postersCreatedToday: map['postersCreatedToday']?.toInt() ?? 0,
      newUsersToday: map['newUsersToday']?.toInt() ?? 0,
      businessParameters: map['businessParameters']?.toInt() ?? 0,
      politicalParameters: map['politicalParameters']?.toInt() ?? 0,
      politicalParties: map['politicalParties']?.toInt() ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        totalUsers,
        premiumUsers,
        regularUsers,
        businessUsers,
        politicianUsers,
        totalTemplates,
        totalBanners,
        activeUsers,
        totalRevenue,
        postersCreatedToday,
        newUsersToday,
        businessParameters,
        politicalParameters,
        politicalParties,
      ];
}
