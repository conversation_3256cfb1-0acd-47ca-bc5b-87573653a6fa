import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:http/http.dart' as http;
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter/services.dart';
import '../entities/notification_model.dart';

class AdminNotificationService {
  static final AdminNotificationService _instance = AdminNotificationService._internal();
  factory AdminNotificationService() => _instance;
  AdminNotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ========================================
  // 📢 NOTIFICATION DELIVERY METHODS:
  // ========================================
  // 1. TOPIC-BASED (Current Implementation):
  //    - Sends to users subscribed to FCM topics
  //    - Reliable for subscribed users only
  //    - Used by this admin service
  //
  // 2. USER SEGMENT (Recommended for 100% reach):
  //    - Uses Firebase Console → Messaging → New notification → User segment
  //    - Leverages Firebase Analytics audiences
  //    - Reaches ALL users regardless of topic subscription
  //    - More reliable for critical announcements
  //
  // 💡 For maximum reach, use Firebase Console with User Segments
  //    for important notifications, and this service for targeted campaigns.
  // ========================================

  // Firebase project ID - replace with your actual project ID
  static const String _projectId = 'quickposters-app';
  
  // FCM HTTP v1 API endpoint
  static const String _fcmUrl = 'https://fcm.googleapis.com/v1/projects/$_projectId/messages:send';
  
  // Service account credentials for OAuth 2.0
  static const List<String> _scopes = ['https://www.googleapis.com/auth/firebase.messaging'];

  // Create a new notification
  Future<String> createNotification(NotificationModel notification) async {
    try {
      print('🔵 [AdminNotificationService] Creating notification: ${notification.title}');
      final docRef = await _firestore.collection('admin_notifications').add(notification.toFirestore());
      print('✅ [AdminNotificationService] Notification created with ID: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to create notification: $e');
      throw Exception('Failed to create notification: $e');
    }
  }

  // Update notification
  Future<void> updateNotification(String id, NotificationModel notification) async {
    try {
      print('🔵 [AdminNotificationService] Updating notification: $id');
      await _firestore.collection('admin_notifications').doc(id).update(notification.toFirestore());
      print('✅ [AdminNotificationService] Notification updated: $id');
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to update notification: $e');
      throw Exception('Failed to update notification: $e');
    }
  }

  // Delete notification
  Future<void> deleteNotification(String id) async {
    try {
      print('🔵 [AdminNotificationService] Deleting notification: $id');
      await _firestore.collection('admin_notifications').doc(id).delete();
      print('✅ [AdminNotificationService] Notification deleted: $id');
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to delete notification: $e');
      throw Exception('Failed to delete notification: $e');
    }
  }

  // Get all notifications
  Stream<List<NotificationModel>> getNotifications() {
    print('🔵 [AdminNotificationService] Getting notifications stream');
    return _firestore
        .collection('admin_notifications')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          print('📊 [AdminNotificationService] Received ${snapshot.docs.length} notifications from Firestore');
          return snapshot.docs
              .map((doc) => NotificationModel.fromFirestore(doc))
              .toList();
        });
  }

  // Get notification by ID
  Future<NotificationModel?> getNotificationById(String id) async {
    try {
      print('🔵 [AdminNotificationService] Getting notification by ID: $id');
      final doc = await _firestore.collection('admin_notifications').doc(id).get();
      if (doc.exists) {
        print('✅ [AdminNotificationService] Notification found: $id');
        return NotificationModel.fromFirestore(doc);
      }
      print('⚠️ [AdminNotificationService] Notification not found: $id');
      return null;
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to get notification: $e');
      throw Exception('Failed to get notification: $e');
    }
  }

  // Load service account credentials from assets
  Future<Map<String, dynamic>> _loadServiceAccountCredentials() async {
    try {
      print('🔵 [AdminNotificationService] Loading service account credentials from assets');
      // Try to load from assets/firebase_service_account.json
      final String credentialsJson = await rootBundle.loadString('assets/firebase_service_account.json');
      final credentials = jsonDecode(credentialsJson);
      print('✅ [AdminNotificationService] Service account credentials loaded successfully');
      print('📋 [AdminNotificationService] Project ID from credentials: ${credentials['project_id']}');
      print('📋 [AdminNotificationService] Client email: ${credentials['client_email']}');
      return credentials;
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to load service account credentials: $e');
      // If assets file doesn't exist, provide a template for manual configuration
      throw Exception(
        'Service account credentials not found. Please:\n'
        '1. Download your Firebase service account key from Firebase Console\n'
        '2. Save it as assets/firebase_service_account.json\n'
        '3. Add it to pubspec.yaml under assets:\n'
        '   assets:\n'
        '     - assets/firebase_service_account.json\n'
        '4. Or implement environment variable loading\n'
        'Error: $e'
      );
    }
  }

  // Get OAuth 2.0 access token for FCM HTTP v1 API
  Future<String> _getAccessToken() async {
    try {
      print('🔵 [AdminNotificationService] Getting OAuth 2.0 access token');
      // Load service account credentials
      final serviceAccountCredentials = await _loadServiceAccountCredentials();

      // Create service account credentials
      final accountCredentials = ServiceAccountCredentials.fromJson(serviceAccountCredentials);
      print('✅ [AdminNotificationService] Service account credentials created');
      
      // Get authenticated client
      print('🔵 [AdminNotificationService] Creating authenticated client with scopes: $_scopes');
      final client = await clientViaServiceAccount(accountCredentials, _scopes);
      print('✅ [AdminNotificationService] Authenticated client created');
      
      // Get access token
      final accessToken = client.credentials.accessToken.data;
      print('✅ [AdminNotificationService] Access token obtained (length: ${accessToken.length})');
      print('📋 [AdminNotificationService] Token expires at: ${client.credentials.accessToken.expiry}');
      
      // Close the client
      client.close();
      
      return accessToken;
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to get access token: $e');
      throw Exception('Failed to get access token: $e');
    }
  }

  // Send notification immediately
  Future<void> sendNotification(String notificationId) async {
    try {
      print('🚀 [AdminNotificationService] Starting to send notification: $notificationId');
      final notification = await getNotificationById(notificationId);
      if (notification == null) {
        throw Exception('Notification not found');
      }

      print('📋 [AdminNotificationService] Notification details:');
      print('   Title: ${notification.title}');
      print('   Body: ${notification.body}');
      print('   Target Type: ${notification.target.type}');
      print('   Image URL: ${notification.imageUrl ?? 'None'}');

      // Update status to sending
      print('🔵 [AdminNotificationService] Updating status to sending');
      await updateNotification(notificationId, notification.copyWith(
        status: NotificationStatus.sending,
      ));

      // Get target users
      print('🔵 [AdminNotificationService] Getting target users');
      final targetUsers = await _getTargetUsers(notification.target);
      print('📊 [AdminNotificationService] Found ${targetUsers.length} target users');
      
      // Send notification based on target type
      int deliveredCount = 0;
      
      print('🔵 [AdminNotificationService] Sending based on target type: ${notification.target.type}');
      switch (notification.target.type) {
        case NotificationTargetType.all:
          print('📡 [AdminNotificationService] Sending to all users via topic: all_users');
          deliveredCount = await _sendToAllUsers(notification);
          break;
        case NotificationTargetType.userType:
          print('📡 [AdminNotificationService] Sending to user types: ${notification.target.userTypes}');
          deliveredCount = await _sendToUserTypes(notification, notification.target.userTypes);
          break;
        case NotificationTargetType.specific:
          print('📡 [AdminNotificationService] Sending to specific users: ${notification.target.specificUsers.length} users');
          deliveredCount = await _sendToSpecificUsers(notification, notification.target.specificUsers);
          break;
        case NotificationTargetType.premium:
          print('📡 [AdminNotificationService] Sending to premium users via topic: premium_users');
          deliveredCount = await _sendToPremiumUsers(notification);
          break;
        case NotificationTargetType.topic:
          print('📡 [AdminNotificationService] Sending to topics: ${notification.target.topics}');
          deliveredCount = await _sendToTopics(notification, notification.target.topics);
          break;
      }

      print('📊 [AdminNotificationService] Delivery completed. Delivered count: $deliveredCount');

      // Update notification status
      print('🔵 [AdminNotificationService] Updating final status to sent');
      await updateNotification(notificationId, notification.copyWith(
        status: NotificationStatus.sent,
        sentAt: DateTime.now(),
        recipientCount: targetUsers.length,
        deliveredCount: deliveredCount,
      ));

      print('✅ [AdminNotificationService] Notification sending process completed successfully');

    } catch (e) {
      print('❌ [AdminNotificationService] Failed to send notification: $e');
      // Update status to failed
      final notification = await getNotificationById(notificationId);
      if (notification != null) {
        await updateNotification(notificationId, notification.copyWith(
          status: NotificationStatus.failed,
        ));
      }
      throw Exception('Failed to send notification: $e');
    }
  }

  // Schedule notification
  Future<void> scheduleNotification(String notificationId, DateTime scheduledTime) async {
    try {
      print('🔵 [AdminNotificationService] Scheduling notification: $notificationId for $scheduledTime');
      final notification = await getNotificationById(notificationId);
      if (notification == null) {
        throw Exception('Notification not found');
      }

      await updateNotification(notificationId, notification.copyWith(
        status: NotificationStatus.scheduled,
        scheduledAt: scheduledTime,
      ));

      print('✅ [AdminNotificationService] Notification scheduled successfully');
      // TODO: Implement actual scheduling logic (could use Cloud Functions or a job scheduler)
      // For now, we'll just update the status
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to schedule notification: $e');
      throw Exception('Failed to schedule notification: $e');
    }
  }

  // Cancel scheduled notification
  Future<void> cancelNotification(String notificationId) async {
    try {
      print('🔵 [AdminNotificationService] Cancelling notification: $notificationId');
      final notification = await getNotificationById(notificationId);
      if (notification == null) {
        throw Exception('Notification not found');
      }

      await updateNotification(notificationId, notification.copyWith(
        status: NotificationStatus.cancelled,
      ));
      print('✅ [AdminNotificationService] Notification cancelled successfully');
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to cancel notification: $e');
      throw Exception('Failed to cancel notification: $e');
    }
  }

  // Get target users based on notification target
  Future<List<String>> _getTargetUsers(NotificationTarget target) async {
    List<String> userIds = [];

    print('🔵 [AdminNotificationService] Getting target users for type: ${target.type}');

    switch (target.type) {
      case NotificationTargetType.all:
        print('📊 [AdminNotificationService] Querying all users from Firestore');
        final usersQuery = await _firestore.collection('users').get();
        userIds = usersQuery.docs.map((doc) => doc.id).toList();
        print('📊 [AdminNotificationService] Found ${userIds.length} total users');
        break;

      case NotificationTargetType.userType:
        print('📊 [AdminNotificationService] Querying users by types: ${target.userTypes}');
        for (String userType in target.userTypes) {
          final usersQuery = await _firestore
              .collection('users')
              .where('userType', isEqualTo: userType)
              .get();
          final typeUsers = usersQuery.docs.map((doc) => doc.id).toList();
          print('📊 [AdminNotificationService] Found ${typeUsers.length} users of type: $userType');
          userIds.addAll(typeUsers);
        }
        break;

      case NotificationTargetType.specific:
        userIds = target.specificUsers;
        print('📊 [AdminNotificationService] Using specific user list: ${userIds.length} users');
        break;

      case NotificationTargetType.premium:
        print('📊 [AdminNotificationService] Querying premium users from Firestore');
        final usersQuery = await _firestore
            .collection('users')
            .where('isPremium', isEqualTo: true)
            .get();
        userIds = usersQuery.docs.map((doc) => doc.id).toList();
        print('📊 [AdminNotificationService] Found ${userIds.length} premium users');
        break;

      case NotificationTargetType.topic:
        print('📊 [AdminNotificationService] Using topic-based targeting: ${target.topics}');
        // For topic-based notifications, we don't need specific user IDs
        // as FCM handles topic subscriptions
        break;
    }

    final uniqueUserIds = userIds.toSet().toList(); // Remove duplicates
    print('📊 [AdminNotificationService] Final target users count (after deduplication): ${uniqueUserIds.length}');
    return uniqueUserIds;
  }

  // Send to all users
  Future<int> _sendToAllUsers(NotificationModel notification) async {
    print('📡 [AdminNotificationService] Sending to all users via topic: all_users');
    return await _sendToTopic(notification, 'all_users');
  }

  // Send to specific user types
  Future<int> _sendToUserTypes(NotificationModel notification, List<String> userTypes) async {
    print('📡 [AdminNotificationService] Sending to user types: $userTypes');
    int totalSent = 0;
    for (String userType in userTypes) {
      // Map user types to topic names
      String topic;
      switch (userType) {
        case 'businessman':
          topic = 'businessman';
          break;
        case 'politician':
          topic = 'politician';
          break;
        case 'individual_user':
        case 'regular':
          topic = 'individual_user';
          break;
        default:
          topic = '${userType}_users'; // Fallback to legacy format
      }
      
      print('📡 [AdminNotificationService] Sending to topic: $topic');
      totalSent += await _sendToTopic(notification, topic);
    }
    print('📊 [AdminNotificationService] Total sent to user types: $totalSent');
    return totalSent;
  }

  // Send to specific users
  Future<int> _sendToSpecificUsers(NotificationModel notification, List<String> userIds) async {
    print('📡 [AdminNotificationService] Sending to ${userIds.length} specific users');
    int sentCount = 0;
    
    // Get FCM tokens for specific users
    final tokens = <String>[];
    for (String userId in userIds) {
      print('🔍 [AdminNotificationService] Getting FCM token for user: $userId');
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        final fcmToken = userDoc.data()?['fcmToken'] as String?;
        if (fcmToken != null && fcmToken.isNotEmpty) {
          tokens.add(fcmToken);
          print('✅ [AdminNotificationService] Found FCM token for user: $userId');
        } else {
          print('⚠️ [AdminNotificationService] No FCM token found for user: $userId');
        }
      } else {
        print('⚠️ [AdminNotificationService] User document not found: $userId');
      }
    }

    print('📊 [AdminNotificationService] Found ${tokens.length} valid FCM tokens out of ${userIds.length} users');

    if (tokens.isNotEmpty) {
      sentCount = await _sendToTokens(notification, tokens);
    }

    return sentCount;
  }

  // Send to premium users
  Future<int> _sendToPremiumUsers(NotificationModel notification) async {
    print('📡 [AdminNotificationService] Sending to premium users via topic: premium_users');
    return await _sendToTopic(notification, 'premium_users');
  }

  // Send to topics
  Future<int> _sendToTopics(NotificationModel notification, List<String> topics) async {
    print('📡 [AdminNotificationService] Sending to topics: $topics');
    int totalSent = 0;
    for (String topic in topics) {
      print('📡 [AdminNotificationService] Sending to topic: $topic');
      totalSent += await _sendToTopic(notification, topic);
    }
    print('📊 [AdminNotificationService] Total sent to topics: $totalSent');
    return totalSent;
  }

  // Send notification to a topic using FCM HTTP v1 API
  Future<int> _sendToTopic(NotificationModel notification, String topic) async {
    try {
      print('🚀 [AdminNotificationService] Sending to topic: $topic');
      print('🔑 [AdminNotificationService] Getting access token...');
      final accessToken = await _getAccessToken();
      
      final payload = {
        'message': {
          'topic': topic,
          'notification': {
            'title': notification.title,
            'body': notification.body,
            if (notification.imageUrl != null) 'image': notification.imageUrl,
          },
          'data': notification.data.map((key, value) => MapEntry(key, value.toString())),
          'android': {
            'priority': 'HIGH',
            'notification': {
              'channel_id': 'quickposters_channel',
              'click_action': 'FLUTTER_NOTIFICATION_CLICK',
              'default_sound': true,
            }
          },
          'apns': {
            'headers': {
              'apns-priority': '10',
            },
            'payload': {
              'aps': {
                'alert': {
                  'title': notification.title,
                  'body': notification.body,
                },
                'badge': 1,
                'sound': 'default',
              }
            }
          }
        }
      };

      print('📋 [AdminNotificationService] FCM Payload:');
      print(jsonEncode(payload));
      print('🌐 [AdminNotificationService] FCM URL: $_fcmUrl');
      print('🔑 [AdminNotificationService] Using access token (first 20 chars): ${accessToken.substring(0, 20)}...');

      final response = await http.post(
        Uri.parse(_fcmUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: jsonEncode(payload),
      );

      print('📡 [AdminNotificationService] FCM Response Status: ${response.statusCode}');
      print('📡 [AdminNotificationService] FCM Response Body: ${response.body}');

      if (response.statusCode == 200) {
        print('✅ [AdminNotificationService] Successfully sent to topic: $topic');
        // FCM HTTP v1 API returns success for topic messages
        // We can't get exact delivery count for topics, so return 1 for success
        return 1;
      } else {
        print('❌ [AdminNotificationService] FCM request failed for topic: $topic');
        throw Exception('FCM request failed: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to send to topic $topic: $e');
      throw Exception('Failed to send to topic $topic: $e');
    }
  }

  // Send notification to specific tokens using FCM HTTP v1 API
  Future<int> _sendToTokens(NotificationModel notification, List<String> tokens) async {
    try {
      print('🚀 [AdminNotificationService] Sending to ${tokens.length} specific tokens');
      final accessToken = await _getAccessToken();
      int totalSent = 0;
      
      // Send to each token individually (FCM HTTP v1 doesn't support batch sending to multiple tokens)
      for (int i = 0; i < tokens.length; i++) {
        final token = tokens[i];
        print('📡 [AdminNotificationService] Sending to token ${i + 1}/${tokens.length}: ${token.substring(0, 20)}...');
        
        final payload = {
          'message': {
            'token': token,
            'notification': {
              'title': notification.title,
              'body': notification.body,
              if (notification.imageUrl != null) 'image': notification.imageUrl,
            },
            'data': notification.data.map((key, value) => MapEntry(key, value.toString())),
            'android': {
              'priority': 'HIGH',
              'notification': {
                'channel_id': 'quickposters_channel',
                'click_action': 'FLUTTER_NOTIFICATION_CLICK',
                'default_sound': true,
              }
            },
            'apns': {
              'headers': {
                'apns-priority': '10',
              },
              'payload': {
                'aps': {
                  'alert': {
                    'title': notification.title,
                    'body': notification.body,
                  },
                  'badge': 1,
                  'sound': 'default',
                }
              }
            }
          }
        };

        print('📋 [AdminNotificationService] Token Payload:');
        print(jsonEncode(payload));

        final response = await http.post(
          Uri.parse(_fcmUrl),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $accessToken',
          },
          body: jsonEncode(payload),
        );

        print('📡 [AdminNotificationService] Token Response Status: ${response.statusCode}');
        print('📡 [AdminNotificationService] Token Response Body: ${response.body}');

        if (response.statusCode == 200) {
          totalSent++;
          print('✅ [AdminNotificationService] Successfully sent to token ${i + 1}');
        } else {
          print('❌ [AdminNotificationService] Failed to send to token ${i + 1}: ${response.statusCode} - ${response.body}');
        }
        // Continue sending to other tokens even if one fails
      }
      
      print('📊 [AdminNotificationService] Successfully sent to $totalSent out of ${tokens.length} tokens');
      return totalSent;
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to send to tokens: $e');
      throw Exception('Failed to send to tokens: $e');
    }
  }

  // Get notification statistics
  Future<Map<String, int>> getNotificationStats() async {
    try {
      print('🔵 [AdminNotificationService] Getting notification statistics');
      final notifications = await _firestore.collection('admin_notifications').get();
      
      int total = notifications.docs.length;
      int sent = 0;
      int scheduled = 0;
      int failed = 0;
      int totalRecipients = 0;
      int totalDelivered = 0;
      int totalOpened = 0;

      for (var doc in notifications.docs) {
        final data = doc.data();
        final status = data['status'] as String?;
        
        switch (status) {
          case 'sent':
            sent++;
            break;
          case 'scheduled':
            scheduled++;
            break;
          case 'failed':
            failed++;
            break;
        }

        totalRecipients += (data['recipientCount'] as num?)?.toInt() ?? 0;
        totalDelivered += (data['deliveredCount'] as num?)?.toInt() ?? 0;
        totalOpened += (data['openedCount'] as num?)?.toInt() ?? 0;
      }

      final stats = {
        'total': total,
        'sent': sent,
        'scheduled': scheduled,
        'failed': failed,
        'totalRecipients': totalRecipients,
        'totalDelivered': totalDelivered,
        'totalOpened': totalOpened,
      };

      print('📊 [AdminNotificationService] Statistics: $stats');
      return stats;
    } catch (e) {
      print('❌ [AdminNotificationService] Failed to get notification stats: $e');
      throw Exception('Failed to get notification stats: $e');
    }
  }
} 