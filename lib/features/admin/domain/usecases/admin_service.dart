import '../entities/admin_user.dart';
import '../entities/dashboard_stats.dart';
import '../repositories/admin_repository.dart';
import '../../../user/domain/entities/user_model.dart';
import '../../../user/domain/entities/business_parameter.dart';
import '../../../user/domain/entities/political_parameter.dart';
import '../../../user/domain/entities/party.dart';
import '../../../templates/domain/entities/template_item.dart';
import '../../../banners/domain/entities/banner_item.dart';

/// Service class for admin operations
class AdminService {
  final AdminRepository _repository;

  AdminService(this._repository);

  // Dashboard & Analytics
  /// Get dashboard statistics
  Future<DashboardStats> getDashboardStats() async {
    return await _repository.getDashboardStats();
  }

  /// Get user analytics
  Future<Map<String, dynamic>> getUserAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return await _repository.getUserAnalytics(
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Get content analytics
  Future<Map<String, dynamic>> getContentAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return await _repository.getContentAnalytics(
      startDate: startDate,
      endDate: endDate,
    );
  }

  // User Management
  /// Get all users with filtering and pagination
  Future<List<UserModel>> getAllUsers({
    int limit = 20,
    String? lastUserId,
    String? searchQuery,
    String? userType,
    bool? isPremium,
  }) async {
    return await _repository.getAllUsers(
      limit: limit,
      lastUserId: lastUserId,
      searchQuery: searchQuery,
      userType: userType,
      isPremium: isPremium,
    );
  }

  /// Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    return await _repository.getUserById(userId);
  }

  /// Update user information
  Future<void> updateUser(UserModel user) async {
    await _repository.updateUser(user);
  }

  /// Delete user
  Future<void> deleteUser(String userId) async {
    await _repository.deleteUser(userId);
  }

  /// Toggle user premium status
  Future<void> toggleUserPremium(String userId, bool isPremium) async {
    await _repository.toggleUserPremium(userId, isPremium);
  }

  /// Get users count by type
  Future<Map<String, int>> getUsersCountByType() async {
    return await _repository.getUsersCountByType();
  }

  // Admin Management
  /// Get all admin users
  Future<List<AdminUser>> getAllAdmins() async {
    return await _repository.getAllAdmins();
  }

  /// Create admin user
  Future<void> createAdmin({
    required UserModel user,
    String role = 'admin',
    List<String>? permissions,
    String? grantedBy,
  }) async {
    final adminPermissions = permissions ?? AdminUser.getDefaultPermissions(role);
    final admin = AdminUser.fromUser(
      user: user,
      role: role,
      permissions: adminPermissions,
      grantedBy: grantedBy,
    );
    await _repository.createAdmin(admin);
  }

  /// Update admin user
  Future<void> updateAdmin(AdminUser admin) async {
    await _repository.updateAdmin(admin);
  }

  /// Remove admin privileges
  Future<void> removeAdmin(String userId) async {
    await _repository.removeAdmin(userId);
  }

  /// Check if user is admin
  Future<bool> isUserAdmin(String userId) async {
    return await _repository.isUserAdmin(userId);
  }

  // Content Management
  /// Get all templates
  Future<List<TemplateItem>> getAllTemplates({
    int limit = 20,
    String? lastTemplateId,
  }) async {
    return await _repository.getAllTemplates(
      limit: limit,
      lastTemplateId: lastTemplateId,
    );
  }

  /// Upload template
  Future<String> uploadTemplate(String filePath, String fileName) async {
    return await _repository.uploadTemplate(filePath, fileName);
  }

  /// Update template
  Future<void> updateTemplate(TemplateItem template) async {
    await _repository.updateTemplate(template);
  }

  /// Delete template
  Future<void> deleteTemplate(String templateId) async {
    await _repository.deleteTemplate(templateId);
  }

  /// Get all banners
  Future<List<BannerItem>> getAllBanners({
    int limit = 20,
    String? lastBannerId,
  }) async {
    return await _repository.getAllBanners(
      limit: limit,
      lastBannerId: lastBannerId,
    );
  }

  /// Upload banner
  Future<String> uploadBanner(String filePath, String fileName) async {
    return await _repository.uploadBanner(filePath, fileName);
  }

  /// Delete banner
  Future<void> deleteBanner(String bannerId) async {
    await _repository.deleteBanner(bannerId);
  }

  // Parameter Management
  /// Get all business parameters
  Future<List<BusinessParameter>> getAllBusinessParameters() async {
    return await _repository.getAllBusinessParameters();
  }

  /// Create business parameter
  Future<void> createBusinessParameter(BusinessParameter parameter) async {
    await _repository.createBusinessParameter(parameter);
  }

  /// Update business parameter
  Future<void> updateBusinessParameter(BusinessParameter parameter) async {
    await _repository.updateBusinessParameter(parameter);
  }

  /// Delete business parameter
  Future<void> deleteBusinessParameter(String parameterId) async {
    await _repository.deleteBusinessParameter(parameterId);
  }

  /// Get all political parameters
  Future<List<PoliticalParameter>> getAllPoliticalParameters() async {
    return await _repository.getAllPoliticalParameters();
  }

  /// Create political parameter
  Future<void> createPoliticalParameter(PoliticalParameter parameter) async {
    await _repository.createPoliticalParameter(parameter);
  }

  /// Update political parameter
  Future<void> updatePoliticalParameter(PoliticalParameter parameter) async {
    await _repository.updatePoliticalParameter(parameter);
  }

  /// Delete political parameter
  Future<void> deletePoliticalParameter(String parameterId) async {
    await _repository.deletePoliticalParameter(parameterId);
  }

  // Party Management
  /// Get all political parties
  Future<List<Party>> getAllPoliticalParties() async {
    return await _repository.getAllPoliticalParties();
  }

  /// Create political party
  Future<void> createPoliticalParty(Party party) async {
    await _repository.createPoliticalParty(party);
  }

  /// Update political party
  Future<void> updatePoliticalParty(Party party) async {
    await _repository.updatePoliticalParty(party);
  }

  /// Delete political party
  Future<void> deletePoliticalParty(String partyId) async {
    await _repository.deletePoliticalParty(partyId);
  }

  // System Operations
  /// Backup data
  Future<void> backupData() async {
    await _repository.backupData();
  }

  /// Get system health status
  Future<Map<String, dynamic>> getSystemHealth() async {
    return await _repository.getSystemHealth();
  }

  /// Send notification to all users
  Future<void> sendBroadcastNotification(String title, String message) async {
    await _repository.sendBroadcastNotification(title, message);
  }

  /// Get app usage statistics
  Future<Map<String, dynamic>> getAppUsageStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return await _repository.getAppUsageStats(
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Validate admin permissions for an action
  bool validateAdminPermission(AdminUser admin, String action) {
    return admin.canPerform(action);
  }

  /// Get admin role hierarchy
  List<String> getAdminRoles() {
    return ['super_admin', 'admin', 'moderator'];
  }

  /// Check if role has higher privileges than another
  bool hasHigherPrivileges(String role1, String role2) {
    final roles = getAdminRoles();
    final index1 = roles.indexOf(role1);
    final index2 = roles.indexOf(role2);
    return index1 < index2; // Lower index = higher privileges
  }
}
