import '../entities/admin_user.dart';
import '../entities/dashboard_stats.dart';
import '../../../user/domain/entities/user_model.dart';
import '../../../user/domain/entities/business_parameter.dart';
import '../../../user/domain/entities/political_parameter.dart';
import '../../../user/domain/entities/party.dart';
import '../../../templates/domain/entities/template_item.dart';
import '../../../banners/domain/entities/banner_item.dart';

/// Repository interface for admin operations
abstract class AdminRepository {
  // Dashboard & Analytics
  /// Get dashboard statistics
  Future<DashboardStats> getDashboardStats();

  /// Get user analytics data
  Future<Map<String, dynamic>> getUserAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get content analytics data
  Future<Map<String, dynamic>> getContentAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  });

  // User Management
  /// Get all users with pagination
  Future<List<UserModel>> getAllUsers({
    int limit = 20,
    String? lastUserId,
    String? searchQuery,
    String? userType,
    bool? isPremium,
  });

  /// Get user by ID
  Future<UserModel?> getUserById(String userId);

  /// Update user information
  Future<void> updateUser(UserModel user);

  /// Delete user
  Future<void> deleteUser(String userId);

  /// Toggle user premium status
  Future<void> toggleUserPremium(String userId, bool isPremium);

  /// Get users count by type
  Future<Map<String, int>> getUsersCountByType();

  // Admin Management
  /// Get all admin users
  Future<List<AdminUser>> getAllAdmins();

  /// Create admin user
  Future<void> createAdmin(AdminUser admin);

  /// Update admin user
  Future<void> updateAdmin(AdminUser admin);

  /// Remove admin privileges
  Future<void> removeAdmin(String userId);

  /// Check if user is admin
  Future<bool> isUserAdmin(String userId);

  // Content Management
  /// Get all templates
  Future<List<TemplateItem>> getAllTemplates({
    int limit = 20,
    String? lastTemplateId,
  });

  /// Upload template
  Future<String> uploadTemplate(String filePath, String fileName);

  /// Update template
  Future<void> updateTemplate(TemplateItem template);

  /// Delete template
  Future<void> deleteTemplate(String templateId);

  /// Get all banners
  Future<List<BannerItem>> getAllBanners({
    int limit = 20,
    String? lastBannerId,
  });

  /// Upload banner
  Future<String> uploadBanner(String filePath, String fileName);

  /// Delete banner
  Future<void> deleteBanner(String bannerId);

  // Parameter Management
  /// Get all business parameters
  Future<List<BusinessParameter>> getAllBusinessParameters();

  /// Create business parameter
  Future<void> createBusinessParameter(BusinessParameter parameter);

  /// Update business parameter
  Future<void> updateBusinessParameter(BusinessParameter parameter);

  /// Delete business parameter
  Future<void> deleteBusinessParameter(String parameterId);

  /// Get all political parameters
  Future<List<PoliticalParameter>> getAllPoliticalParameters();

  /// Create political parameter
  Future<void> createPoliticalParameter(PoliticalParameter parameter);

  /// Update political parameter
  Future<void> updatePoliticalParameter(PoliticalParameter parameter);

  /// Delete political parameter
  Future<void> deletePoliticalParameter(String parameterId);

  // Party Management
  /// Get all political parties
  Future<List<Party>> getAllPoliticalParties();

  /// Create political party
  Future<void> createPoliticalParty(Party party);

  /// Update political party
  Future<void> updatePoliticalParty(Party party);

  /// Delete political party
  Future<void> deletePoliticalParty(String partyId);

  // System Operations
  /// Backup data
  Future<void> backupData();

  /// Get system health status
  Future<Map<String, dynamic>> getSystemHealth();

  /// Send notification to all users
  Future<void> sendBroadcastNotification(String title, String message);

  /// Get app usage statistics
  Future<Map<String, dynamic>> getAppUsageStats({
    DateTime? startDate,
    DateTime? endDate,
  });
}
