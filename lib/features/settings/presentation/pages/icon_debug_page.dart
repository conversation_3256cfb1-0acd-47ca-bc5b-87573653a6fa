import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dynamic_icon/flutter_dynamic_icon.dart';
import '../../../../core/services/app_icon_service.dart';
import '../../../../core/theme/app_theme.dart';

class IconDebugPage extends StatefulWidget {
  const IconDebugPage({super.key});

  @override
  State<IconDebugPage> createState() => _IconDebugPageState();
}

class _IconDebugPageState extends State<IconDebugPage> {
  String? _currentIcon;
  bool _supportsAlternateIcons = false;
  bool _isLoading = false;
  List<String> _debugLogs = [];

  @override
  void initState() {
    super.initState();
    _checkSupport();
    _getCurrentIcon();
  }

  void _addLog(String message) {
    setState(() {
      _debugLogs.add('${DateTime.now().toIso8601String()}: $message');
    });
    debugPrint(message);
  }

  Future<void> _checkSupport() async {
    try {
      final bool supports = await FlutterDynamicIcon.supportsAlternateIcons;
      setState(() {
        _supportsAlternateIcons = supports;
      });
      _addLog('Supports alternate icons: $supports');
    } catch (e) {
      _addLog('Error checking support: $e');
    }
  }

  Future<void> _getCurrentIcon() async {
    try {
      final String? current = await FlutterDynamicIcon.getAlternateIconName();
      setState(() {
        _currentIcon = current;
      });
      _addLog('Current icon: ${current ?? 'default (AppIcon)'}');
    } catch (e) {
      _addLog('Error getting current icon: $e');
    }
  }

  Future<void> _setIcon(String? iconName) async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Setting icon to: ${iconName ?? 'default'}');
      
      await FlutterDynamicIcon.setAlternateIconName(
        iconName,
        showAlert: false,
      );
      
      // Wait a bit for the change to take effect
      await Future.delayed(const Duration(milliseconds: 1000));
      
      // Verify the change
      await _getCurrentIcon();
      
      _addLog('Icon change completed');
    } catch (e) {
      _addLog('Error setting icon: $e');
      if (e is PlatformException) {
        _addLog('Platform exception details: Code: ${e.code}, Message: ${e.message}');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testAppIconService(bool isPremium) async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addLog('Testing AppIconService with isPremium: $isPremium');
      
      final bool success = await AppIconService.setAppIcon(isPremium);
      
      _addLog('AppIconService result: $success');
      
      // Verify the change
      await _getCurrentIcon();
    } catch (e) {
      _addLog('Error testing AppIconService: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearLogs() {
    setState(() {
      _debugLogs.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Icon Debug'),
        backgroundColor: AppTheme.primaryBlue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('Supports alternate icons: $_supportsAlternateIcons'),
                    Text('Current icon: ${_currentIcon ?? 'default (AppIcon)'}'),
                    if (_isLoading) const Text('Loading...'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Control buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Direct Icon Control',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: _isLoading ? null : () => _setIcon(null),
                          child: const Text('Default'),
                        ),
                        ElevatedButton(
                          onPressed: _isLoading ? null : () => _setIcon('regular'),
                          child: const Text('Regular'),
                        ),
                        ElevatedButton(
                          onPressed: _isLoading ? null : () => _setIcon('premium'),
                          child: const Text('Premium'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'AppIconService Test',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: _isLoading ? null : () => _testAppIconService(false),
                          child: const Text('Set Non-Premium'),
                        ),
                        ElevatedButton(
                          onPressed: _isLoading ? null : () => _testAppIconService(true),
                          child: const Text('Set Premium'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Debug logs
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Debug Logs',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          ElevatedButton(
                            onPressed: _clearLogs,
                            child: const Text('Clear'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: SingleChildScrollView(
                            child: Text(
                              _debugLogs.isEmpty 
                                  ? 'No logs yet...' 
                                  : _debugLogs.join('\n'),
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
