import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/language_provider.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../../../../core/widgets/language_selector.dart';

/// Test page to verify language functionality
class LanguageTestPage extends StatelessWidget {
  const LanguageTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final localizations = AppLocalizations.of(context)!;
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Language Test',
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current language info
              Card(
                color: isPremium 
                    ? AppTheme.premiumDarkGrey.withAlpha(128) // 0.5 opacity
                    : Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Language Info',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildInfoRow(
                        'Language Code',
                        languageProvider.currentLanguageCode,
                        isPremium,
                      ),
                      _buildInfoRow(
                        'Display Name',
                        languageProvider.currentLanguageDisplayName,
                        isPremium,
                      ),
                      _buildInfoRow(
                        'English Name',
                        languageProvider.currentLanguageEnglishName,
                        isPremium,
                      ),
                      _buildInfoRow(
                        'Locale',
                        languageProvider.currentLocale.toString(),
                        isPremium,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Language selector
              Card(
                color: isPremium 
                    ? AppTheme.premiumDarkGrey.withAlpha(128) // 0.5 opacity
                    : Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Language Selector',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 12),
                      const LanguageSelector(),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Localized strings test
              Card(
                color: isPremium 
                    ? AppTheme.premiumDarkGrey.withAlpha(128) // 0.5 opacity
                    : Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Localized Strings Test',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildLocalizedStringRow('App Title', localizations.appTitle, isPremium),
                      _buildLocalizedStringRow('Welcome', localizations.welcome, isPremium),
                      _buildLocalizedStringRow('Login', localizations.login, isPremium),
                      _buildLocalizedStringRow('Home', localizations.home, isPremium),
                      _buildLocalizedStringRow('Templates', localizations.templates, isPremium),
                      _buildLocalizedStringRow('Premium', localizations.premium, isPremium),
                      _buildLocalizedStringRow('Settings', localizations.settings, isPremium),
                      _buildLocalizedStringRow('Language', localizations.language, isPremium),
                      _buildLocalizedStringRow('Create Poster', localizations.createPoster, isPremium),
                      _buildLocalizedStringRow('Save Image', localizations.saveImage, isPremium),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Language indicators
              Card(
                color: isPremium 
                    ? AppTheme.premiumDarkGrey.withAlpha(128) // 0.5 opacity
                    : Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Language Indicators',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          const LanguageIndicator(),
                          const SizedBox(width: 12),
                          const LanguageIndicator(showFullName: true),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Compact language selector
              Card(
                color: isPremium 
                    ? AppTheme.premiumDarkGrey.withAlpha(128) // 0.5 opacity
                    : Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Compact Language Selector',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 12),
                      const LanguageSelector(
                        isCompact: true,
                        showLabel: false,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Test buttons
              Card(
                color: isPremium 
                    ? AppTheme.premiumDarkGrey.withAlpha(128) // 0.5 opacity
                    : Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Actions',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 12),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pushNamed(context, '/language-selection');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            foregroundColor: isPremium ? Colors.black : Colors.white,
                          ),
                          child: const Text('Open Language Selection Page'),
                        ),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            languageProvider.refreshLanguage();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            foregroundColor: isPremium ? Colors.black : Colors.white,
                          ),
                          child: const Text('Refresh Language'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isPremium) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: isPremium ? Colors.white : AppTheme.textRichBlack,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocalizedStringRow(String key, String value, bool isPremium) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$key:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
                color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: isPremium ? Colors.white : AppTheme.textRichBlack,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
