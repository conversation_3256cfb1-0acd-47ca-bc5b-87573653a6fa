import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../../../../features/authentication/presentation/bloc/auth_bloc.dart';
import '../../../../features/authentication/presentation/bloc/auth_event.dart';
import '../../../../features/user/presentation/pages/profile_page.dart';
import 'web_view_page.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late ThemeProvider _themeProvider;

  @override
  void initState() {
    super.initState();
    _themeProvider = Provider.of<ThemeProvider>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _themeProvider.gradientAppBar(
        title: 'Settings',
      ),
      body: Container(
        decoration: _themeProvider.isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Profile section
            _buildSectionTitle(AppLocalizations.of(context)!.account),
            _buildSettingsItem(
              icon: Icons.person,
              title: AppLocalizations.of(context)!.myProfile,
              subtitle: AppLocalizations.of(context)!.viewEditProfileInfo,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProfilePage(),
                  ),
                );
              },
            ),
            const Divider(),

            // App settings section
            _buildSectionTitle(AppLocalizations.of(context)!.appSettings),
            _buildSettingsItem(
              icon: Icons.language,
              title: AppLocalizations.of(context)!.language,
              subtitle: AppLocalizations.of(context)!.changeAppLanguage,
              onTap: () {
                // To be implemented
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(AppLocalizations.of(context)!.languageSettingsComingSoon)),
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.notifications,
              title: AppLocalizations.of(context)!.notifications,
              subtitle: AppLocalizations.of(context)!.manageNotificationSettings,
              onTap: () {
                // To be implemented
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(AppLocalizations.of(context)!.notificationSettingsComingSoon)),
                );
              },
            ),
            // Screenshot prevention toggle
            // _buildSettingsToggle(
            //   icon: Icons.screenshot_monitor,
            //   title: 'Prevent Screenshots',
            //   subtitle: 'Block screenshots and screen recording',
            //   value: _isScreenshotPreventionEnabled,
            //   onChanged: _toggleScreenshotPrevention,
            // ),
            // Premium mode toggle
            _buildSettingsToggle(
              icon: Icons.workspace_premium,
              title: 'Premium Mode',
              subtitle: 'Enable premium gold theme',
              value: _themeProvider.isPremium,
              onChanged: _togglePremiumMode,
              iconColor: _themeProvider.isPremium ? AppTheme.premiumGold : null,
            ),
            Divider(
              color: _themeProvider.isPremium ? AppTheme.premiumGold.withAlpha(77) : null,
              thickness: _themeProvider.isPremium ? 1 : null,
            ),

            // About section
            _buildSectionTitle(AppLocalizations.of(context)!.about),
            _buildSettingsItem(
              icon: Icons.info,
              title: AppLocalizations.of(context)!.aboutQuickPosters,
              subtitle: AppLocalizations.of(context)!.version,
              onTap: () {
                // To be implemented
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(AppLocalizations.of(context)!.aboutPageComingSoon)),
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.privacy_tip,
              title: AppLocalizations.of(context)!.privacyPolicy,
              subtitle: AppLocalizations.of(context)!.readPrivacyPolicy,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => WebViewPage(
                      url: 'https://quickposters.in/privacy-policy.html',
                      title: AppLocalizations.of(context)!.privacyPolicy,
                    ),
                  ),
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.description,
              title: 'Terms of Service',
              subtitle: 'Read our terms of service',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WebViewPage(
                      url: 'https://quickposters.in/terms-of-service.html',
                      title: 'Terms of Service',
                    ),
                  ),
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.receipt_long,
              title: 'Refund Policy',
              subtitle: 'Read our refund policy',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WebViewPage(
                      url: 'https://quickposters.in/refund-policy.html',
                      title: 'Refund Policy',
                    ),
                  ),
                );
              },
            ),
            Divider(
              color: _themeProvider.isPremium ? AppTheme.premiumGold.withAlpha(77) : null,
              thickness: _themeProvider.isPremium ? 1 : null,
            ),

            // Logout section
            _buildSectionTitle('Account Actions'),
            _buildSettingsItem(
              icon: Icons.logout,
              title: 'Logout',
              subtitle: 'Sign out from your account',
              onTap: () {
                _showLogoutConfirmationDialog(context);
              },
              iconColor: Colors.red,
              titleColor: Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  // Build a section title
  Widget _buildSectionTitle(String title) {
    final isPremium = _themeProvider.isPremium;

    return Padding(
      padding: const EdgeInsets.only(top: 16.0, bottom: 8.0, left: 8.0),
      child: Text(
        title,
        style: TextStyle(
          color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  // Build a settings item
  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? iconColor,
    Color? titleColor,
  }) {
    final isPremium = _themeProvider.isPremium;

    return ListTile(
      leading: Icon(
        icon,
        color: iconColor ?? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: titleColor ?? (isPremium ? AppTheme.premiumGold : null),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: isPremium
            ? TextStyle(color: Colors.white.withAlpha(200)) // 0.8 opacity white
            : null,
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      tileColor: isPremium ? AppTheme.premiumDarkGrey.withAlpha(100) : null,
    );
  }

  // Build a settings toggle item
  Widget _buildSettingsToggle({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    Color? iconColor,
    Color? titleColor,
  }) {
    final isPremium = _themeProvider.isPremium;

    return ListTile(
      leading: Icon(
        icon,
        color: iconColor ?? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: titleColor ?? (isPremium ? AppTheme.premiumGold : null),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: isPremium
            ? TextStyle(color: Colors.white.withAlpha(200)) // 0.8 opacity white
            : null,
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
        activeTrackColor: isPremium ? AppTheme.premiumGold.withAlpha(77) : null, // 0.3 opacity
        inactiveThumbColor: isPremium ? AppTheme.premiumDarkGrey : null,
        inactiveTrackColor: isPremium ? AppTheme.premiumLightGrey.withAlpha(77) : null,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      tileColor: isPremium ? AppTheme.premiumDarkGrey.withAlpha(100) : null,
    );
  }

  // Screenshot prevention is always enabled

  // Toggle premium mode
  Future<void> _togglePremiumMode(bool value) async {
    try {
      await _themeProvider.updatePremiumStatus(value);

      // Check if the widget is still mounted before updating UI
      if (!mounted) return;

      setState(() {
        // Update UI if needed
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            value
                ? 'Premium mode enabled'
                : 'Premium mode disabled',
            style: TextStyle(
              color: value ? AppTheme.premiumGold : Colors.white,
            ),
          ),
          backgroundColor: value
              ? AppTheme.premiumBlack
              : AppTheme.infoBlue,
        ),
      );
    } catch (e) {
      // Check if the widget is still mounted before updating UI
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to change premium mode settings'),
          backgroundColor: AppTheme.errorRed,
        ),
      );
    }
  }

  // Show logout confirmation dialog
  void _showLogoutConfirmationDialog(BuildContext context) {
    final isPremium = _themeProvider.isPremium;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: isPremium ? AppTheme.premiumBlack : null,
          title: Text(
            'Logout',
            style: TextStyle(
              color: isPremium ? AppTheme.premiumGold : null,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: TextStyle(
              color: isPremium ? Colors.white : null,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: isPremium
                ? BorderSide(color: AppTheme.premiumGold, width: 1)
                : BorderSide.none,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close the dialog
              },
              style: TextButton.styleFrom(
                foregroundColor: isPremium ? AppTheme.premiumGold : null,
              ),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close the dialog
                context.read<AuthBloc>().add(SignOutEvent());
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }
}
