import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/language_provider.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/services/language_service.dart';

/// Page for selecting app language
class LanguageSelectionPage extends StatefulWidget {
  const LanguageSelectionPage({super.key});

  @override
  State<LanguageSelectionPage> createState() => _LanguageSelectionPageState();
}

class _LanguageSelectionPageState extends State<LanguageSelectionPage> {
  String? _selectedLanguage;
  bool _isChangingLanguage = false;

  @override
  void initState() {
    super.initState();
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    _selectedLanguage = languageProvider.currentLanguageCode;
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Select Language', // TODO: Localize this
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: Column(
          children: [
            // Header section
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.language,
                    size: 48,
                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Choose your preferred language',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'The app will restart to apply the new language',
                    style: TextStyle(
                      fontSize: 14,
                      color: isPremium 
                          ? Colors.white.withAlpha(179) // 0.7 opacity
                          : AppTheme.secondaryText,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // Language list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: LanguageService.supportedLanguages.length,
                itemBuilder: (context, index) {
                  final languages = LanguageService.getSupportedLanguagesList();
                  final language = languages[index];
                  final languageCode = language['code']!;
                  final displayName = language['displayName']!;
                  final englishName = language['englishName']!;
                  final isSelected = _selectedLanguage == languageCode;
                  final isCurrentLanguage = languageProvider.currentLanguageCode == languageCode;

                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _isChangingLanguage ? null : () {
                          setState(() {
                            _selectedLanguage = languageCode;
                          });
                        },
                        borderRadius: BorderRadius.circular(16),
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? (isPremium 
                                    ? AppTheme.premiumGold.withAlpha(51) // 0.2 opacity
                                    : AppTheme.primaryBlue.withAlpha(51))
                                : (isPremium 
                                    ? AppTheme.premiumDarkGrey.withAlpha(128) // 0.5 opacity
                                    : Colors.white),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: isSelected
                                  ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                                  : (isPremium 
                                      ? AppTheme.premiumGold.withAlpha(77) // 0.3 opacity
                                      : AppTheme.lightGray),
                              width: isSelected ? 2 : 1,
                            ),
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                                    .withAlpha(77), // 0.3 opacity
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ] : null,
                          ),
                          child: Row(
                            children: [
                              // Language icon/flag placeholder
                              Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: isPremium 
                                      ? AppTheme.premiumGold.withAlpha(51) // 0.2 opacity
                                      : AppTheme.primaryBlue.withAlpha(51),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Center(
                                  child: Text(
                                    languageCode.toUpperCase(),
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                      color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),

                              // Language names
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      displayName,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: isPremium 
                                            ? (isSelected ? AppTheme.premiumGold : Colors.white)
                                            : (isSelected ? AppTheme.primaryBlue : AppTheme.textRichBlack),
                                      ),
                                    ),
                                    Text(
                                      englishName,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: isPremium 
                                            ? Colors.white.withAlpha(179) // 0.7 opacity
                                            : AppTheme.secondaryText,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Selection indicator
                              if (isSelected)
                                Icon(
                                  Icons.check_circle,
                                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                  size: 24,
                                )
                              else if (isCurrentLanguage)
                                Icon(
                                  Icons.radio_button_checked,
                                  color: isPremium 
                                      ? AppTheme.premiumGold.withAlpha(128) // 0.5 opacity
                                      : AppTheme.primaryBlue.withAlpha(128),
                                  size: 24,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            // Apply button
            Container(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: (_isChangingLanguage || 
                             _selectedLanguage == null || 
                             _selectedLanguage == languageProvider.currentLanguageCode)
                      ? null
                      : _applyLanguageChange,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    foregroundColor: isPremium ? Colors.black : Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 4,
                  ),
                  child: _isChangingLanguage
                      ? SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              isPremium ? Colors.black : Colors.white,
                            ),
                          ),
                        )
                      : const Text(
                          'Apply Language', // TODO: Localize this
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Apply the selected language change
  Future<void> _applyLanguageChange() async {
    if (_selectedLanguage == null) return;

    setState(() {
      _isChangingLanguage = true;
    });

    try {
      final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
      final success = await languageProvider.changeLanguage(_selectedLanguage!);

      if (success && mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Language changed to ${LanguageService.getDisplayName(_selectedLanguage!)}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        // Navigate back after a short delay
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          Navigator.pop(context);
        }
      } else if (mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to change language. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isChangingLanguage = false;
        });
      }
    }
  }
}
