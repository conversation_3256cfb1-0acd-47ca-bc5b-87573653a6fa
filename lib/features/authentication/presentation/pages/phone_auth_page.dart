import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../../../settings/presentation/pages/web_view_page.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';
import 'otp_verification_page.dart';

class PhoneAuthPage extends StatefulWidget {
  const PhoneAuthPage({super.key});

  @override
  State<PhoneAuthPage> createState() => _PhoneAuthPageState();
}

class _PhoneAuthPageState extends State<PhoneAuthPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  bool _isLoading = false;

  // Rate limiting variables
  DateTime? _lastRequestTime;
  int _requestCount = 0;
  static const int _maxRequestsPerMinute = 2; // Maximum 2 requests per minute

  @override
  void initState() {
    super.initState();
    _phoneController.addListener(() {
      setState(() {}); // Rebuild to update validation message
    });
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  // Check if we can send a new request based on rate limiting
  bool _canSendRequest() {
    final now = DateTime.now();

    // If this is the first request or it's been more than a minute since the last request
    if (_lastRequestTime == null ||
        now.difference(_lastRequestTime!).inMinutes >= 1) {
      _lastRequestTime = now;
      _requestCount = 1;
      return true;
    }

    // If we haven't exceeded the maximum requests per minute
    if (_requestCount < _maxRequestsPerMinute) {
      _requestCount++;
      return true;
    }

    // Rate limit exceeded
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryBlue.withValues(alpha: 0.05),
              Colors.white,
              AppTheme.accentViolet.withValues(alpha: 0.03),
            ],
          ),
        ),
        child: BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthLoading) {
              setState(() {
                _isLoading = true;
              });
            } else {
              setState(() {
                _isLoading = false;
              });
            }

            if (state is OtpSent) {
              // Construct full phone number with +91 prefix for display
              final fullPhoneNumber = '+91${_phoneController.text.trim()}';

              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => OtpVerificationPage(
                        verificationId: state.verificationId,
                        phoneNumber: fullPhoneNumber,
                      ),
                ),
              );
            } else if (state is AuthFailure) {
              // Localize error messages based on error codes
              String localizedMessage;
              final localizations = AppLocalizations.of(context)!;

              switch (state.message) {
                case 'invalid-phone-number':
                  localizedMessage = localizations.invalidPhoneNumber;
                  break;
                case 'quota-exceeded':
                  localizedMessage = localizations.quotaExceeded;
                  break;
                case 'user-disabled':
                  localizedMessage = localizations.userDisabled;
                  break;
                case 'operation-not-allowed':
                  localizedMessage = localizations.operationNotAllowed;
                  break;
                case 'captcha-check-failed':
                  localizedMessage = localizations.captchaCheckFailed;
                  break;
                case 'missing-client-identifier':
                  localizedMessage = localizations.missingClientIdentifier;
                  break;
                case 'too-many-requests':
                  localizedMessage = localizations.tooManyRequests;
                  break;
                case 'verification-failed':
                  localizedMessage = localizations.verificationFailed;
                  break;
                default:
                  localizedMessage = state.message;
              }

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    localizedMessage,
                    style: AppTheme.bodyMedium.copyWith(color: Colors.white),
                  ),
                  backgroundColor: AppTheme.errorRed,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            }
          },
          builder: (context, state) {
            return SafeArea(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: constraints.maxHeight,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(height: 20),

                              // Modern Header Section
                              Column(
                                children: [
                                  // App Icon with modern design
                                  Container(
                                    height: 70,
                                    width: 70,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          AppTheme.primaryBlue,
                                          AppTheme.accentViolet,
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(18),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppTheme.primaryBlue
                                              .withValues(alpha: 0.3),
                                          blurRadius: 15,
                                          offset: const Offset(0, 6),
                                        ),
                                      ],
                                    ),
                                    child: const Icon(
                                      Icons.auto_awesome_rounded,
                                      size: 35,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 20),

                                  // App Name
                                  Text(
                                    'QuickPosters',
                                    style: TextStyle(
                                      fontSize: 28,
                                      fontWeight: FontWeight.w800,
                                      color: AppTheme.textRichBlack,
                                      letterSpacing: -0.5,
                                    ),
                                  ),
                                  const SizedBox(height: 6),

                                  // Tagline
                                  Text(
                                    'Create stunning posters in seconds',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: AppTheme.secondaryText,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 50),
                              // Welcome Text
                              Column(
                                children: [
                                  Text(
                                    localizations.welcomeBack,
                                    style: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w700,
                                      color: AppTheme.textRichBlack,
                                      letterSpacing: -0.3,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    localizations.enterPhoneNumber,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: AppTheme.secondaryText,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),

                              const SizedBox(height: 40),
                              // Modern Phone Input Card
                              Container(
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.04,
                                      ),
                                      blurRadius: 15,
                                      offset: const Offset(0, 3),
                                    ),
                                    BoxShadow(
                                      color: AppTheme.primaryBlue.withValues(
                                        alpha: 0.06,
                                      ),
                                      blurRadius: 30,
                                      offset: const Offset(0, 6),
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(24.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Label
                                      Text(
                                        AppLocalizations.of(context)!.phoneNumberLabel,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                          color: AppTheme.textRichBlack,
                                          letterSpacing: 0.2,
                                        ),
                                      ),
                                      const SizedBox(height: 16),

                                      // Phone Input Row
                                      Container(
                                        height: 56,
                                        decoration: BoxDecoration(
                                          color: const Color(0xFFF8F9FA),
                                          borderRadius: BorderRadius.circular(
                                            14,
                                          ),
                                          border: Border.all(
                                            color: const Color(0xFFE9ECEF),
                                            width: 1.5,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            // Country Code
                                            Container(
                                              width: 75,
                                              height: 56,
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  begin: Alignment.topLeft,
                                                  end: Alignment.bottomRight,
                                                  colors: [
                                                    AppTheme.primaryBlue,
                                                    AppTheme.accentViolet,
                                                  ],
                                                ),
                                                borderRadius:
                                                    const BorderRadius.only(
                                                      topLeft: Radius.circular(
                                                        14,
                                                      ),
                                                      bottomLeft:
                                                          Radius.circular(14),
                                                    ),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  '+91',
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.w700,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            ),

                                            // Phone Number Input
                                            Expanded(
                                              child: TextFormField(
                                                controller: _phoneController,
                                                keyboardType:
                                                    TextInputType.phone,
                                                maxLength: 10,
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w600,
                                                  color: AppTheme.textRichBlack,
                                                  letterSpacing: 0.8,
                                                ),
                                                decoration: InputDecoration(
                                                  hintText: '98765 43210',
                                                  hintStyle: TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                    color:
                                                        AppTheme.secondaryText,
                                                    letterSpacing: 0.8,
                                                  ),
                                                  counterText: '',
                                                  errorStyle: const TextStyle(
                                                    height: 0,
                                                  ),
                                                  contentPadding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 16,
                                                        vertical: 16,
                                                      ),
                                                  enabledBorder: OutlineInputBorder(
                                                    borderRadius:
                                                        const BorderRadius.only(
                                                          topRight:
                                                              Radius.circular(
                                                                12,
                                                              ),
                                                          bottomRight:
                                                              Radius.circular(
                                                                12,
                                                              ),
                                                        ),
                                                    borderSide: BorderSide(
                                                      color: Colors.grey,
                                                    ),
                                                  ),
                                                  focusedBorder: OutlineInputBorder(
                                                    borderRadius:
                                                        const BorderRadius.only(
                                                          topRight:
                                                              Radius.circular(
                                                                12,
                                                              ),
                                                          bottomRight:
                                                              Radius.circular(
                                                                12,
                                                              ),
                                                        ),
                                                    borderSide: BorderSide(
                                                      color: Colors.blue,
                                                    ),
                                                  ),
                                                  border: OutlineInputBorder(
                                                    borderRadius:
                                                        const BorderRadius.only(
                                                          topRight:
                                                              Radius.circular(
                                                                12,
                                                              ),
                                                          bottomRight:
                                                              Radius.circular(
                                                                12,
                                                              ),
                                                        ),
                                                    borderSide: BorderSide(
                                                      color: Colors.grey,
                                                    ),
                                                  ),
                                                ),
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .digitsOnly,
                                                  LengthLimitingTextInputFormatter(
                                                    10,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      const SizedBox(height: 12),

                                      // Helper Text with validation
                                      Builder(
                                        builder: (context) {
                                          String message =
                                              'We\'ll send a verification code to this number';
                                          Color color = AppTheme.secondaryText;

                                          final phoneText =
                                              _phoneController.text;
                                          if (phoneText.isNotEmpty) {
                                            if (phoneText.length != 10) {
                                              message = localizations.phoneNumberValidation;
                                              color = AppTheme.errorRed;
                                            } else if (!RegExp(
                                              r'^[6-9]\d{9}$',
                                            ).hasMatch(phoneText)) {
                                              message = localizations.phoneNumberValidation;
                                              color = AppTheme.errorRed;
                                            } else {
                                              message = '✓ Valid phone number';
                                              color = Colors.green;
                                            }
                                          }

                                          return Text(
                                            message,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: color,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              const SizedBox(height: 30),

                              // Modern Send OTP Button
                              Container(
                                width: double.infinity,
                                height: 56,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      AppTheme.primaryBlue,
                                      AppTheme.accentViolet,
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppTheme.primaryBlue.withValues(
                                        alpha: 0.4,
                                      ),
                                      blurRadius: 20,
                                      offset: const Offset(0, 8),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(20),
                                    onTap:
                                        _isLoading
                                            ? null
                                            : () {
                                              // Manual validation without using form validation to prevent layout shift
                                              final phoneText =
                                                  _phoneController.text.trim();

                                              if (phoneText.isEmpty) {
                                                ScaffoldMessenger.of(
                                                  context,
                                                ).showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                      localizations.enterPhoneNumber,
                                                      style: AppTheme.bodyMedium
                                                          .copyWith(
                                                            color: Colors.white,
                                                          ),
                                                    ),
                                                    backgroundColor:
                                                        AppTheme.errorRed,
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            12,
                                                          ),
                                                    ),
                                                  ),
                                                );
                                                return;
                                              }

                                              if (phoneText.length != 10) {
                                                ScaffoldMessenger.of(
                                                  context,
                                                ).showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                      localizations.phoneNumberValidation,
                                                      style: AppTheme.bodyMedium
                                                          .copyWith(
                                                            color: Colors.white,
                                                          ),
                                                    ),
                                                    backgroundColor:
                                                        AppTheme.errorRed,
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            12,
                                                          ),
                                                    ),
                                                  ),
                                                );
                                                return;
                                              }

                                              if (!RegExp(
                                                r'^[6-9]\d{9}$',
                                              ).hasMatch(phoneText)) {
                                                ScaffoldMessenger.of(
                                                  context,
                                                ).showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                      localizations.phoneNumberValidation,
                                                      style: AppTheme.bodyMedium
                                                          .copyWith(
                                                            color: Colors.white,
                                                          ),
                                                    ),
                                                    backgroundColor:
                                                        AppTheme.errorRed,
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            12,
                                                          ),
                                                    ),
                                                  ),
                                                );
                                                return;
                                              }

                                              // Check rate limiting
                                              if (_canSendRequest()) {
                                                // Construct full phone number with +91 prefix
                                                final fullPhoneNumber =
                                                    '+91$phoneText';

                                                // Send OTP directly without showing a dialog
                                                context.read<AuthBloc>().add(
                                                  SendOtpEvent(
                                                    phoneNumber:
                                                        fullPhoneNumber,
                                                  ),
                                                );

                                                // Show a snackbar to inform the user
                                                ScaffoldMessenger.of(
                                                  context,
                                                ).showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                      'Sending OTP to $fullPhoneNumber...',
                                                      style: AppTheme.bodyMedium
                                                          .copyWith(
                                                            color: Colors.white,
                                                          ),
                                                    ),
                                                    duration: const Duration(
                                                      seconds: 2,
                                                    ),
                                                    backgroundColor:
                                                        AppTheme.primaryBlue,
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            12,
                                                          ),
                                                    ),
                                                  ),
                                                );
                                              } else {
                                                // Show rate limit exceeded message
                                                ScaffoldMessenger.of(
                                                  context,
                                                ).showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                      'Too many requests. Please wait a moment before trying again.',
                                                      style: AppTheme.bodyMedium
                                                          .copyWith(
                                                            color: Colors.white,
                                                          ),
                                                    ),
                                                    duration: const Duration(
                                                      seconds: 3,
                                                    ),
                                                    backgroundColor:
                                                        AppTheme.errorRed,
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            12,
                                                          ),
                                                    ),
                                                  ),
                                                );
                                              }
                                            },
                                    child: Center(
                                      child:
                                          _isLoading
                                              ? const SizedBox(
                                                width: 24,
                                                height: 24,
                                                child:
                                                    CircularProgressIndicator(
                                                      color: Colors.white,
                                                      strokeWidth: 2.5,
                                                    ),
                                              )
                                              : Text(
                                                localizations.sendOTP,
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w700,
                                                  color: Colors.white,
                                                  letterSpacing: 0.5,
                                                ),
                                              ),
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(height: 30),

                              // Modern Footer with Clickable Links
                              Center(
                                child: Column(
                                  children: [
                                    Text(
                                      'By continuing, you agree to our',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppTheme.secondaryText,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        GestureDetector(
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) => const WebViewPage(
                                                  url: 'https://quickposters.in/terms-of-service.html',
                                                  title: 'Terms of Service',
                                                ),
                                              ),
                                            );
                                          },
                                          child: Text(
                                            'Terms of Service',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: AppTheme.primaryBlue,
                                              fontWeight: FontWeight.w600,
                                              decoration: TextDecoration.underline,
                                            ),
                                          ),
                                        ),
                                        Text(
                                          ' & ',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: AppTheme.secondaryText,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                        GestureDetector(
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) => const WebViewPage(
                                                  url: 'https://quickposters.in/privacy-policy.html',
                                                  title: 'Privacy Policy',
                                                ),
                                              ),
                                            );
                                          },
                                          child: Text(
                                            'Privacy Policy',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: AppTheme.primaryBlue,
                                              fontWeight: FontWeight.w600,
                                              decoration: TextDecoration.underline,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),

                              const SizedBox(height: 20),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
