import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class OtpSent extends AuthState {
  final String verificationId;
  final int? resendToken;

  const OtpSent({
    required this.verificationId,
    this.resendToken,
  });

  @override
  List<Object?> get props => [verificationId, resendToken];
}

class AuthSuccess extends AuthState {
  final User user;
  final bool isNewUser;

  const AuthSuccess({
    required this.user,
    this.isNewUser = false,
  });

  @override
  List<Object?> get props => [user, isNewUser];
}

class AuthFailure extends AuthState {
  final String message;

  const AuthFailure({required this.message});

  @override
  List<Object?> get props => [message];
}

class SignedOut extends AuthState {}
