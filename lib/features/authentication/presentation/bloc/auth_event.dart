import 'package:equatable/equatable.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class SendOtpEvent extends AuthEvent {
  final String phoneNumber;

  const SendOtpEvent({required this.phoneNumber});

  @override
  List<Object?> get props => [phoneNumber];
}

class VerifyOtpEvent extends AuthEvent {
  final String otp;
  final String verificationId;

  const VerifyOtpEvent({
    required this.otp,
    required this.verificationId,
  });

  @override
  List<Object?> get props => [otp, verificationId];
}

class SignOutEvent extends AuthEvent {}

class CheckAuthStatusEvent extends AuthEvent {}

/// Event that does nothing, used to avoid duplicate events when rebuilding providers
class NoOpAuthEvent extends AuthEvent {
  const NoOpAuthEvent();
}
