import 'dart:math';
import 'package:vector_math/vector_math_64.dart';

/// Extension methods for Matrix4 to extract transformation components
extension Matrix4Extensions on Matrix4 {
  /// Get the translation component of the matrix
  Vector3 getTranslation() {
    return Vector3(this[12], this[13], this[14]);
  }

  /// Get the rotation angle around the Z axis in radians
  double getRotationZ() {
    // Extract rotation from the upper-left 2x2 matrix
    final double a = this[0];
    final double b = this[1];

    // Calculate the rotation angle
    return atan2(b, a);
  }

  /// Get the maximum scale factor from the matrix
  double getMaxScaleOnAxis() {
    final double scaleX = Vector3(this[0], this[1], this[2]).length;
    final double scaleY = Vector3(this[4], this[5], this[6]).length;
    final double scaleZ = Vector3(this[8], this[9], this[10]).length;

    return max(max(scaleX, scaleY), scaleZ);
  }
}
