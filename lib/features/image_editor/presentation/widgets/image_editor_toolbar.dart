import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../domain/entities/editable_image.dart';
import '../bloc/image_editor_bloc.dart';
import '../bloc/image_editor_event.dart';

/// A toolbar with editing options for the selected image
class ImageEditorToolbar extends StatelessWidget {
  final EditableImage? selectedImage;

  const ImageEditorToolbar({
    Key? key,
    this.selectedImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle for dragging
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Toolbar title
          Text(
            selectedImage == null
                ? 'Image Editor'
                : selectedImage!.isBaseImage
                    ? 'Base Image'
                    : 'Edit Image',
            style: AppTheme.headingSmall,
          ),
          
          const SizedBox(height: 16),
          
          // Toolbar actions
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildToolbarButton(
                  context,
                  icon: Icons.add_photo_alternate,
                  label: 'Add Image',
                  onTap: () {
                    context.read<ImageEditorBloc>().add(
                          const PickImageEvent(isBaseImage: false),
                        );
                  },
                ),
                
                if (selectedImage != null && !selectedImage!.isBaseImage) ...[
                  _buildToolbarButton(
                    context,
                    icon: Icons.crop,
                    label: 'Crop',
                    onTap: () {
                      context.read<ImageEditorBloc>().add(
                            CropImageEvent(image: selectedImage!),
                          );
                    },
                  ),
                  
                  _buildToolbarButton(
                    context,
                    icon: Icons.rotate_90_degrees_ccw,
                    label: 'Rotate',
                    onTap: () {
                      // Rotate 90 degrees (π/2 radians)
                      final newRotation = selectedImage!.rotation + (3.14159 / 2);
                      context.read<ImageEditorBloc>().add(
                            RotateImageEvent(
                              image: selectedImage!,
                              angle: newRotation,
                            ),
                          );
                    },
                  ),
                  
                  _buildToolbarButton(
                    context,
                    icon: Icons.layers,
                    label: 'Bring to Front',
                    onTap: () {
                      context.read<ImageEditorBloc>().add(
                            ChangeImageLayerEvent(
                              image: selectedImage!,
                              bringToFront: true,
                            ),
                          );
                    },
                  ),
                  
                  _buildToolbarButton(
                    context,
                    icon: Icons.delete,
                    label: 'Remove',
                    onTap: () {
                      context.read<ImageEditorBloc>().add(
                            RemoveImageFromCanvasEvent(image: selectedImage!),
                          );
                    },
                  ),
                ],
                
                if (selectedImage == null || selectedImage!.isBaseImage)
                  _buildToolbarButton(
                    context,
                    icon: Icons.image,
                    label: 'Change Base',
                    onTap: () {
                      context.read<ImageEditorBloc>().add(
                            const PickImageEvent(isBaseImage: true),
                          );
                    },
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(30),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                shape: BoxShape.circle,
                boxShadow: AppTheme.lightShadow,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
