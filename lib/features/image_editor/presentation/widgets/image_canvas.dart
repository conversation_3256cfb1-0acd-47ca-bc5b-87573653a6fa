import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:matrix_gesture_detector/matrix_gesture_detector.dart';
import '../../domain/entities/editable_image.dart';
import '../bloc/image_editor_bloc.dart';
import '../bloc/image_editor_event.dart';
import '../bloc/image_editor_state.dart';
import 'matrix_extensions.dart';

/// A widget that displays and allows manipulation of images on a canvas
class ImageCanvas extends StatelessWidget {
  final List<EditableImage> images;
  final EditableImage? selectedImage;
  final double canvasWidth;
  final double canvasHeight;
  final VoidCallback? onTap;

  const ImageCanvas({
    super.key,
    required this.images,
    this.selectedImage,
    required this.canvasWidth,
    required this.canvasHeight,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: canvasWidth,
        height: canvasHeight,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRect(
          child: Stack(
            children: images.map((image) {
              return _buildImageLayer(context, image);
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildImageLayer(BuildContext context, EditableImage image) {
    final isSelected = selectedImage?.id == image.id;
    final imageWidget = _buildTransformedImage(context, image);

    // Base image is not selectable or movable
    if (image.isBaseImage) {
      return Positioned.fill(
        child: imageWidget,
      );
    }

    return Positioned(
      left: image.position.dx,
      top: image.position.dy,
      child: GestureDetector(
        onTap: () {
          // Select this image
          if (!isSelected) {
            final currentState = context.read<ImageEditorBloc>().state;
            if (currentState is ImageEditorLoaded) {
              context.read<ImageEditorBloc>().add(
                    UpdateImagePositionEvent(
                      image: image,
                      position: image.position,
                    ),
                  );
            }
          }
        },
        child: Container(
          decoration: isSelected
              ? BoxDecoration(
                  border: Border.all(
                    color: Colors.blue,
                    width: 2,
                  ),
                )
              : null,
          child: imageWidget,
        ),
      ),
    );
  }

  Widget _buildTransformedImage(BuildContext context, EditableImage image) {
    // For base image, we just show it as is
    if (image.isBaseImage) {
      return _buildImageWidget(image);
    }

    // For other images, we allow transformation
    return MatrixGestureDetector(
      onMatrixUpdate: (Matrix4 matrix, Matrix4 translationDeltaMatrix, Matrix4 scaleDeltaMatrix, Matrix4 rotationDeltaMatrix) {
        // Extract translation from the matrix
        final translation = matrix.getTranslation();
        final scale = matrix.getMaxScaleOnAxis();

        // Calculate rotation angle (simplified)
        final rotationZ = matrix.getRotationZ();

        // Update the image position, scale, and rotation
        context.read<ImageEditorBloc>().add(
              UpdateImagePositionEvent(
                image: image,
                position: Offset(translation.x, translation.y),
              ),
            );

        context.read<ImageEditorBloc>().add(
              UpdateImageScaleEvent(
                image: image,
                scale: scale,
              ),
            );

        context.read<ImageEditorBloc>().add(
              RotateImageEvent(
                image: image,
                angle: rotationZ,
              ),
            );
      },
      child: Transform(
        transform: Matrix4.identity()
          ..translate(0.0, 0.0)
          ..scale(image.scale)
          ..rotateZ(image.rotation),
        alignment: Alignment.center,
        child: _buildImageWidget(image),
      ),
    );
  }

  Widget _buildImageWidget(EditableImage image) {
    if (image.file != null) {
      return Image.file(
        image.file!,
        fit: BoxFit.contain,
      );
    } else if (image.networkUrl != null) {
      return Image.network(
        image.networkUrl!,
        fit: BoxFit.contain,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
            ),
          );
        },
      );
    } else if (image.assetPath != null) {
      return Image.asset(
        image.assetPath!,
        fit: BoxFit.contain,
      );
    }

    // Fallback
    return const SizedBox();
  }
}
