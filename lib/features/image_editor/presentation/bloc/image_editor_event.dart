import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../domain/entities/editable_image.dart';

/// Base class for all image editor events
abstract class ImageEditorEvent extends Equatable {
  const ImageEditorEvent();

  @override
  List<Object?> get props => [];
}

/// Event to pick an image from gallery or camera
class PickImageEvent extends ImageEditorEvent {
  final bool isCamera;
  final bool isBaseImage;

  const PickImageEvent({
    this.isCamera = false,
    this.isBaseImage = false,
  });

  @override
  List<Object?> get props => [isCamera, isBaseImage];
}

/// Event to crop the selected image
class CropImageEvent extends ImageEditorEvent {
  final EditableImage image;

  const CropImageEvent({required this.image});

  @override
  List<Object?> get props => [image];
}

/// Event to rotate the selected image
class RotateImageEvent extends ImageEditorEvent {
  final EditableImage image;
  final double angle; // Rotation angle in radians

  const RotateImageEvent({
    required this.image,
    required this.angle,
  });

  @override
  List<Object?> get props => [image, angle];
}

/// Event to update the position of an image on the canvas
class UpdateImagePositionEvent extends ImageEditorEvent {
  final EditableImage image;
  final Offset position;

  const UpdateImagePositionEvent({
    required this.image,
    required this.position,
  });

  @override
  List<Object?> get props => [image, position];
}

/// Event to update the scale of an image
class UpdateImageScaleEvent extends ImageEditorEvent {
  final EditableImage image;
  final double scale;

  const UpdateImageScaleEvent({
    required this.image,
    required this.scale,
  });

  @override
  List<Object?> get props => [image, scale];
}

/// Event to add an image to the canvas
class AddImageToCanvasEvent extends ImageEditorEvent {
  final File imageFile;
  final bool isBaseImage;

  const AddImageToCanvasEvent({
    required this.imageFile,
    this.isBaseImage = false,
  });

  @override
  List<Object?> get props => [imageFile, isBaseImage];
}

/// Event to remove an image from the canvas
class RemoveImageFromCanvasEvent extends ImageEditorEvent {
  final EditableImage image;

  const RemoveImageFromCanvasEvent({required this.image});

  @override
  List<Object?> get props => [image];
}

/// Event to change the layer order of an image
class ChangeImageLayerEvent extends ImageEditorEvent {
  final EditableImage image;
  final bool bringToFront; // If true, bring to front; if false, send to back

  const ChangeImageLayerEvent({
    required this.image,
    required this.bringToFront,
  });

  @override
  List<Object?> get props => [image, bringToFront];
}

/// Event to reset the editor state
class ResetEditorEvent extends ImageEditorEvent {}
