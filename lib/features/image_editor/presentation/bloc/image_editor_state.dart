import 'package:equatable/equatable.dart';
import '../../domain/entities/editable_image.dart';

/// Base class for all image editor states
abstract class ImageEditorState extends Equatable {
  const ImageEditorState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the image editor
class ImageEditorInitial extends ImageEditorState {}

/// State when the editor is loading (e.g., picking or processing an image)
class ImageEditorLoading extends ImageEditorState {
  final String message;

  const ImageEditorLoading({this.message = 'Loading...'});

  @override
  List<Object?> get props => [message];
}

/// State when an error occurs in the editor
class ImageEditorError extends ImageEditorState {
  final String message;

  const ImageEditorError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// State when images are loaded and ready for editing
class ImageEditorLoaded extends ImageEditorState {
  final List<EditableImage> images;
  final EditableImage? selectedImage;
  final bool isBaseImageSet;

  const ImageEditorLoaded({
    required this.images,
    this.selectedImage,
    this.isBaseImageSet = false,
  });

  /// Creates a copy of this state with the given fields replaced with new values
  ImageEditorLoaded copyWith({
    List<EditableImage>? images,
    EditableImage? selectedImage,
    bool? isBaseImageSet,
  }) {
    return ImageEditorLoaded(
      images: images ?? this.images,
      selectedImage: selectedImage ?? this.selectedImage,
      isBaseImageSet: isBaseImageSet ?? this.isBaseImageSet,
    );
  }

  @override
  List<Object?> get props => [images, selectedImage, isBaseImageSet];
}
