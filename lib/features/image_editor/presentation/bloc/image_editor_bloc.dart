import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:image_cropper/image_cropper.dart'; // Commented out due to compatibility issues
import 'package:image_picker/image_picker.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/editable_image.dart';
import 'image_editor_event.dart';
import 'image_editor_state.dart';

class ImageEditorBloc extends Bloc<ImageEditorEvent, ImageEditorState> {
  final ImagePicker _imagePicker = ImagePicker();
  final uuid = const Uuid();

  ImageEditorBloc() : super(ImageEditorInitial()) {
    on<PickImageEvent>(_onPickImage);
    on<CropImageEvent>(_onCropImage);
    on<RotateImageEvent>(_onRotateImage);
    on<UpdateImagePositionEvent>(_onUpdateImagePosition);
    on<UpdateImageScaleEvent>(_onUpdateImageScale);
    on<AddImageToCanvasEvent>(_onAddImageToCanvas);
    on<RemoveImageFromCanvasEvent>(_onRemoveImageFromCanvas);
    on<ChangeImageLayerEvent>(_onChangeImageLayer);
    on<ResetEditorEvent>(_onResetEditor);
  }

  Future<void> _onPickImage(
    PickImageEvent event,
    Emitter<ImageEditorState> emit,
  ) async {
    try {
      emit(const ImageEditorLoading(message: 'Picking image...'));

      final XFile? pickedFile = await _imagePicker.pickImage(
        source: event.isCamera ? ImageSource.camera : ImageSource.gallery,
        imageQuality: 85,
      );

      if (pickedFile == null) {
        // User canceled the picker
        if (state is ImageEditorLoaded) {
          emit(state);
        } else {
          emit(ImageEditorInitial());
        }
        return;
      }

      final File imageFile = File(pickedFile.path);
      add(AddImageToCanvasEvent(
        imageFile: imageFile,
        isBaseImage: event.isBaseImage,
      ));

    } catch (e) {
      emit(ImageEditorError(message: 'Failed to pick image: $e'));
    }
  }

  Future<void> _onCropImage(
    CropImageEvent event,
    Emitter<ImageEditorState> emit,
  ) async {
    try {
      emit(const ImageEditorLoading(message: 'Cropping image...'));

      final File? imageFile = event.image.file;
      if (imageFile == null) {
        emit(const ImageEditorError(message: 'No image file to crop'));
        return;
      }

      // Image cropper functionality removed due to compatibility issues
      // Instead, we'll just use the original image
      final File croppedFile = imageFile;

      if (state is ImageEditorLoaded) {
        final currentState = state as ImageEditorLoaded;
        final updatedImages = currentState.images.map((image) {
          if (image.id == event.image.id) {
            return image.copyWith(file: croppedFile);
          }
          return image;
        }).toList();

        final updatedSelectedImage = event.image.copyWith(
          file: croppedFile,
        );

        emit(currentState.copyWith(
          images: updatedImages,
          selectedImage: updatedSelectedImage,
        ));
      }

    } catch (e) {
      emit(ImageEditorError(message: 'Failed to crop image: $e'));
    }
  }

  void _onRotateImage(
    RotateImageEvent event,
    Emitter<ImageEditorState> emit,
  ) {
    if (state is ImageEditorLoaded) {
      final currentState = state as ImageEditorLoaded;
      final updatedImages = currentState.images.map((image) {
        if (image.id == event.image.id) {
          return image.copyWith(rotation: event.angle);
        }
        return image;
      }).toList();

      final updatedSelectedImage = event.image.copyWith(
        rotation: event.angle,
      );

      emit(currentState.copyWith(
        images: updatedImages,
        selectedImage: updatedSelectedImage,
      ));
    }
  }

  void _onUpdateImagePosition(
    UpdateImagePositionEvent event,
    Emitter<ImageEditorState> emit,
  ) {
    if (state is ImageEditorLoaded) {
      final currentState = state as ImageEditorLoaded;
      final updatedImages = currentState.images.map((image) {
        if (image.id == event.image.id) {
          return image.copyWith(position: event.position);
        }
        return image;
      }).toList();

      final updatedSelectedImage = event.image.copyWith(
        position: event.position,
      );

      emit(currentState.copyWith(
        images: updatedImages,
        selectedImage: updatedSelectedImage,
      ));
    }
  }

  void _onUpdateImageScale(
    UpdateImageScaleEvent event,
    Emitter<ImageEditorState> emit,
  ) {
    if (state is ImageEditorLoaded) {
      final currentState = state as ImageEditorLoaded;
      final updatedImages = currentState.images.map((image) {
        if (image.id == event.image.id) {
          return image.copyWith(scale: event.scale);
        }
        return image;
      }).toList();

      final updatedSelectedImage = event.image.copyWith(
        scale: event.scale,
      );

      emit(currentState.copyWith(
        images: updatedImages,
        selectedImage: updatedSelectedImage,
      ));
    }
  }

  void _onAddImageToCanvas(
    AddImageToCanvasEvent event,
    Emitter<ImageEditorState> emit,
  ) {
    try {
      final String imageId = uuid.v4();
      final newImage = EditableImage(
        file: event.imageFile,
        id: imageId,
        isBaseImage: event.isBaseImage,
        zIndex: event.isBaseImage ? 0 : 1,
      );

      if (state is ImageEditorLoaded) {
        final currentState = state as ImageEditorLoaded;

        // If this is a base image and we already have one, replace it
        List<EditableImage> updatedImages = [...currentState.images];
        if (event.isBaseImage) {
          updatedImages.removeWhere((image) => image.isBaseImage);
        }

        // Add the new image
        updatedImages.add(newImage);

        // Sort by z-index
        updatedImages.sort((a, b) => a.zIndex.compareTo(b.zIndex));

        emit(currentState.copyWith(
          images: updatedImages,
          selectedImage: newImage,
          isBaseImageSet: event.isBaseImage || currentState.isBaseImageSet,
        ));
      } else {
        emit(ImageEditorLoaded(
          images: [newImage],
          selectedImage: newImage,
          isBaseImageSet: event.isBaseImage,
        ));
      }
    } catch (e) {
      emit(ImageEditorError(message: 'Failed to add image to canvas: $e'));
    }
  }

  void _onRemoveImageFromCanvas(
    RemoveImageFromCanvasEvent event,
    Emitter<ImageEditorState> emit,
  ) {
    if (state is ImageEditorLoaded) {
      final currentState = state as ImageEditorLoaded;

      // Don't allow removing the base image if it's the only image
      if (event.image.isBaseImage && currentState.images.length == 1) {
        emit(const ImageEditorError(
          message: 'Cannot remove the base image. Add another image first.',
        ));
        emit(currentState); // Revert to previous state
        return;
      }

      final updatedImages = currentState.images
          .where((image) => image.id != event.image.id)
          .toList();

      // Update isBaseImageSet flag
      final stillHasBaseImage = updatedImages.any((image) => image.isBaseImage);

      // Select another image if the selected one was removed
      EditableImage? newSelectedImage;
      if (currentState.selectedImage?.id == event.image.id) {
        newSelectedImage = updatedImages.isNotEmpty ? updatedImages.last : null;
      } else {
        newSelectedImage = currentState.selectedImage;
      }

      emit(currentState.copyWith(
        images: updatedImages,
        selectedImage: newSelectedImage,
        isBaseImageSet: stillHasBaseImage,
      ));
    }
  }

  void _onChangeImageLayer(
    ChangeImageLayerEvent event,
    Emitter<ImageEditorState> emit,
  ) {
    if (state is ImageEditorLoaded) {
      final currentState = state as ImageEditorLoaded;

      // Don't change z-index of base image
      if (event.image.isBaseImage) {
        return;
      }

      final List<EditableImage> updatedImages = [...currentState.images];
      final int currentIndex = updatedImages.indexWhere(
        (image) => image.id == event.image.id,
      );

      if (currentIndex == -1) return;

      // Find the maximum and minimum z-index values (excluding base image)
      final nonBaseImages = updatedImages.where((img) => !img.isBaseImage).toList();
      if (nonBaseImages.isEmpty) return;

      final int maxZIndex = nonBaseImages.map((img) => img.zIndex).reduce(max);
      final int minZIndex = nonBaseImages.map((img) => img.zIndex).reduce(min);

      // Update the z-index of the image
      final EditableImage updatedImage = event.image.copyWith(
        zIndex: event.bringToFront ? maxZIndex + 1 : minZIndex - 1,
      );

      updatedImages[currentIndex] = updatedImage;

      // Sort by z-index
      updatedImages.sort((a, b) => a.zIndex.compareTo(b.zIndex));

      emit(currentState.copyWith(
        images: updatedImages,
        selectedImage: updatedImage,
      ));
    }
  }

  void _onResetEditor(
    ResetEditorEvent event,
    Emitter<ImageEditorState> emit,
  ) {
    emit(ImageEditorInitial());
  }
}
