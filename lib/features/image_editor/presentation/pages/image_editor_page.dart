import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../bloc/image_editor_bloc.dart';
import '../bloc/image_editor_event.dart';
import '../bloc/image_editor_state.dart';
import '../widgets/image_canvas.dart';
import '../widgets/image_editor_toolbar.dart';

class ImageEditorPage extends StatefulWidget {
  const ImageEditorPage({Key? key}) : super(key: key);

  @override
  State<ImageEditorPage> createState() => _ImageEditorPageState();
}

class _ImageEditorPageState extends State<ImageEditorPage> {
  late final ImageEditorBloc _imageEditorBloc;

  @override
  void initState() {
    super.initState();
    _imageEditorBloc = ImageEditorBloc();
  }

  @override
  void dispose() {
    _imageEditorBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final canvasWidth = screenSize.width * 0.9;
    final canvasHeight = screenSize.height * 0.6;

    return BlocProvider(
      create: (context) => _imageEditorBloc,
      child: Scaffold(
        appBar: AppTheme.gradientAppBar(
          title: 'Image Editor',
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                _imageEditorBloc.add(ResetEditorEvent());
              },
            ),
          ],
        ),
        body: BlocConsumer<ImageEditorBloc, ImageEditorState>(
          listener: (context, state) {
            if (state is ImageEditorError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          builder: (context, state) {
            if (state is ImageEditorInitial) {
              return _buildInitialState(context);
            } else if (state is ImageEditorLoading) {
              return _buildLoadingState(state);
            } else if (state is ImageEditorLoaded) {
              return _buildLoadedState(context, state, canvasWidth, canvasHeight);
            } else {
              return _buildInitialState(context);
            }
          },
        ),
      ),
    );
  }

  Widget _buildInitialState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.image,
            size: 100,
            color: Colors.grey,
          ),
          const SizedBox(height: 24),
          Text(
            AppLocalizations.of(context)!.startByAddingBaseImage,
            style: AppTheme.headingMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.backgroundOfPoster,
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.secondaryText),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          GradientButton(
            text: AppLocalizations.of(context)!.selectBaseImage,
            icon: const Icon(Icons.add_photo_alternate, color: Colors.white),
            onPressed: () {
              context.read<ImageEditorBloc>().add(
                    const PickImageEvent(isBaseImage: true),
                  );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(ImageEditorLoading state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            state.message,
            style: AppTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(
    BuildContext context,
    ImageEditorLoaded state,
    double canvasWidth,
    double canvasHeight,
  ) {
    return Column(
      children: [
        Expanded(
          child: Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ImageCanvas(
                      images: state.images,
                      selectedImage: state.selectedImage,
                      canvasWidth: canvasWidth,
                      canvasHeight: canvasHeight,
                    ),
                    const SizedBox(height: 16),
                    if (!state.isBaseImageSet)
                      GradientButton(
                        text: AppLocalizations.of(context)!.addBaseImage,
                        onPressed: () {
                          context.read<ImageEditorBloc>().add(
                                const PickImageEvent(isBaseImage: true),
                              );
                        },
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
        ImageEditorToolbar(selectedImage: state.selectedImage),
      ],
    );
  }
}
