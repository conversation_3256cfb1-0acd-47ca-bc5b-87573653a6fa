import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Represents an image that can be edited in the app
class EditableImage extends Equatable {
  /// The image file
  final File? file;
  
  /// The network image URL (if loaded from network)
  final String? networkUrl;
  
  /// The asset image path (if loaded from assets)
  final String? assetPath;
  
  /// The position of the image on the canvas
  final Offset position;
  
  /// The scale of the image
  final double scale;
  
  /// The rotation angle in radians
  final double rotation;
  
  /// Unique identifier for the image
  final String id;
  
  /// Whether this is the base/background image
  final bool isBaseImage;
  
  /// Z-index for layering (higher values are on top)
  final int zIndex;

  const EditableImage({
    this.file,
    this.networkUrl,
    this.assetPath,
    this.position = Offset.zero,
    this.scale = 1.0,
    this.rotation = 0.0,
    required this.id,
    this.isBaseImage = false,
    this.zIndex = 0,
  }) : assert(
          file != null || networkUrl != null || assetPath != null,
          'At least one image source must be provided',
        );

  /// Creates a copy of this EditableImage with the given fields replaced with new values
  EditableImage copyWith({
    File? file,
    String? networkUrl,
    String? assetPath,
    Offset? position,
    double? scale,
    double? rotation,
    String? id,
    bool? isBaseImage,
    int? zIndex,
  }) {
    return EditableImage(
      file: file ?? this.file,
      networkUrl: networkUrl ?? this.networkUrl,
      assetPath: assetPath ?? this.assetPath,
      position: position ?? this.position,
      scale: scale ?? this.scale,
      rotation: rotation ?? this.rotation,
      id: id ?? this.id,
      isBaseImage: isBaseImage ?? this.isBaseImage,
      zIndex: zIndex ?? this.zIndex,
    );
  }

  @override
  List<Object?> get props => [
        file?.path,
        networkUrl,
        assetPath,
        position,
        scale,
        rotation,
        id,
        isBaseImage,
        zIndex,
      ];
}
