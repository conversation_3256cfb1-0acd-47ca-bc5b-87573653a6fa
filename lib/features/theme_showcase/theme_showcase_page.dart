import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';
import '../../core/widgets/fancy_card.dart';
import '../../core/widgets/gradient_button.dart';
import '../../core/widgets/gradient_container.dart';

/// A page to showcase the app's theme components
class ThemeShowcasePage extends StatelessWidget {
  const ThemeShowcasePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppTheme.gradientAppBar(
        title: 'QuickPosters Theme',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('Color Palette'),
            _buildColorPalette(),

            _buildSectionTitle('Typography'),
            _buildTypography(),

            _buildSectionTitle('Buttons'),
            _buildButtons(context),

            _buildSectionTitle('Cards'),
            _buildCards(),

            _buildSectionTitle('Containers'),
            _buildContainers(),

            _buildSectionTitle('Form Elements'),
            _buildFormElements(),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Text(
        title,
        style: AppTheme.headingMedium,
      ),
    );
  }

  Widget _buildColorPalette() {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: [
        _buildColorItem('Primary Blue', AppTheme.primaryBlue),
        _buildColorItem('Accent Violet', AppTheme.accentViolet),
        _buildColorItem('Gradient Mid', AppTheme.gradientMidBlueViolet),
        _buildColorItem('Background', AppTheme.backgroundWhite),
        _buildColorItem('Light BG', AppTheme.lightGradientBg),
        _buildColorItem('Text Rich', AppTheme.textRichBlack),
        _buildColorItem('Text Secondary', AppTheme.secondaryText),
      ],
    );
  }

  Widget _buildColorItem(String name, Color color) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(16),
            boxShadow: AppTheme.lightShadow,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          name,
          style: AppTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTypography() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Heading Large', style: AppTheme.headingLarge),
        const SizedBox(height: 8),
        Text('Heading Medium', style: AppTheme.headingMedium),
        const SizedBox(height: 8),
        Text('Heading Small', style: AppTheme.headingSmall),
        const SizedBox(height: 16),
        Text('Body Large', style: AppTheme.bodyLarge),
        const SizedBox(height: 8),
        Text('Body Medium', style: AppTheme.bodyMedium),
        const SizedBox(height: 8),
        Text('Body Small', style: AppTheme.bodySmall),
        const SizedBox(height: 8),
        Text('Button Text', style: AppTheme.buttonText.copyWith(color: AppTheme.primaryBlue)),
      ],
    );
  }

  Widget _buildButtons(BuildContext context) {
    return Column(
      children: [
        GradientButton(
          text: 'Primary Gradient Button',
          onPressed: () {},
        ),
        const SizedBox(height: 16),
        GradientButton(
          text: 'Accent Gradient Button',
          gradient: AppTheme.accentGradient,
          onPressed: () {},
        ),
        const SizedBox(height: 16),
        GradientButton(
          text: 'Button with Icon',
          icon: const Icon(Icons.add, color: Colors.white),
          onPressed: () {},
        ),
        const SizedBox(height: 16),
        GradientBorderButton(
          text: 'Gradient Border Button',
          onPressed: () {},
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: () {},
          child: const Text('Standard Elevated Button'),
        ),
        const SizedBox(height: 16),
        OutlinedButton(
          onPressed: () {},
          child: const Text('Standard Outlined Button'),
        ),
        const SizedBox(height: 16),
        TextButton(
          onPressed: () {},
          child: const Text('Standard Text Button'),
        ),
      ],
    );
  }

  Widget _buildCards() {
    return Column(
      children: [
        FancyCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Fancy Card', style: AppTheme.headingSmall),
                const SizedBox(height: 8),
                Text(
                  'This is a fancy card with gradient background and shadow.',
                  style: AppTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        FancyCard(
          gradient: AppTheme.accentGradient,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Accent Gradient Card',
                  style: AppTheme.headingSmall.copyWith(color: Colors.white)),
                const SizedBox(height: 8),
                Text(
                  'This card uses the accent gradient.',
                  style: AppTheme.bodyMedium.copyWith(color: Colors.white),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        GradientBorderCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Gradient Border Card', style: AppTheme.headingSmall),
              const SizedBox(height: 8),
              Text(
                'This card has a gradient border.',
                style: AppTheme.bodyMedium,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        FancyCard(
          useGlassmorphism: true,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Glassmorphism Card', style: AppTheme.headingSmall),
                const SizedBox(height: 8),
                Text(
                  'This card has a glassmorphism effect.',
                  style: AppTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContainers() {
    return Column(
      children: [
        GradientContainer(
          padding: const EdgeInsets.all(16),
          child: Text(
            'Gradient Container',
            style: AppTheme.bodyLarge.copyWith(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 16),
        GradientContainer(
          gradient: AppTheme.accentGradient,
          padding: const EdgeInsets.all(16),
          child: Text(
            'Accent Gradient Container',
            style: AppTheme.bodyLarge.copyWith(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 16),
        Stack(
          children: [
            Container(
              height: 100,
              decoration: BoxDecoration(
                image: const DecorationImage(
                  image: NetworkImage('https://picsum.photos/400/100'),
                  fit: BoxFit.cover,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            GradientOverlayContainer(
              height: 100,
              borderRadius: 16,
              overlayOpacity: 0.5,
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'Gradient Overlay Container',
                  style: AppTheme.bodyLarge.copyWith(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFormElements() {
    return Column(
      children: [
        TextFormField(
          decoration: const InputDecoration(
            labelText: 'Text Input',
            hintText: 'Enter some text',
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          decoration: const InputDecoration(
            labelText: 'Email',
            hintText: 'Enter your email',
            prefixIcon: Icon(Icons.email),
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          obscureText: true,
          decoration: const InputDecoration(
            labelText: 'Password',
            hintText: 'Enter your password',
            prefixIcon: Icon(Icons.lock),
            suffixIcon: Icon(Icons.visibility),
          ),
        ),
      ],
    );
  }
}
