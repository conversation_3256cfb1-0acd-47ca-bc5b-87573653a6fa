import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import 'package:url_launcher/url_launcher.dart';

import '../../../../../core/utils/image_saver.dart';
import '../../../../../core/utils/logger.dart';

/// A class that provides utilities for sharing and downloading templates
class SharingUtilities {
  /// Downloads the template as an image to the gallery
  static Future<void> downloadTemplate(
    BuildContext context,
    ScreenshotController screenshotController,
  ) async {
    try {
      // Show loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Saving image to gallery...')),
        );
      }

      // Capture the screenshot
      final Uint8List? imageBytes = await screenshotController.capture();
      if (imageBytes == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to capture image')),
          );
        }
        return;
      }

      // Save the image directly to the gallery using our platform-specific implementation
      final success = await ImageSaver.saveImageToGallery(imageBytes);

      if (context.mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Image saved to gallery successfully')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to save image to gallery')),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save image: $e')),
        );
      }
      AppLogger.error('Failed to save template image', e);
    }
  }

  /// Shares the template to WhatsApp
  static Future<void> shareToWhatsApp(
    BuildContext context,
    ScreenshotController screenshotController,
  ) async {
    try {
      // Capture the screenshot
      final Uint8List? imageBytes = await screenshotController.capture();
      if (imageBytes == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to capture image')),
          );
        }
        return;
      }

      // Get temporary directory
      final directory = await getTemporaryDirectory();
      final imagePath = '${directory.path}/template_${DateTime.now().millisecondsSinceEpoch}.png';

      // Save the image to the temporary directory
      final File imageFile = File(imagePath);
      await imageFile.writeAsBytes(imageBytes);

      // Create WhatsApp URL
      final whatsappUrl = Uri.parse('whatsapp://send?text=Check out this poster from QuickPosters!');

      // Check if WhatsApp is installed
      if (await canLaunchUrl(whatsappUrl)) {
        // Share the image file
        await share_plus.SharePlus.instance.share(
          share_plus.ShareParams(
            text: 'Check out this poster from QuickPosters!',
            files: [share_plus.XFile(imagePath)],
          ),
        );
      } else {
        // WhatsApp not installed
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('WhatsApp not installed')),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to share to WhatsApp: $e')),
        );
      }
      AppLogger.error('Failed to share template to WhatsApp', e);
    }
  }

  /// Shares the template to other apps
  static Future<void> shareTemplate(
    BuildContext context,
    ScreenshotController screenshotController,
  ) async {
    try {
      // Capture the screenshot
      final Uint8List? imageBytes = await screenshotController.capture();
      if (imageBytes == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to capture image')),
          );
        }
        return;
      }

      // Get temporary directory
      final directory = await getTemporaryDirectory();
      final imagePath = '${directory.path}/template_${DateTime.now().millisecondsSinceEpoch}.png';

      // Save the image to the temporary directory
      final File imageFile = File(imagePath);
      await imageFile.writeAsBytes(imageBytes);

      // Share the image file
      await share_plus.SharePlus.instance.share(
        share_plus.ShareParams(
          text: 'Check out this poster from QuickPosters!',
          files: [share_plus.XFile(imagePath)],
        ),
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to share image: $e')),
        );
      }
      AppLogger.error('Failed to share template image', e);
    }
  }
}
