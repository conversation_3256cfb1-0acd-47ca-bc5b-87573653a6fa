import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:io';

import '../../../../../core/theme/app_theme.dart';

// Custom clipper for hexagon shape
class HexagonClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;

    path.moveTo(width * 0.25, 0);
    path.lineTo(width * 0.75, 0);
    path.lineTo(width, height * 0.5);
    path.lineTo(width * 0.75, height);
    path.lineTo(width * 0.25, height);
    path.lineTo(0, height * 0.5);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

/// A widget that handles additional image display, dragging and scaling
class AdditionalImageHandler extends StatefulWidget {
  /// The image data
  final Map<String, dynamic> imageData;
  
  /// Whether the user has premium status
  final bool isPremium;
  
  /// Initial offset for the image
  final Offset initialOffset;
  
  /// Initial scale for the image
  final double initialScale;
  
  /// The shape of the image
  final String imageShape;
  
  /// The size of the image
  final double imageSize;
  
  /// Callback when the scale changes
  final Function(double) onScaleChanged;
  
  /// Callback when the offset changes
  final Function(Offset) onOffsetChanged;
  
  /// Callback when shape selection is requested
  final VoidCallback? onShapeSelectionRequested;

  const AdditionalImageHandler({
    Key? key,
    required this.imageData,
    required this.isPremium,
    this.initialOffset = Offset.zero,
    this.initialScale = 1.0,
    this.imageShape = 'none',
    this.imageSize = 60.0,
    required this.onScaleChanged,
    required this.onOffsetChanged,
    this.onShapeSelectionRequested,
  }) : super(key: key);

  @override
  State<AdditionalImageHandler> createState() => _AdditionalImageHandlerState();
}

class _AdditionalImageHandlerState extends State<AdditionalImageHandler> {
  late Offset _imageOffset;
  late double _imageScale;
  bool _isDraggingImage = false;
  double? _initialScale;

  @override
  void initState() {
    super.initState();
    _imageOffset = widget.initialOffset;
    _imageScale = widget.initialScale;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Use GestureDetector with direct scale handling for immediate scaling and dragging
      behavior: HitTestBehavior.translucent,

      // Handle tap to show shape selection
      onTap: () {
        if (widget.onShapeSelectionRequested != null) {
          widget.onShapeSelectionRequested!();
        } else {
          _showSizeSelectionDialog(context);
        }
      },

      // Handle scale gestures (pinch to zoom)
      onScaleStart: (ScaleStartDetails details) {
        setState(() {
          _isDraggingImage = true;
          _initialScale = _imageScale;
        });
      },
      onScaleUpdate: (ScaleUpdateDetails details) {
        setState(() {
          // Handle position change (dragging)
          if (details.pointerCount == 1) {
            // Single finger drag - update position
            _imageOffset += details.focalPointDelta;
            widget.onOffsetChanged(_imageOffset);
          }

          // Handle scale change (pinch)
          if (details.scale != 1.0 && _initialScale != null) {
            // Calculate new scale with some constraints
            _imageScale = (_initialScale! * details.scale).clamp(0.3, 2.5);
            widget.onScaleChanged(_imageScale);
          }
        });
      },
      onScaleEnd: (ScaleEndDetails details) {
        setState(() {
          _isDraggingImage = false;
          _initialScale = null;
        });
      },
      child: Stack(
        children: [
          // Image container with scaling and shape
          Transform.scale(
            scale: _imageScale,
            child: _buildShapedImage(),
          ),

          // Drag indicator icon (always shown to indicate direct draggability)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(3),
              decoration: BoxDecoration(
                color: _isDraggingImage
                    ? Colors.blue.withAlpha(220)
                    : Colors.blue.withAlpha(180),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.zoom_out_map,
                color: Colors.white,
                size: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the image with the selected shape
  Widget _buildShapedImage() {
    Widget imageWidget = widget.imageData['source'] == 'gallery'
        ? Image.file(
            File(widget.imageData['path']),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Colors.grey[200],
                child: const Icon(Icons.error, size: 20),
              );
            },
          )
        : CachedNetworkImage(
            imageUrl: widget.imageData['url'],
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.grey[200],
              child: const Center(child: CircularProgressIndicator()),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey[200],
              child: const Icon(Icons.error, size: 20),
            ),
          );

    switch (widget.imageShape) {
      case 'circle':
        return Container(
          width: widget.imageSize,
          height: widget.imageSize,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(
              color: _isDraggingImage
                  ? Colors.blue
                  : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
              width: _isDraggingImage ? 3 : 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingImage ? 100 : 50),
                blurRadius: _isDraggingImage ? 8 : 4,
                spreadRadius: _isDraggingImage ? 1 : 0,
              ),
            ],
          ),
          child: ClipOval(child: imageWidget),
        );

      case 'rectangle':
        return Container(
          width: widget.imageSize,
          height: widget.imageSize,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: _isDraggingImage
                  ? Colors.blue
                  : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
              width: _isDraggingImage ? 3 : 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingImage ? 100 : 50),
                blurRadius: _isDraggingImage ? 8 : 4,
                spreadRadius: _isDraggingImage ? 1 : 0,
              ),
            ],
          ),
          child: ClipRect(child: imageWidget),
        );

      case 'rounded_rectangle':
        return Container(
          width: widget.imageSize,
          height: widget.imageSize,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _isDraggingImage
                  ? Colors.blue
                  : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
              width: _isDraggingImage ? 3 : 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingImage ? 100 : 50),
                blurRadius: _isDraggingImage ? 8 : 4,
                spreadRadius: _isDraggingImage ? 1 : 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: imageWidget,
          ),
        );

      case 'diamond':
        return Container(
          width: widget.imageSize,
          height: widget.imageSize,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingImage ? 100 : 50),
                blurRadius: _isDraggingImage ? 8 : 4,
                spreadRadius: _isDraggingImage ? 1 : 0,
              ),
            ],
          ),
          child: Transform.rotate(
            angle: 0.785398, // 45 degrees in radians
            child: Container(
              width: widget.imageSize * 0.7,
              height: widget.imageSize * 0.7,
              decoration: BoxDecoration(
                border: Border.all(
                  color: _isDraggingImage
                      ? Colors.blue
                      : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
                  width: _isDraggingImage ? 3 : 2,
                ),
              ),
              child: ClipRect(child: imageWidget),
            ),
          ),
        );

      case 'hexagon':
        return Container(
          width: widget.imageSize,
          height: widget.imageSize,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingImage ? 100 : 50),
                blurRadius: _isDraggingImage ? 8 : 4,
                spreadRadius: _isDraggingImage ? 1 : 0,
              ),
            ],
          ),
          child: ClipPath(
            clipper: HexagonClipper(),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: _isDraggingImage
                      ? Colors.blue
                      : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
                  width: _isDraggingImage ? 3 : 2,
                ),
              ),
              child: imageWidget,
            ),
          ),
        );

      case 'none':
      default:
        return Container(
          width: widget.imageSize,
          height: widget.imageSize,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: _isDraggingImage
                  ? Colors.blue
                  : Colors.white.withAlpha(100),
              width: _isDraggingImage ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingImage ? 100 : 50),
                blurRadius: _isDraggingImage ? 6 : 3,
                spreadRadius: _isDraggingImage ? 1 : 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: imageWidget,
          ),
        );
    }
  }
  
  // Getter for the current dragging state
  bool get isDragging => _isDraggingImage;

  // Show a dialog to select the additional image size
  void _showSizeSelectionDialog(BuildContext context) {
    // Only show the dialog if we're not currently dragging
    if (_isDraggingImage) return;

    // Store the initial scale in case user cancels
    final initialScale = _imageScale;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Adjust Image Size'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Select a size for your image:'),
                  const SizedBox(height: 20),
                  Slider(
                    value: _imageScale,
                    min: 0.3,
                    max: 2.5,
                    divisions: 22,
                    label: '${(_imageScale * 100).round()}%',
                    onChanged: (double value) {
                      // Update both the dialog state and the parent widget state
                      setDialogState(() {
                        setState(() {
                          _imageScale = value;
                          widget.onScaleChanged(_imageScale);
                        });
                      });
                    },
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: const [
                      Text('Small', style: TextStyle(fontSize: 12)),
                      Text('Normal', style: TextStyle(fontSize: 12)),
                      Text('Large', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    // Restore the original scale if user cancels
                    setState(() {
                      _imageScale = initialScale;
                      widget.onScaleChanged(_imageScale);
                    });
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Keep the current scale and close the dialog
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('Done'),
                ),
              ],
            );
          }
        );
      },
    );
  }
} 