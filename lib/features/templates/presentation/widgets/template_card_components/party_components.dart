import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../user/domain/entities/party.dart';

/// A class that provides widgets for displaying political party information
class PartyComponents {
  /// Builds a party logo widget for the top right corner
  static Widget? buildPartyLogo(String? logoUrl) {
    if (logoUrl == null || logoUrl.isEmpty) {
      return null;
    }

    return Container(
      width: 70, // Increased size
      height: 70, // Increased size
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(100), // Darker shadow
            blurRadius: 8,
            spreadRadius: 2, // Increased spread
          ),
        ],
      ),
      child: ClipOval(
        child: CachedNetworkImage(
          imageUrl: logoUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => const Center(
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          errorWidget: (context, url, error) {
            debugPrint('PartyComponents: Error loading party logo: $error');
            return const Icon(
              Icons.flag,
              size: 32, // Larger icon
              color: Colors.grey,
            );
          },
        ),
      ),
    );
  }

  /// Builds leadership images for the left side in a row
  static Widget? buildLeadershipImages(List<PartyLeader> leadership) {
    if (leadership.isEmpty) {
      return null;
    }

    // Take up to 3 leadership images to display
    final displayLeaders = leadership.take(3).toList();

    return SizedBox(
      height: 60, // Fixed height for the row
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: displayLeaders.map((leader) {
          return Container(
            width: 50, // Slightly smaller to fit in a row
            height: 50,
            margin: const EdgeInsets.only(right: 10), // Space between images
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(100),
                  blurRadius: 6,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: ClipOval(
              child: CachedNetworkImage(
                imageUrl: leader.image,
                fit: BoxFit.cover,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                errorWidget: (context, url, error) {
                  debugPrint('PartyComponents: Error loading leader image: $error');
                  return const Icon(
                    Icons.person,
                    size: 30,
                    color: Colors.grey,
                  );
                },
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Builds a top banner for business name
  static Widget? buildTopBanner(String? displayName, bool isPremium) {
    // If no display name found or it's empty, don't show the banner
    if (displayName == null || displayName.isEmpty) {
      return null;
    }

    // Convert to uppercase
    displayName = displayName.toUpperCase();

    return Container(
      width: double.infinity,
      height: 50, // Increased banner height for larger font
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(180),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16.0), // Add padding for left alignment
      alignment: Alignment.centerLeft, // Left align the content
      child: Text(
        displayName,
        style: TextStyle(
          color: isPremium ? const Color(0xFFD4AF37) : Colors.white, // Use gold color for premium
          fontSize: 20, // Increased font size
          fontWeight: FontWeight.bold,
          letterSpacing: 1.0, // Increased letter spacing for uppercase text
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
