import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:provider/provider.dart';

import '../../../../../core/theme/app_theme.dart';
import '../../../../../core/providers/template_customization_provider.dart';
import '../../../../user/domain/entities/user_model.dart';

/// A stateful widget that displays business user information with customization options
class BusinessUserInfoOverlay extends StatefulWidget {
  final UserModel user;
  final bool isPremium;
  final bool isForSharing;
  
  // Initial customization values
  final String? initialNameDisplayOption;
  final String? initialContactDisplayOption;
  final Color? initialNameTextColor;
  final String? initialNameTextCase;
  final Color? initialContactTextColor;
  final String? initialContactTextCase;
  
  // Callbacks for customization changes
  final Function(String)? onNameDisplayOptionChanged;
  final Function(String)? onContactDisplayOptionChanged;
  final Function(Color)? onNameTextColorChanged;
  final Function(String)? onNameTextCaseChanged;
  final Function(Color)? onContactTextColorChanged;
  final Function(String)? onContactTextCaseChanged;

  const BusinessUserInfoOverlay({
    Key? key,
    required this.user,
    required this.isPremium,
    this.isForSharing = false, // Default to false for normal display
    this.initialNameDisplayOption,
    this.initialContactDisplayOption,
    this.initialNameTextColor,
    this.initialNameTextCase,
    this.initialContactTextColor,
    this.initialContactTextCase,
    this.onNameDisplayOptionChanged,
    this.onContactDisplayOptionChanged,
    this.onNameTextColorChanged,
    this.onNameTextCaseChanged,
    this.onContactTextColorChanged,
    this.onContactTextCaseChanged,
  }) : super(key: key);

  @override
  State<BusinessUserInfoOverlay> createState() => _BusinessUserInfoOverlayState();
}

class _BusinessUserInfoOverlayState extends State<BusinessUserInfoOverlay> {
  @override
  void initState() {
    super.initState();
    // Initialize with default values from provider if needed
    final customization = Provider.of<TemplateCustomizationProvider>(context, listen: false);
    final defaultColor = widget.isPremium ? AppTheme.premiumGold : Colors.white;
    
    // Only set default colors if they haven't been set yet
    if (customization.nameTextColor == Colors.white && widget.isPremium) {
      customization.updateNameTextColor(defaultColor);
    }
    if (customization.contactTextColor == Colors.white && widget.isPremium) {
      customization.updateContactTextColor(defaultColor);
      }
  }

  // Get the display name based on selected option
  String _getDisplayName(TemplateCustomizationProvider customization) {
    String displayName;

    if (customization.nameDisplayOption == 'business_name') {
      // First check direct businessName field
      if (widget.user.businessProfile?.businessName != null &&
          widget.user.businessProfile!.businessName!.isNotEmpty) {
        displayName = widget.user.businessProfile!.businessName!;
      }
      // Then check parameter values
      else if (widget.user.businessProfile?.parameterValues != null) {
        final businessNameParam = widget.user.businessProfile!.parameterValues!['business_name'] ??
                               widget.user.businessProfile!.parameterValues!['company_name'] ??
                               widget.user.businessProfile!.parameterValues!['organization_name'];

        if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
          displayName = businessNameParam;
        } else {
          // Default to user's name if no business name found
          displayName = widget.user.name;
        }
      } else {
        // Default to user's name if no business profile or parameters
        displayName = widget.user.name;
      }
    } else {
      // Default to user's name
      displayName = widget.user.name;
    }

    // Apply text case transformation
    return _applyNameTextCase(displayName, customization.nameTextCase);
  }

  // Apply text case transformation to name
  String _applyNameTextCase(String text, String textCase) {
    switch (textCase) {
      case 'uppercase':
        return text.toUpperCase();
      case 'lowercase':
        return text.toLowerCase();
      case 'capitalize':
        return _capitalizeFirstLetter(text);
      default:
        return text; // 'normal' case
    }
  }

  // Apply text case transformation to contact info
  String _applyContactTextCase(String text, String textCase) {
    switch (textCase) {
      case 'uppercase':
        return text.toUpperCase();
      case 'lowercase':
        return text.toLowerCase();
      case 'capitalize':
        return _capitalizeFirstLetter(text);
      default:
        return text; // 'normal' case
    }
  }

  // Capitalize the first letter of each word
  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;
    return text.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  // Get the contact info based on selected option
  String? _getContactInfo(TemplateCustomizationProvider customization) {
    String? contactInfo;

    switch (customization.contactDisplayOption) {
      case 'business_phone':
        contactInfo = _getBusinessPhone();
        break;
      case 'personal_phone':
        contactInfo = widget.user.phoneNumber;
        break;
      case 'email':
        contactInfo = widget.user.email;
        break;
      case 'address':
        contactInfo = _getBusinessAddress();
        break;
      default:
        contactInfo = _getBusinessPhone();
    }

    // Apply text case transformation if contact info is not null
    return contactInfo != null ? _applyContactTextCase(contactInfo, customization.contactTextCase) : null;
  }

  // Helper method to get business phone from any source
  String? _getBusinessPhone() {
    // First check direct field
    if (widget.user.businessProfile?.mobileNumber != null &&
        widget.user.businessProfile!.mobileNumber!.isNotEmpty) {
      return widget.user.businessProfile!.mobileNumber;
    }

    // Then check parameter values
    if (widget.user.businessProfile?.parameterValues != null) {
      final phoneParam = widget.user.businessProfile!.parameterValues!['mobile_number'] ??
                       widget.user.businessProfile!.parameterValues!['phone'] ??
                       widget.user.businessProfile!.parameterValues!['contact'] ??
                       widget.user.businessProfile!.parameterValues!['business_phone'];

      if (phoneParam != null && phoneParam is String && phoneParam.isNotEmpty) {
        return phoneParam;
      }
    }

    return widget.user.businessProfile?.mobileNumber;
  }

  // Helper method to get business address from any source
  String? _getBusinessAddress() {
    // First check direct field
    if (widget.user.businessProfile?.address != null &&
        widget.user.businessProfile!.address!.isNotEmpty) {
      return widget.user.businessProfile!.address;
    }

    // Then check parameter values
    if (widget.user.businessProfile?.parameterValues != null) {
      final addressParam = widget.user.businessProfile!.parameterValues!['address'] ??
                         widget.user.businessProfile!.parameterValues!['business_address'] ??
                         widget.user.businessProfile!.parameterValues!['location'];

      if (addressParam != null && addressParam is String && addressParam.isNotEmpty) {
        return addressParam;
      }
    }

    return widget.user.businessProfile?.address;
  }

  // Get icon for the contact info
  IconData _getContactIcon(String contactDisplayOption) {
    switch (contactDisplayOption) {
      case 'business_phone':
        return Icons.business;
      case 'personal_phone':
        return Icons.phone;
      case 'email':
        return Icons.email;
      case 'address':
        return Icons.location_on;
      default:
        return Icons.phone;
    }
  }

  // Show bottom sheet for name options
  void _showNameOptionsBottomSheet() {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return Consumer<TemplateCustomizationProvider>(
          builder: (context, customization, child) {
        return SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Choose what to display',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                        ListTile(
                          title: const Text('Your Name'),
                          subtitle: Text(widget.user.name),
                          leading: const Icon(Icons.person),
                      selected: customization.nameDisplayOption == 'name',
                          onTap: () {
                        customization.updateNameDisplayOption('name');
                        Navigator.pop(context);
                          },
                        ),
                        if (_hasBusinessName())
                          ListTile(
                            title: const Text('Business Name'),
                            subtitle: Text(_getBusinessNameForDisplay()),
                            leading: const Icon(Icons.business),
                        selected: customization.nameDisplayOption == 'business_name',
                            onTap: () {
                          customization.updateNameDisplayOption('business_name');
                          Navigator.pop(context);
                  },
                ),
                const Divider(),
                ListTile(
                  title: const Text('Name Color'),
                  leading: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                          color: customization.nameTextColor,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey),
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showNameColorPicker();
                  },
                ),
                ListTile(
                  title: const Text('Name Text Case'),
                      subtitle: Text(_getNameTextCaseDescription(customization.nameTextCase)),
                  leading: const Icon(Icons.text_format),
                  onTap: () {
                    Navigator.pop(context);
                    _showNameTextCaseOptionsBottomSheet();
                  },
                ),
              ],
            ),
          ),
            );
          },
        );
      },
    );
  }

  // Show color picker dialog for name
  void _showNameColorPicker() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Consumer<TemplateCustomizationProvider>(
          builder: (context, customization, child) {
        return AlertDialog(
          title: const Text('Pick a color for name'),
          content: SingleChildScrollView(
            child: ColorPicker(
                  pickerColor: customization.nameTextColor,
              onColorChanged: (Color color) {
                    customization.updateNameTextColor(color);
              },
              pickerAreaHeightPercent: 0.8,
              displayThumbColor: true,
              labelTypes: const [ColorLabelType.rgb, ColorLabelType.hsv, ColorLabelType.hex],
              paletteType: PaletteType.hsv,
              pickerAreaBorderRadius: const BorderRadius.all(Radius.circular(10)),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Done'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
      },
    );
  }

  // Show text case options bottom sheet for name
  void _showNameTextCaseOptionsBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Consumer<TemplateCustomizationProvider>(
          builder: (context, customization, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Choose text case for name',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                title: const Text('Normal'),
                subtitle: const Text('Text as entered'),
                leading: const Icon(Icons.text_format),
                    selected: customization.nameTextCase == 'normal',
                onTap: () {
                      customization.updateNameTextCase('normal');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('UPPERCASE'),
                subtitle: const Text('ALL CAPITAL LETTERS'),
                leading: const Icon(Icons.text_fields),
                    selected: customization.nameTextCase == 'uppercase',
                onTap: () {
                      customization.updateNameTextCase('uppercase');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('lowercase'),
                subtitle: const Text('all small letters'),
                leading: const Icon(Icons.text_fields),
                    selected: customization.nameTextCase == 'lowercase',
                onTap: () {
                      customization.updateNameTextCase('lowercase');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('Capitalize Each Word'),
                subtitle: const Text('First Letter Uppercase'),
                leading: const Icon(Icons.text_fields),
                    selected: customization.nameTextCase == 'capitalize',
                onTap: () {
                      customization.updateNameTextCase('capitalize');
                  Navigator.pop(context);
                },
              ),
            ],
          ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TemplateCustomizationProvider>(
      builder: (context, customization, child) {
        final String? contactInfo = _getContactInfo(customization);

        return Stack(
          children: [
            // Background overlay
            Container(
              height: 80,
              width: double.infinity,
              color: Colors.black.withAlpha(128),
            ),

            // Left side: Name and position
            Positioned(
              left: 16.0,
              top: 0,
              bottom: 0,
              child: Row(
                children: [
                  // User info (name and position)
                  SizedBox(
                    width: 200,
          child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                        // Name (tappable only when not for sharing)
                        GestureDetector(
                          onTap: widget.isForSharing ? null : _showNameOptionsBottomSheet,
                          child: Row(
                            children: [
                              Flexible(
                                child: Text(
                                  _getDisplayName(customization),
                style: TextStyle(
                                    color: customization.nameTextColor,
                                    fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (!widget.isForSharing) ...[
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.edit,
                                  size: 14,
                                  color: widget.isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70,
                                ),
                              ],
                            ],
              ),
                        ),

                        // Contact info (tappable only when not for sharing)
                        if (contactInfo != null && contactInfo.isNotEmpty)
                          GestureDetector(
                            onTap: widget.isForSharing ? null : () {
                              _showContactOptionsBottomSheet(customization);
                },
                            child: Row(
                              children: [
                                Icon(
                                  _getContactIcon(customization.contactDisplayOption),
                                  color: widget.isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70,
                                  size: 12,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    contactInfo,
                                    style: TextStyle(
                                      color: customization.contactTextColor.withAlpha(204),
                                      fontSize: 15,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (!widget.isForSharing) ...[
                                  const SizedBox(width: 4),
                                  Icon(
                                    Icons.edit,
                                    size: 12,
                                    color: widget.isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70,
                                  ),
                                ],
                              ],
                            ),
                          ),
                      ],
                    ),
              ),
            ],
          ),
            ),
          ],
        );
      },
    );
  }

  // Helper methods for business name
  bool _hasBusinessName() {
    if (widget.user.businessProfile?.businessName != null &&
        widget.user.businessProfile!.businessName!.isNotEmpty) {
      return true;
    }

    if (widget.user.businessProfile?.parameterValues != null) {
      final businessNameParam = widget.user.businessProfile!.parameterValues!['business_name'] ??
                             widget.user.businessProfile!.parameterValues!['company_name'] ??
                             widget.user.businessProfile!.parameterValues!['organization_name'];

      if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
        return true;
      }
    }

    return false;
  }

  String _getBusinessNameForDisplay() {
    if (widget.user.businessProfile?.businessName != null &&
        widget.user.businessProfile!.businessName!.isNotEmpty) {
      return widget.user.businessProfile!.businessName!;
    }

    if (widget.user.businessProfile?.parameterValues != null) {
      final businessNameParam = widget.user.businessProfile!.parameterValues!['business_name'] ??
                             widget.user.businessProfile!.parameterValues!['company_name'] ??
                             widget.user.businessProfile!.parameterValues!['organization_name'];

      if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
        return businessNameParam;
      }
    }

    return "Business Name";
  }

  String _getNameTextCaseDescription(String textCase) {
    switch (textCase) {
      case 'uppercase':
        return 'UPPERCASE';
      case 'lowercase':
        return 'lowercase';
      case 'capitalize':
        return 'Capitalize Each Word';
      default:
        return 'Normal';
    }
  }

  // Show bottom sheet for contact options
  void _showContactOptionsBottomSheet(TemplateCustomizationProvider customization) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Choose what to display',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                        ListTile(
                          title: const Text('Business Phone'),
                          subtitle: Text(_getBusinessPhone() ?? 'Not available'),
                          leading: const Icon(Icons.business),
                  selected: customization.contactDisplayOption == 'business_phone',
                          onTap: () {
                    customization.updateContactDisplayOption('business_phone');
                    Navigator.pop(context);
                          },
                        ),
                        ListTile(
                          title: const Text('Personal Phone'),
                          subtitle: Text(widget.user.phoneNumber ?? 'Not available'),
                          leading: const Icon(Icons.phone),
                  selected: customization.contactDisplayOption == 'personal_phone',
                          onTap: () {
                    customization.updateContactDisplayOption('personal_phone');
                    Navigator.pop(context);
                          },
                        ),
                        ListTile(
                          title: const Text('Email'),
                          subtitle: Text(widget.user.email ?? 'Not available'),
                          leading: const Icon(Icons.email),
                  selected: customization.contactDisplayOption == 'email',
                          onTap: () {
                    customization.updateContactDisplayOption('email');
                    Navigator.pop(context);
                          },
                        ),
                        ListTile(
                          title: const Text('Address'),
                          subtitle: Text(_getBusinessAddress() ?? 'Not available'),
                          leading: const Icon(Icons.location_on),
                  selected: customization.contactDisplayOption == 'address',
                          onTap: () {
                    customization.updateContactDisplayOption('address');
                    Navigator.pop(context);
                  },
                ),
                const Divider(),
                ListTile(
                  title: const Text('Contact Info Color'),
                  leading: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: customization.contactTextColor,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey),
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showContactColorPicker();
                  },
                ),
                ListTile(
                  title: const Text('Contact Info Text Case'),
                  subtitle: Text(_getNameTextCaseDescription(customization.contactTextCase)),
                  leading: const Icon(Icons.text_format),
                  onTap: () {
                    Navigator.pop(context);
                    _showContactTextCaseOptionsBottomSheet();
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Show color picker dialog for contact info
  void _showContactColorPicker() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Consumer<TemplateCustomizationProvider>(
          builder: (context, customization, child) {
            return AlertDialog(
              title: const Text('Pick a color for contact info'),
              content: SingleChildScrollView(
                child: ColorPicker(
                  pickerColor: customization.contactTextColor,
                  onColorChanged: (Color color) {
                    customization.updateContactTextColor(color);
                      },
                  pickerAreaHeightPercent: 0.8,
                  displayThumbColor: true,
                  labelTypes: const [ColorLabelType.rgb, ColorLabelType.hsv, ColorLabelType.hex],
                  paletteType: PaletteType.hsv,
                  pickerAreaBorderRadius: const BorderRadius.all(Radius.circular(10)),
                      ),
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text('Done'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Show text case options bottom sheet for contact info
  void _showContactTextCaseOptionsBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Consumer<TemplateCustomizationProvider>(
          builder: (context, customization, child) {
            return Container(
              padding: const EdgeInsets.all(16),
                child: Column(
                mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                  const Text(
                    'Choose text case for contact info',
                              style: TextStyle(
                      fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: const Text('Normal'),
                    subtitle: const Text('Text as entered'),
                    leading: const Icon(Icons.text_format),
                    selected: customization.contactTextCase == 'normal',
                    onTap: () {
                      customization.updateContactTextCase('normal');
                      Navigator.pop(context);
                    },
                        ),
                  ListTile(
                    title: const Text('UPPERCASE'),
                    subtitle: const Text('ALL CAPITAL LETTERS'),
                    leading: const Icon(Icons.text_fields),
                    selected: customization.contactTextCase == 'uppercase',
                    onTap: () {
                      customization.updateContactTextCase('uppercase');
                      Navigator.pop(context);
                    },
                  ),
                  ListTile(
                    title: const Text('lowercase'),
                    subtitle: const Text('all small letters'),
                    leading: const Icon(Icons.text_fields),
                    selected: customization.contactTextCase == 'lowercase',
                    onTap: () {
                      customization.updateContactTextCase('lowercase');
                      Navigator.pop(context);
                    },
                  ),
                  ListTile(
                    title: const Text('Capitalize Each Word'),
                    subtitle: const Text('First Letter Uppercase'),
                    leading: const Icon(Icons.text_fields),
                    selected: customization.contactTextCase == 'capitalize',
                    onTap: () {
                      customization.updateContactTextCase('capitalize');
                      Navigator.pop(context);
                    },
                      ),
                  ],
                ),
            );
          },
        );
      },
    );
  }
}
