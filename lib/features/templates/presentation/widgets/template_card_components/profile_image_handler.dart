import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../../core/theme/app_theme.dart';

// Custom clipper for hexagon shape
class HexagonClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;

    path.moveTo(width * 0.25, 0);
    path.lineTo(width * 0.75, 0);
    path.lineTo(width, height * 0.5);
    path.lineTo(width * 0.75, height);
    path.lineTo(width * 0.25, height);
    path.lineTo(0, height * 0.5);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

/// A widget that handles the profile image display, dragging and scaling
class ProfileImageHandler extends StatefulWidget {
  /// The URL of the profile image
  final String photoUrl;
  
  /// Whether the user has premium status
  final bool isPremium;
  
  /// Initial offset for the profile image
  final Offset initialOffset;
  
  /// Initial scale for the profile image
  final double initialScale;
  
  /// The shape of the profile image
  final String imageShape;
  
  /// Callback when the scale changes
  final Function(double) onScaleChanged;
  
  /// Callback when the offset changes
  final Function(Offset) onOffsetChanged;

  const ProfileImageHandler({
    Key? key,
    required this.photoUrl,
    required this.isPremium,
    this.initialOffset = Offset.zero,
    this.initialScale = 1.0,
    this.imageShape = 'circle',
    required this.onScaleChanged,
    required this.onOffsetChanged,
  }) : super(key: key);

  @override
  State<ProfileImageHandler> createState() => _ProfileImageHandlerState();
}

class _ProfileImageHandlerState extends State<ProfileImageHandler> {
  late Offset _profileImageOffset;
  late double _profileImageScale;
  bool _isDraggingProfileImage = false;
  double? _initialScale;

  @override
  void initState() {
    super.initState();
    _profileImageOffset = widget.initialOffset;
    _profileImageScale = widget.initialScale;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Use GestureDetector with direct scale handling for immediate scaling and dragging
      // This keeps the image visible during the entire operation
      // Set behavior to translucent to ensure this GestureDetector wins the gesture arena
      behavior: HitTestBehavior.translucent,

      // Handle tap to show size selection dialog
      onTap: () {
        _showSizeSelectionDialog(context);
      },

      // Handle scale gestures (pinch to zoom)
      onScaleStart: (ScaleStartDetails details) {
        setState(() {
          _isDraggingProfileImage = true;
          _initialScale = _profileImageScale;
        });
      },
      onScaleUpdate: (ScaleUpdateDetails details) {
        setState(() {
          // Handle position change (dragging)
          if (details.pointerCount == 1) {
            // Single finger drag - update position
            _profileImageOffset += details.focalPointDelta;
            widget.onOffsetChanged(_profileImageOffset);
          }

          // Handle scale change (pinch)
          if (details.scale != 1.0 && _initialScale != null) {
            // Calculate new scale with some constraints
            _profileImageScale = (_initialScale! * details.scale).clamp(0.5, 2.0);
            widget.onScaleChanged(_profileImageScale);
          }
        });
      },
      onScaleEnd: (ScaleEndDetails details) {
        setState(() {
          _isDraggingProfileImage = false;
          _initialScale = null;
        });
      },
      child: Stack(
        children: [
          // Profile image container with scaling and shape
          Transform.scale(
            scale: _profileImageScale,
            child: _buildShapedImage(),
          ),

          // Drag indicator icon (always shown to indicate direct draggability)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: _isDraggingProfileImage
                    ? Colors.blue.withAlpha(220)
                    : Colors.blue.withAlpha(180),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.zoom_out_map,
                color: Colors.white,
                size: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the profile image with the selected shape
  Widget _buildShapedImage() {
    final imageWidget = CachedNetworkImage(
      imageUrl: widget.photoUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => const Center(
        child: CircularProgressIndicator(),
      ),
      errorWidget: (context, url, error) => const Icon(
        Icons.person,
        size: 32,
        color: Colors.grey,
      ),
    );

    switch (widget.imageShape) {
      case 'circle':
        return Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(
              color: _isDraggingProfileImage
                  ? Colors.blue
                  : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
              width: _isDraggingProfileImage ? 3 : 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingProfileImage ? 100 : 50),
                blurRadius: _isDraggingProfileImage ? 8 : 4,
                spreadRadius: _isDraggingProfileImage ? 1 : 0,
              ),
            ],
          ),
          child: ClipOval(child: imageWidget),
        );

      case 'rectangle':
        return Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: _isDraggingProfileImage
                  ? Colors.blue
                  : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
              width: _isDraggingProfileImage ? 3 : 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingProfileImage ? 100 : 50),
                blurRadius: _isDraggingProfileImage ? 8 : 4,
                spreadRadius: _isDraggingProfileImage ? 1 : 0,
              ),
            ],
          ),
          child: ClipRect(child: imageWidget),
        );

      case 'rounded_rectangle':
        return Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _isDraggingProfileImage
                  ? Colors.blue
                  : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
              width: _isDraggingProfileImage ? 3 : 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingProfileImage ? 100 : 50),
                blurRadius: _isDraggingProfileImage ? 8 : 4,
                spreadRadius: _isDraggingProfileImage ? 1 : 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: imageWidget,
          ),
        );

      case 'diamond':
        return Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingProfileImage ? 100 : 50),
                blurRadius: _isDraggingProfileImage ? 8 : 4,
                spreadRadius: _isDraggingProfileImage ? 1 : 0,
              ),
            ],
          ),
          child: Transform.rotate(
            angle: 0.785398, // 45 degrees in radians
            child: Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                border: Border.all(
                  color: _isDraggingProfileImage
                      ? Colors.blue
                      : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
                  width: _isDraggingProfileImage ? 3 : 2,
                ),
              ),
              child: ClipRect(child: imageWidget),
            ),
          ),
        );

      case 'hexagon':
        return Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(_isDraggingProfileImage ? 100 : 50),
                blurRadius: _isDraggingProfileImage ? 8 : 4,
                spreadRadius: _isDraggingProfileImage ? 1 : 0,
              ),
            ],
          ),
          child: ClipPath(
            clipper: HexagonClipper(),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: _isDraggingProfileImage
                      ? Colors.blue
                      : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
                  width: _isDraggingProfileImage ? 3 : 2,
                ),
              ),
              child: imageWidget,
            ),
          ),
        );

      case 'none':
      default:
        return Container(
          width: 64,
          height: 64,
          child: imageWidget,
        );
    }
  }

  // Show a dialog to select the profile image size
  void _showSizeSelectionDialog(BuildContext context) {
    // Only show the dialog if we're not currently dragging
    if (_isDraggingProfileImage) return;

    // Store the initial scale in case user cancels
    final initialScale = _profileImageScale;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Adjust Profile Image Size'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Select a size for your profile image:'),
                  const SizedBox(height: 20),
                  Slider(
                    value: _profileImageScale,
                    min: 0.5,
                    max: 2.0,
                    divisions: 15,
                    label: '${(_profileImageScale * 100).round()}%',
                    onChanged: (double value) {
                      // Update both the dialog state and the parent widget state
                      setDialogState(() {
                        setState(() {
                          _profileImageScale = value;
                          widget.onScaleChanged(_profileImageScale);
                        });
                      });
                    },
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: const [
                      Text('Small', style: TextStyle(fontSize: 12)),
                      Text('Normal', style: TextStyle(fontSize: 12)),
                      Text('Large', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    // Restore the original scale if user cancels
                    setState(() {
                      _profileImageScale = initialScale;
                      widget.onScaleChanged(_profileImageScale);
                    });
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Keep the current scale and close the dialog
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('Done'),
                ),
              ],
            );
          }
        );
      },
    );
  }
  
  // Getter for the current dragging state
  bool get isDragging => _isDraggingProfileImage;
}
