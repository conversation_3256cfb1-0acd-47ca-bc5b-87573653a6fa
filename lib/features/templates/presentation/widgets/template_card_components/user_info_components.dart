import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../../core/theme/app_theme.dart';
import '../../../../user/domain/entities/user_model.dart';
import 'business_user_info_overlay.dart';

/// A class that provides widgets for displaying user information
class UserInfoComponents {
  /// Builds the user information overlay for the banner
  static Widget buildUserInfoOverlay(
    UserModel? user, 
    bool isPremium, {
    bool isForSharing = false,
    // Banner text customization parameters
    String? bannerNameDisplayOption,
    String? bannerContactDisplayOption,
    Color? bannerNameTextColor,
    String? bannerNameTextCase,
    Color? bannerContactTextColor,
    String? bannerContactTextCase,
    // Callbacks for banner text changes
    Function(String)? onBannerNameDisplayOptionChanged,
    Function(String)? onBannerContactDisplayOptionChanged,
    Function(Color)? onBannerNameTextColorChanged,
    Function(String)? onBannerNameTextCaseChanged,
    Function(Color)? onBannerContactTextColorChanged,
    Function(String)? onBannerContactTextCaseChanged,
  }) {
    if (user == null) {
      return const SizedBox.shrink();
    }

    // For business users, use the interactive version
    if (user.userType == 'businessman' && user.businessProfile != null) {
      return BusinessUserInfoOverlay(
        user: user,
        isPremium: isPremium,
        isForSharing: isForSharing,
        // Pass banner text customizations
        initialNameDisplayOption: bannerNameDisplayOption,
        initialContactDisplayOption: bannerContactDisplayOption,
        initialNameTextColor: bannerNameTextColor,
        initialNameTextCase: bannerNameTextCase,
        initialContactTextColor: bannerContactTextColor,
        initialContactTextCase: bannerContactTextCase,
        // Pass callbacks
        onNameDisplayOptionChanged: onBannerNameDisplayOptionChanged,
        onContactDisplayOptionChanged: onBannerContactDisplayOptionChanged,
        onNameTextColorChanged: onBannerNameTextColorChanged,
        onNameTextCaseChanged: onBannerNameTextCaseChanged,
        onContactTextColorChanged: onBannerContactTextColorChanged,
        onContactTextCaseChanged: onBannerContactTextCaseChanged,
      );
    }

    // For other users, use the static version
    return _buildStaticUserInfoOverlay(user, isPremium);
  }

  /// Builds a static (non-interactive) user information overlay
  static Widget _buildStaticUserInfoOverlay(UserModel user, bool isPremium) {
    // Get user information based on user type
    String name = user.name;
    String? position;
    String? phoneNumber;
    String? photoUrl = user.photoUrl;
    bool isRegularUser = user.userType == null ||
                       (user.userType != 'businessman' &&
                        user.userType != 'politician');

    if (user.userType == 'businessman' && user.businessProfile != null) {
      // Business user
      position = user.businessProfile!.businessType;

      // Get phone number from main field
      phoneNumber = user.businessProfile!.mobileNumber;

      // If mobile number is not available in the main field, try to get it from parameter values
      if ((phoneNumber == null || phoneNumber.isEmpty) &&
          user.businessProfile!.parameterValues != null) {
        // Look for mobile number in parameter values
        final mobileParam = user.businessProfile!.parameterValues!['mobile_number'] ??
                         user.businessProfile!.parameterValues!['phone'] ??
                         user.businessProfile!.parameterValues!['contact'] ??
                         user.businessProfile!.parameterValues!['business_phone'];

        if (mobileParam != null && mobileParam is String && mobileParam.isNotEmpty) {
          phoneNumber = mobileParam;
        }
      }
    } else if (user.userType == 'politician' && user.politicalProfile != null) {
      // Political user
      final paramValues = user.politicalProfile!.parameterValues;
      if (paramValues != null) {
        position = paramValues['position'] as String?;
      }
    }

    return Stack(
      children: [
        // Background overlay
        Container(
          height: 80,
          width: double.infinity,
          color: Colors.black.withAlpha(128),
        ),

        // Left side: Name and position (for business/political users)
        // Or name and profile photo (for regular users)
        Positioned(
          left: 16.0,
          top: 0,
          bottom: 0,
          child: Row(
            children: [
              // For regular users, show circular profile photo
              if (isRegularUser && photoUrl != null && photoUrl.isNotEmpty)
                Container(
                  width: 60,
                  height: 60,
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isPremium ? AppTheme.premiumGold : Colors.white,
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(50),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: photoUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      errorWidget: (context, url, error) => const Icon(
                        Icons.person,
                        size: 32,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),

              // User info (name and position)
              SizedBox(
                width: isRegularUser ? 120 : 200, // Adjust width based on user type
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name
                    Text(
                      name,
                      style: TextStyle(
                        color: isPremium ? AppTheme.premiumGold : Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Position (only for business/political users)
                    if (!isRegularUser && position != null)
                      Text(
                        position,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                    // Phone number (only for business users)
                    if (user.userType == 'businessman' && phoneNumber != null && phoneNumber.isNotEmpty)
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            color: isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70, // 0.8 * 255 = 204
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              phoneNumber,
                              style: TextStyle(
                                color: isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70, // 0.8 * 255 = 204
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),

                    // Fallback phone number for testing
                    if (user.userType == 'businessman' && (phoneNumber == null || phoneNumber.isEmpty))
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            color: isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70, // 0.8 * 255 = 204
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              "************", // Hardcoded for testing
                              style: TextStyle(
                                color: isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70, // 0.8 * 255 = 204
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Gets the profile photo URL based on user type
  static String? getProfilePhotoUrl(UserModel? user) {
    if (user == null) {
      return null;
    }

    if (user.userType == 'businessman' && user.businessProfile != null) {
      // Business user
      // First check if logoUrl is available
      if (user.businessProfile!.logoUrl != null && user.businessProfile!.logoUrl!.isNotEmpty) {
        return user.businessProfile!.logoUrl;
      }

      // If not, check if there's a logo in the parameter values
      if (user.businessProfile!.parameterValues != null) {
        // Look for any parameter that might contain a logo URL
        for (var entry in user.businessProfile!.parameterValues!.entries) {
          if (entry.key.toLowerCase().contains('logo') &&
              entry.value is String &&
              (entry.value as String).isNotEmpty) {
            return entry.value as String;
          }
        }
      }

      // If still no logo, use the user's photo as fallback
      if (user.photoUrl != null && user.photoUrl!.isNotEmpty) {
        return user.photoUrl;
      }
    } else if (user.userType == 'politician' && user.politicalProfile != null) {
      // Political user
      final paramValues = user.politicalProfile!.parameterValues;
      if (paramValues != null) {
        final photoUrl = paramValues['political_photo'] as String?;
        if (photoUrl != null && photoUrl.isNotEmpty) {
          return photoUrl;
        }
      }

      // If no political photo, use the user's photo as fallback
      if (user.photoUrl != null && user.photoUrl!.isNotEmpty) {
        return user.photoUrl;
      }
    } else if (user.photoUrl != null && user.photoUrl!.isNotEmpty) {
      // Regular user - use their photo
      return user.photoUrl;
    }

    return null;
  }
}
