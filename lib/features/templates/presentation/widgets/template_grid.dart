import 'package:flutter/material.dart';
import '../../../../features/banners/domain/entities/banner_item.dart';
import '../../domain/entities/template_item.dart';
import 'template_item_card.dart';

class TemplateGrid extends StatelessWidget {
  final List<TemplateItem> templates;
  final Function(TemplateItem) onTemplateTap;
  final Function(BannerItem)? onBannerTap;
  final VoidCallback? onLoadMore;
  final bool hasMoreTemplates;
  final bool isLoadingMore;
  final double itemWidth;
  final int crossAxisCount;
  final bool isLoading;
  final BannerItem? banner;

  const TemplateGrid({
    super.key,
    required this.templates,
    required this.onTemplateTap,
    this.onBannerTap,
    this.onLoadMore,
    this.hasMoreTemplates = false,
    this.isLoadingMore = false,
    this.itemWidth = 120,
    this.crossAxisCount = 2,
    this.isLoading = false,
    this.banner,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingGrid();
    }

    if (templates.isEmpty) {
      return const Center(
        child: Text('No templates available'),
      );
    }

    // Use ListView instead of GridView to show one item per row
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
      // Add +1 to itemCount if we have more templates to load (for the loading indicator or load more button)
      itemCount: templates.length + (hasMoreTemplates ? 1 : 0),
      itemBuilder: (context, index) {
        // If we've reached the end and there are more templates to load
        if (index == templates.length) {
          // Auto-trigger loading more templates when we reach the end
          if (onLoadMore != null && !isLoadingMore) {
            // Use Future.microtask to avoid calling setState during build
            Future.microtask(() => onLoadMore!());
          }

          // Show loading indicator or load more button
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
            alignment: Alignment.center,
            child: isLoadingMore
                ? const CircularProgressIndicator()
                : ElevatedButton(
                    onPressed: onLoadMore,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: const Text('Load More Templates'),
                  ),
          );
        }

        final template = templates[index];
        // Calculate the width based on screen size
        final screenWidth = MediaQuery.of(context).size.width;
        final cardWidth = screenWidth - 16.0; // Account for horizontal padding (8.0 * 2)
        // Note: The actual visible width will be cardWidth - 8.0 (for the card's horizontal margin)

        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: TemplateItemCard(
            template: template,
            onTap: () => onTemplateTap(template),
            width: cardWidth,
            // Use 3:4 aspect ratio (width:height) to make templates taller
            aspectRatio: 3/4,
            banner: banner,
            onBannerTap: onBannerTap,
          ),
        );
      },
    );
  }

  Widget _buildLoadingGrid() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
      itemCount: 4, // Show 4 placeholder items
      itemBuilder: (context, index) {
        // Calculate the width based on screen size
        final screenWidth = MediaQuery.of(context).size.width;
        final cardWidth = screenWidth - 16.0; // Account for horizontal padding (8.0 * 2)
        // Note: The actual visible width will be cardWidth - 8.0 (for the card's horizontal margin)
        // Calculate height based on 3:4 aspect ratio
        final cardHeight = cardWidth * 4 / 3;

        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Container(
            width: cardWidth,
            height: cardHeight,
            margin: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
        );
      },
    );
  }
}
