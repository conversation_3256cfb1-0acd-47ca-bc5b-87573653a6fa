import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/services/firebase_localization_service.dart';
import 'template_item.dart';

/// Localized version of TemplateItem that supports multiple languages
class LocalizedTemplateItem extends TemplateItem {
  LocalizedTemplateItem._({
    required TemplateItem template,
    required FirebaseLocalizationService localizationService,
    required String userLanguageCode,
    required Map<String, dynamic> rawData,
  })  :
        super(
          id: template.id,
          imageUrl: template.imageUrl,
          name: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'name',
            languageCode: userLanguageCode,
          ),
          description: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'description',
            languageCode: userLanguageCode,
          ),
          category: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'category',
            languageCode: userLanguageCode,
          ),
          subCategory: rawData['subCategory'] != null
              ? localizationService.getLocalizedText(
                  data: rawData,
                  fieldName: 'subCategory',
                  languageCode: userLanguageCode,
                )
              : null,
          isPremium: template.isPremium,
          tags: localizationService.getLocalizedList(
            data: rawData,
            fieldName: 'tags',
            languageCode: userLanguageCode,
          ),
          layoutType: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'layoutType',
            languageCode: userLanguageCode,
          ),
          targetAudience: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'targetAudience',
            languageCode: userLanguageCode,
          ),
          designStyle: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'designStyle',
            languageCode: userLanguageCode,
          ),
          colors: localizationService.getLocalizedList(
            data: rawData,
            fieldName: 'colors',
            languageCode: userLanguageCode,
          ),
          fontStyles: localizationService.getLocalizedList(
            data: rawData,
            fieldName: 'fontStyles',
            languageCode: userLanguageCode,
          ),
          usageType: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'usageType',
            languageCode: userLanguageCode,
          ),
          resolution: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'resolution',
            languageCode: userLanguageCode,
          ),
          licenseType: localizationService.getLocalizedText(
            data: rawData,
            fieldName: 'licenseType',
            languageCode: userLanguageCode,
          ),
          uploadDate: template.uploadDate,
          lastModified: template.lastModified,
          creator: template.creator,
          fileFormats: template.fileFormats,
          downloadCount: template.downloadCount,
          viewCount: template.viewCount,
          rating: template.rating,
          ratingCount: template.ratingCount,
          isActive: template.isActive,
          dimensions: template.dimensions,
          fileSize: template.fileSize,
          uploadedBy: template.uploadedBy,
        );

  /// Create a localized template item from Firestore document
  static LocalizedTemplateItem fromFirestore({
    required DocumentSnapshot doc,
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) {
    final data = doc.data() as Map<String, dynamic>;
    final originalTemplate = TemplateItem.fromFirestore(doc);
    final service = localizationService ?? FirebaseLocalizationService();

    return LocalizedTemplateItem._(
      template: originalTemplate,
      localizationService: service,
      userLanguageCode: userLanguageCode,
      rawData: data,
    );
  }

  /// Get localized category list for filtering
  static Future<List<String>> getLocalizedCategories({
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) async {
    final service = localizationService ?? FirebaseLocalizationService();
    final firestore = FirebaseFirestore.instance;

    try {
      final querySnapshot = await firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      final categories = <String>{};
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final localizedCategory = service.getLocalizedText(
          data: data,
          fieldName: 'category',
          languageCode: userLanguageCode,
        );
        if (localizedCategory.isNotEmpty) {
          categories.add(localizedCategory);
        }
      }

      return categories.toList()..sort();
    } catch (e) {
      return [];
    }
  }

  /// Get localized tags for search
  static Future<List<String>> getLocalizedTags({
    required String userLanguageCode,
    FirebaseLocalizationService? localizationService,
  }) async {
    final service = localizationService ?? FirebaseLocalizationService();
    final firestore = FirebaseFirestore.instance;

    try {
      final querySnapshot = await firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      final tags = <String>{};
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final localizedTags = service.getLocalizedList(
          data: data,
          fieldName: 'tags',
          languageCode: userLanguageCode,
        );
        tags.addAll(localizedTags);
      }

      return tags.toList()..sort();
    } catch (e) {
      return [];
    }
  }
}
