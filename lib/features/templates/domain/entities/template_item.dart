import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class representing a template item with comprehensive metadata
class TemplateItem {
  /// Unique identifier for the template
  final String id;

  /// URL of the template image
  final String imageUrl;

  /// Template name - short, descriptive name
  final String name;

  /// Brief description of what the template is used for
  final String? description;

  /// Main classification (Business, Event, Festival, Party, etc.)
  final String? category;

  /// More specific classification under main category
  final String? subCategory;

  /// Whether the template is premium or free
  final bool isPremium;

  /// Keywords for search and filtering
  final List<String>? tags;

  /// Type of layout (Horizontal, Vertical, Square)
  final String? layoutType;

  /// Intended user (Small Business, Event Organizers, etc.)
  final String? targetAudience;

  /// Design approach (Modern, Minimalist, Retro, Elegant)
  final String? designStyle;

  /// Predominant color scheme
  final List<String>? colors;

  /// Font styles used (Serif, Sans-serif, Handwritten)
  final List<String>? fontStyles;

  /// Usage type (Print, Social Media, Online Advertising)
  final String? usageType;

  /// Quality/resolution (High, Medium, Low)
  final String? resolution;

  /// Usage rights (Free for personal use, Commercial use allowed)
  final String? licenseType;

  /// When the template was uploaded
  final DateTime uploadDate;

  /// Last modified date
  final DateTime? lastModified;

  /// Name of the designer or source
  final String? creator;

  /// File types available (PSD, PNG, JPEG, AI, etc.)
  final List<String>? fileFormats;

  /// Download count for analytics
  final int downloadCount;

  /// View count for analytics
  final int viewCount;

  /// Rating (1-5 stars)
  final double? rating;

  /// Number of ratings
  final int ratingCount;

  /// Whether template is active/published
  final bool isActive;

  /// Template dimensions (width x height)
  final String? dimensions;

  /// File size in bytes
  final int? fileSize;

  /// Admin who uploaded the template
  final String? uploadedBy;

  const TemplateItem({
    required this.id,
    required this.imageUrl,
    required this.name,
    required this.uploadDate,
    this.description,
    this.category,
    this.subCategory,
    this.isPremium = false,
    this.tags,
    this.layoutType,
    this.targetAudience,
    this.designStyle,
    this.colors,
    this.fontStyles,
    this.usageType,
    this.resolution,
    this.licenseType,
    this.lastModified,
    this.creator,
    this.fileFormats,
    this.downloadCount = 0,
    this.viewCount = 0,
    this.rating,
    this.ratingCount = 0,
    this.isActive = true,
    this.dimensions,
    this.fileSize,
    this.uploadedBy,
  });

  /// Create a template item from Firestore document
  factory TemplateItem.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return TemplateItem(
      id: doc.id,
      imageUrl: data['imageUrl'] ?? '',
      name: data['name'] ?? '',
      description: data['description'],
      category: data['category'],
      subCategory: data['subCategory'],
      isPremium: data['isPremium'] ?? false,
      tags: data['tags'] != null ? List<String>.from(data['tags']) : null,
      layoutType: data['layoutType'],
      targetAudience: data['targetAudience'],
      designStyle: data['designStyle'],
      colors: data['colors'] != null ? List<String>.from(data['colors']) : null,
      fontStyles: data['fontStyles'] != null ? List<String>.from(data['fontStyles']) : null,
      usageType: data['usageType'],
      resolution: data['resolution'],
      licenseType: data['licenseType'],
      uploadDate: data['uploadDate'] != null
          ? (data['uploadDate'] as Timestamp).toDate()
          : DateTime.now(),
      lastModified: data['lastModified'] != null
          ? (data['lastModified'] as Timestamp).toDate()
          : null,
      creator: data['creator'],
      fileFormats: data['fileFormats'] != null ? List<String>.from(data['fileFormats']) : null,
      downloadCount: data['downloadCount'] ?? 0,
      viewCount: data['viewCount'] ?? 0,
      rating: data['rating']?.toDouble(),
      ratingCount: data['ratingCount'] ?? 0,
      isActive: data['isActive'] ?? true,
      dimensions: data['dimensions'],
      fileSize: data['fileSize'],
      uploadedBy: data['uploadedBy'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'imageUrl': imageUrl,
      'name': name,
      'description': description,
      'category': category,
      'subCategory': subCategory,
      'isPremium': isPremium,
      'tags': tags,
      'layoutType': layoutType,
      'targetAudience': targetAudience,
      'designStyle': designStyle,
      'colors': colors,
      'fontStyles': fontStyles,
      'usageType': usageType,
      'resolution': resolution,
      'licenseType': licenseType,
      'uploadDate': Timestamp.fromDate(uploadDate),
      'lastModified': lastModified != null ? Timestamp.fromDate(lastModified!) : null,
      'creator': creator,
      'fileFormats': fileFormats,
      'downloadCount': downloadCount,
      'viewCount': viewCount,
      'rating': rating,
      'ratingCount': ratingCount,
      'isActive': isActive,
      'dimensions': dimensions,
      'fileSize': fileSize,
      'uploadedBy': uploadedBy,
    };
  }

  /// Create a template item from a storage URL (for backward compatibility)
  /// This extracts basic properties from the URL
  factory TemplateItem.fromStorageUrl(String url) {
    // Extract file name from URL
    final Uri uri = Uri.parse(url);
    final String path = uri.path;
    final String fileName = path.split('/').last.split('?').first;

    // Remove file extension
    final String nameWithoutExtension = fileName.split('.').first;

    // Generate an ID based on the URL
    final String id = url.hashCode.toString();

    // Check if it's a premium template (just an example logic)
    final bool isPremium = nameWithoutExtension.toLowerCase().contains('premium');

    // Extract category if present in the name (e.g., "holiday_template.jpg" -> "holiday")
    String? category;
    if (nameWithoutExtension.contains('_')) {
      category = nameWithoutExtension.split('_').first;
      // Capitalize first letter
      category = category[0].toUpperCase() + category.substring(1);
    }

    return TemplateItem(
      id: id,
      imageUrl: url,
      name: nameWithoutExtension.replaceAll('_', ' '),
      category: category,
      isPremium: isPremium,
      uploadDate: DateTime.now(),
      fileFormats: [fileName.split('.').last.toUpperCase()],
      resolution: 'High',
      licenseType: 'Free for personal use',
      isActive: true,
    );
  }

  /// Copy with method for updating template
  TemplateItem copyWith({
    String? id,
    String? imageUrl,
    String? name,
    String? description,
    String? category,
    String? subCategory,
    bool? isPremium,
    List<String>? tags,
    String? layoutType,
    String? targetAudience,
    String? designStyle,
    List<String>? colors,
    List<String>? fontStyles,
    String? usageType,
    String? resolution,
    String? licenseType,
    DateTime? uploadDate,
    DateTime? lastModified,
    String? creator,
    List<String>? fileFormats,
    int? downloadCount,
    int? viewCount,
    double? rating,
    int? ratingCount,
    bool? isActive,
    String? dimensions,
    int? fileSize,
    String? uploadedBy,
  }) {
    return TemplateItem(
      id: id ?? this.id,
      imageUrl: imageUrl ?? this.imageUrl,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      subCategory: subCategory ?? this.subCategory,
      isPremium: isPremium ?? this.isPremium,
      tags: tags ?? this.tags,
      layoutType: layoutType ?? this.layoutType,
      targetAudience: targetAudience ?? this.targetAudience,
      designStyle: designStyle ?? this.designStyle,
      colors: colors ?? this.colors,
      fontStyles: fontStyles ?? this.fontStyles,
      usageType: usageType ?? this.usageType,
      resolution: resolution ?? this.resolution,
      licenseType: licenseType ?? this.licenseType,
      uploadDate: uploadDate ?? this.uploadDate,
      lastModified: lastModified ?? this.lastModified,
      creator: creator ?? this.creator,
      fileFormats: fileFormats ?? this.fileFormats,
      downloadCount: downloadCount ?? this.downloadCount,
      viewCount: viewCount ?? this.viewCount,
      rating: rating ?? this.rating,
      ratingCount: ratingCount ?? this.ratingCount,
      isActive: isActive ?? this.isActive,
      dimensions: dimensions ?? this.dimensions,
      fileSize: fileSize ?? this.fileSize,
      uploadedBy: uploadedBy ?? this.uploadedBy,
    );
  }
}
