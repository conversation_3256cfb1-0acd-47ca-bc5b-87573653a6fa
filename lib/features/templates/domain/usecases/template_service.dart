import '../entities/template_item.dart';
import '../entities/localized_template_item.dart';
import '../../data/repositories/template_repository.dart';
import '../../data/repositories/firestore_template_repository.dart';
import '../../data/repositories/localized_template_repository.dart';

class TemplateService {
  final FirestoreTemplateRepository _firestoreRepository;
  final LocalizedTemplateRepository _localizedRepository;

  TemplateService(TemplateRepository templateRepository, this._firestoreRepository)
      : _localizedRepository = LocalizedTemplateRepository();

  /// Reset pagination state
  void resetPagination() {
    _firestoreRepository.resetPagination();
  }

  /// Reset pagination for category change
  void resetPaginationForCategory(String? category) {
    _firestoreRepository.resetPaginationForCategory(category);
  }

  /// Check if more templates are available
  bool get hasMoreTemplates => _firestoreRepository.hasMoreTemplates;

  /// Get current category filter
  String? get currentCategory => _firestoreRepository.currentCategory;

  /// Get templates with pagination and optional category filtering
  Future<List<TemplateItem>> getTemplates({
    int limit = 5,
    String? category,
  }) async {
    return await _firestoreRepository.getTemplates(
      limit: limit,
      category: category,
    );
  }

  /// Get all available categories
  Future<List<String>> getAvailableCategories() async {
    return await _firestoreRepository.getAvailableCategories();
  }

  /// Get template count for each category
  Future<Map<String, int>> getCategoryCounts() async {
    return await _firestoreRepository.getCategoryCounts();
  }

  /// Get total template count
  Future<int> getTotalTemplateCount() async {
    return await _firestoreRepository.getTotalTemplateCount();
  }

  /// Get template count for specific category
  Future<int> getTemplateCountForCategory(String category) async {
    return await _firestoreRepository.getTemplateCountForCategory(category);
  }

  /// Increment view count for a template
  Future<void> incrementViewCount(String templateId) async {
    await _firestoreRepository.incrementViewCount(templateId);
  }

  /// Increment download count for a template
  Future<void> incrementDownloadCount(String templateId) async {
    await _firestoreRepository.incrementDownloadCount(templateId);
  }

  /// Search templates by name or tags
  Future<List<TemplateItem>> searchTemplates({
    required String searchTerm,
    String? category,
    int limit = 20,
  }) async {
    return await _firestoreRepository.searchTemplates(
      searchTerm: searchTerm,
      category: category,
      limit: limit,
    );
  }

  /// Get all available templates (legacy method, use getTemplates with pagination instead)
  Future<List<TemplateItem>> getAllTemplates() async {
    // Reset pagination to ensure we get all templates from the beginning
    resetPagination();

    // Get first batch of templates
    List<TemplateItem> allTemplates = await getTemplates(limit: 100);

    return allTemplates;
  }

  /// Get templates by category (legacy method, use getTemplates with category parameter instead)
  Future<List<TemplateItem>> getTemplatesByCategory(String category) async {
    resetPaginationForCategory(category);
    return await getTemplates(category: category, limit: 100);
  }

  /// Get free templates only
  Future<List<TemplateItem>> getFreeTemplates() async {
    final List<TemplateItem> allTemplates = await getAllTemplates();

    // Filter out premium templates
    return allTemplates.where((template) => !template.isPremium).toList();
  }

  // ========== LOCALIZED METHODS ==========

  /// Reset localized pagination state
  void resetLocalizedPagination() {
    _localizedRepository.resetPagination();
  }

  /// Reset localized pagination for category change
  void resetLocalizedPaginationForCategory(String? category) {
    _localizedRepository.resetPaginationForCategory(category);
  }

  /// Check if more localized templates are available
  bool get hasMoreLocalizedTemplates => _localizedRepository.hasMoreTemplates;

  /// Get current localized category filter
  String? get currentLocalizedCategory => _localizedRepository.currentCategory;

  /// Get localized templates with pagination and optional category filtering
  Future<List<LocalizedTemplateItem>> getLocalizedTemplates({
    required String userLanguageCode,
    int limit = 5,
    String? category,
  }) async {
    return await _localizedRepository.getLocalizedTemplates(
      userLanguageCode: userLanguageCode,
      limit: limit,
      category: category,
    );
  }

  /// Get localized categories
  Future<List<String>> getLocalizedCategories({
    required String userLanguageCode,
  }) async {
    return await _localizedRepository.getLocalizedCategories(
      userLanguageCode: userLanguageCode,
    );
  }

  /// Get localized tags
  Future<List<String>> getLocalizedTags({
    required String userLanguageCode,
  }) async {
    return await _localizedRepository.getLocalizedTags(
      userLanguageCode: userLanguageCode,
    );
  }

  /// Search localized templates
  Future<List<LocalizedTemplateItem>> searchLocalizedTemplates({
    required String searchTerm,
    required String userLanguageCode,
    String? category,
    int limit = 20,
  }) async {
    return await _localizedRepository.searchLocalizedTemplates(
      searchTerm: searchTerm,
      userLanguageCode: userLanguageCode,
      category: category,
      limit: limit,
    );
  }

  /// Get localized category counts
  Future<Map<String, int>> getLocalizedCategoryCounts({
    required String userLanguageCode,
  }) async {
    return await _localizedRepository.getLocalizedCategoryCounts(
      userLanguageCode: userLanguageCode,
    );
  }

  /// Get template count for localized category
  Future<int> getTemplateCountForLocalizedCategory({
    required String category,
    required String userLanguageCode,
  }) async {
    return await _localizedRepository.getTemplateCountForLocalizedCategory(
      category: category,
      userLanguageCode: userLanguageCode,
    );
  }
}
