import '../entities/template_item.dart';
import '../../data/repositories/template_repository.dart';
import '../../data/repositories/firestore_template_repository.dart';

class TemplateService {
  final FirestoreTemplateRepository _firestoreRepository;

  TemplateService(TemplateRepository templateRepository, this._firestoreRepository);

  /// Reset pagination state
  void resetPagination() {
    _firestoreRepository.resetPagination();
  }

  /// Reset pagination for category change
  void resetPaginationForCategory(String? category) {
    _firestoreRepository.resetPaginationForCategory(category);
  }

  /// Check if more templates are available
  bool get hasMoreTemplates => _firestoreRepository.hasMoreTemplates;

  /// Get current category filter
  String? get currentCategory => _firestoreRepository.currentCategory;

  /// Get templates with pagination and optional category filtering
  Future<List<TemplateItem>> getTemplates({
    int limit = 5,
    String? category,
  }) async {
    return await _firestoreRepository.getTemplates(
      limit: limit,
      category: category,
    );
  }

  /// Get all available categories
  Future<List<String>> getAvailableCategories() async {
    return await _firestoreRepository.getAvailableCategories();
  }

  /// Get template count for each category
  Future<Map<String, int>> getCategoryCounts() async {
    return await _firestoreRepository.getCategoryCounts();
  }

  /// Get total template count
  Future<int> getTotalTemplateCount() async {
    return await _firestoreRepository.getTotalTemplateCount();
  }

  /// Get template count for specific category
  Future<int> getTemplateCountForCategory(String category) async {
    return await _firestoreRepository.getTemplateCountForCategory(category);
  }

  /// Increment view count for a template
  Future<void> incrementViewCount(String templateId) async {
    await _firestoreRepository.incrementViewCount(templateId);
  }

  /// Increment download count for a template
  Future<void> incrementDownloadCount(String templateId) async {
    await _firestoreRepository.incrementDownloadCount(templateId);
  }

  /// Search templates by name or tags
  Future<List<TemplateItem>> searchTemplates({
    required String searchTerm,
    String? category,
    int limit = 20,
  }) async {
    return await _firestoreRepository.searchTemplates(
      searchTerm: searchTerm,
      category: category,
      limit: limit,
    );
  }

  /// Get all available templates (legacy method, use getTemplates with pagination instead)
  Future<List<TemplateItem>> getAllTemplates() async {
    // Reset pagination to ensure we get all templates from the beginning
    resetPagination();

    // Get first batch of templates
    List<TemplateItem> allTemplates = await getTemplates(limit: 100);

    return allTemplates;
  }

  /// Get templates by category (legacy method, use getTemplates with category parameter instead)
  Future<List<TemplateItem>> getTemplatesByCategory(String category) async {
    resetPaginationForCategory(category);
    return await getTemplates(category: category, limit: 100);
  }

  /// Get free templates only
  Future<List<TemplateItem>> getFreeTemplates() async {
    final List<TemplateItem> allTemplates = await getAllTemplates();

    // Filter out premium templates
    return allTemplates.where((template) => !template.isPremium).toList();
  }
}
