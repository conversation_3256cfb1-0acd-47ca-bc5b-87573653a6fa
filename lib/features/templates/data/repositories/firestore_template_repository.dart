import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/template_item.dart';

class FirestoreTemplateRepository {
  final FirebaseFirestore _firestore;
  bool _hasMoreTemplates = true; // Flag to indicate if more templates are available
  String? _currentCategory; // Track current category filter

  FirestoreTemplateRepository({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Reset pagination state
  void resetPagination() {
    _currentOffset = 0;
    _hasMoreTemplates = true;
    _currentCategory = null;
    // Don't clear cache, just reset pagination
  }

  /// Reset pagination for category change
  void resetPaginationForCategory(String? category) {
    _currentOffset = 0;
    _hasMoreTemplates = true;
    _currentCategory = category;
    // Don't clear cache, just reset pagination
  }

  /// Check if more templates are available
  bool get hasMoreTemplates => _hasMoreTemplates;

  /// Get current category filter
  String? get currentCategory => _currentCategory;

  /// Fetches templates from Firestore with pagination and optional category filtering
  Future<List<TemplateItem>> getTemplates({
    int limit = 5,
    String? category,
  }) async {
    try {
      // If no more templates are available, return empty list
      if (!_hasMoreTemplates) {
        return [];
      }

      // If category changed, reset pagination
      if (category != _currentCategory) {
        resetPaginationForCategory(category);
      }

      AppLogger.info('Fetching templates - Category: ${category ?? "All"}, Limit: $limit');

      // Use simple approach: get all active templates and handle pagination in memory
      final templates = await _getTemplatesInMemoryPagination(category, limit);

      AppLogger.info('Fetched ${templates.length} templates for category: ${category ?? "All"}');
      AppLogger.info('Template details: ${templates.map((t) => '${t.name} (${t.category})').join(', ')}');
      return templates;
    } catch (e) {
      AppLogger.error('Failed to fetch templates from Firestore', e);
      return [];
    }
  }

  // Cache for all templates to avoid repeated Firestore calls
  List<TemplateItem>? _allTemplatesCache;
  int _currentOffset = 0;

  /// Simple in-memory pagination approach
  Future<List<TemplateItem>> _getTemplatesInMemoryPagination(String? category, int limit) async {
    // Load all templates if not cached
    if (_allTemplatesCache == null || category != _currentCategory) {
      await _loadAllTemplates();
    }

    if (_allTemplatesCache == null || _allTemplatesCache!.isEmpty) {
      _hasMoreTemplates = false;
      return [];
    }

    // Filter by category if specified
    List<TemplateItem> filteredTemplates = _allTemplatesCache!;
    if (category != null && category.isNotEmpty && category != 'All') {
      filteredTemplates = _allTemplatesCache!
          .where((template) => template.category == category)
          .toList();
    }

    AppLogger.info('Total filtered templates for category ${category ?? "All"}: ${filteredTemplates.length}');

    // Reset offset if category changed
    if (category != _currentCategory) {
      _currentOffset = 0;
    }

    // Get the next batch
    final startIndex = _currentOffset;
    final endIndex = (startIndex + limit).clamp(0, filteredTemplates.length);
    final templates = filteredTemplates.sublist(startIndex, endIndex);

    // Update pagination state
    _currentOffset = endIndex;
    _hasMoreTemplates = endIndex < filteredTemplates.length;

    AppLogger.info('Returning ${templates.length} templates, offset: $_currentOffset, hasMore: $_hasMoreTemplates');

    return templates;
  }

  /// Load all templates from Firestore
  Future<void> _loadAllTemplates() async {
    try {
      AppLogger.info('Loading all templates from Firestore...');

      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      AppLogger.info('Found ${querySnapshot.docs.length} active templates in Firestore');

      _allTemplatesCache = querySnapshot.docs
          .map((doc) => TemplateItem.fromFirestore(doc))
          .toList();

      // Sort by upload date (newest first)
      _allTemplatesCache!.sort((a, b) => b.uploadDate.compareTo(a.uploadDate));

      AppLogger.info('Loaded and sorted ${_allTemplatesCache!.length} templates');

      // Log template details for debugging
      for (int i = 0; i < _allTemplatesCache!.length && i < 10; i++) {
        final template = _allTemplatesCache![i];
        AppLogger.info('Template $i: ${template.name} - Category: ${template.category}');
      }
    } catch (e) {
      AppLogger.error('Failed to load all templates', e);
      _allTemplatesCache = [];
    }
  }

  /// Get all available categories from templates
  Future<List<String>> getAvailableCategories() async {
    try {
      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      // Extract unique categories
      final categories = <String>{};
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final category = data['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categories.add(category);
        }
      }

      // Convert to list and sort
      final categoryList = categories.toList()..sort();

      AppLogger.info('Found ${categoryList.length} categories: $categoryList');
      return categoryList;
    } catch (e) {
      AppLogger.error('Failed to fetch categories from Firestore', e);
      return [];
    }
  }

  /// Get template count for each category
  Future<Map<String, int>> getCategoryCounts() async {
    try {
      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      // Count templates per category
      final categoryCounts = <String, int>{};
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final category = data['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
        }
      }

      AppLogger.info('Category counts: $categoryCounts');
      return categoryCounts;
    } catch (e) {
      AppLogger.error('Failed to fetch category counts from Firestore', e);
      return {};
    }
  }

  /// Get total template count
  Future<int> getTotalTemplateCount() async {
    try {
      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      AppLogger.error('Failed to fetch total template count from Firestore', e);
      return 0;
    }
  }

  /// Get template count for specific category
  Future<int> getTemplateCountForCategory(String category) async {
    try {
      Query query = _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true);

      if (category != 'All') {
        query = query.where('category', isEqualTo: category);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs.length;
    } catch (e) {
      AppLogger.error('Failed to fetch template count for category: $category', e);
      return 0;
    }
  }

  /// Increment view count for a template
  Future<void> incrementViewCount(String templateId) async {
    try {
      await _firestore.collection('templates').doc(templateId).update({
        'viewCount': FieldValue.increment(1),
      });
      AppLogger.info('Incremented view count for template: $templateId');
    } catch (e) {
      AppLogger.error('Failed to increment view count for template: $templateId', e);
    }
  }

  /// Increment download count for a template
  Future<void> incrementDownloadCount(String templateId) async {
    try {
      await _firestore.collection('templates').doc(templateId).update({
        'downloadCount': FieldValue.increment(1),
      });
      AppLogger.info('Incremented download count for template: $templateId');
    } catch (e) {
      AppLogger.error('Failed to increment download count for template: $templateId', e);
    }
  }

  /// Search templates by name or tags
  Future<List<TemplateItem>> searchTemplates({
    required String searchTerm,
    String? category,
    int limit = 20,
  }) async {
    try {
      // Build base query
      Query query = _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true);

      // Add category filter if specified
      if (category != null && category.isNotEmpty && category != 'All') {
        query = query.where('category', isEqualTo: category);
      }

      final querySnapshot = await query.get();

      // Filter by search term in memory (Firestore doesn't support full-text search)
      final searchTermLower = searchTerm.toLowerCase();
      final filteredTemplates = querySnapshot.docs
          .map((doc) => TemplateItem.fromFirestore(doc))
          .where((template) {
            final nameMatch = template.name.toLowerCase().contains(searchTermLower);
            final descriptionMatch = template.description?.toLowerCase().contains(searchTermLower) ?? false;
            final tagsMatch = template.tags?.any((tag) => tag.toLowerCase().contains(searchTermLower)) ?? false;
            return nameMatch || descriptionMatch || tagsMatch;
          })
          .take(limit)
          .toList();

      AppLogger.info('Found ${filteredTemplates.length} templates matching "$searchTerm"');
      return filteredTemplates;
    } catch (e) {
      AppLogger.error('Failed to search templates', e);
      return [];
    }
  }
}
