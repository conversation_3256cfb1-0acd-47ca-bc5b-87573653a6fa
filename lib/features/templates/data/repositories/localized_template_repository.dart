import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/localized_template_item.dart';
import '../../../../core/services/firebase_localization_service.dart';
import '../../../../core/utils/logger.dart';

/// Repository for handling localized template data from Firestore
class LocalizedTemplateRepository {
  final FirebaseFirestore _firestore;
  final FirebaseLocalizationService _localizationService;

  // Pagination state
  DocumentSnapshot? _lastDocument;
  bool _hasMoreTemplates = true;
  String? _currentCategory;

  LocalizedTemplateRepository({
    FirebaseFirestore? firestore,
    FirebaseLocalizationService? localizationService,
  }) : _firestore = firestore ?? FirebaseFirestore.instance,
       _localizationService = localizationService ?? FirebaseLocalizationService();

  /// Reset pagination state
  void resetPagination() {
    _lastDocument = null;
    _hasMoreTemplates = true;
    _currentCategory = null;
  }

  /// Reset pagination for category change
  void resetPaginationForCategory(String? category) {
    if (_currentCategory != category) {
      resetPagination();
      _currentCategory = category;
    }
  }

  /// Check if more templates are available
  bool get hasMoreTemplates => _hasMoreTemplates;

  /// Get current category filter
  String? get currentCategory => _currentCategory;

  /// Get localized templates with pagination and optional category filtering
  Future<List<LocalizedTemplateItem>> getLocalizedTemplates({
    required String userLanguageCode,
    int limit = 5,
    String? category,
  }) async {
    try {
      AppLogger.info('Fetching localized templates - Language: $userLanguageCode, Category: $category, Limit: $limit');

      // Reset pagination if category changed
      resetPaginationForCategory(category);

      // Build query
      Query query = _firestore.collection('templates')
          .where('isActive', isEqualTo: true);

      // Add category filter if specified
      if (category != null && category.isNotEmpty) {
        query = query.where('category', isEqualTo: category);
      }

      // Add pagination
      query = query.orderBy('uploadDate', descending: true).limit(limit);

      if (_lastDocument != null) {
        query = query.startAfterDocument(_lastDocument!);
      }

      final querySnapshot = await query.get();

      if (querySnapshot.docs.isEmpty) {
        _hasMoreTemplates = false;
        return [];
      }

      // Update pagination state
      _lastDocument = querySnapshot.docs.last;
      _hasMoreTemplates = querySnapshot.docs.length == limit;

      // Convert to localized templates
      final localizedTemplates = querySnapshot.docs
          .map((doc) => LocalizedTemplateItem.fromFirestore(
                doc: doc,
                userLanguageCode: userLanguageCode,
                localizationService: _localizationService,
              ))
          .toList();

      AppLogger.info('Retrieved ${localizedTemplates.length} localized templates');
      return localizedTemplates;

    } catch (e) {
      AppLogger.error('Error fetching localized templates', e);
      return [];
    }
  }

  /// Search localized templates by name or tags
  Future<List<LocalizedTemplateItem>> searchLocalizedTemplates({
    required String searchTerm,
    required String userLanguageCode,
    String? category,
    int limit = 20,
  }) async {
    try {
      AppLogger.info('Searching localized templates - Term: $searchTerm, Language: $userLanguageCode');

      // Build base query
      Query query = _firestore.collection('templates')
          .where('isActive', isEqualTo: true);

      // Add category filter if specified
      if (category != null && category.isNotEmpty) {
        query = query.where('category', isEqualTo: category);
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();

      // Convert to localized templates and filter by search term
      final localizedTemplates = querySnapshot.docs
          .map((doc) => LocalizedTemplateItem.fromFirestore(
                doc: doc,
                userLanguageCode: userLanguageCode,
                localizationService: _localizationService,
              ))
          .where((template) {
            final searchLower = searchTerm.toLowerCase();

            // Check name (with null safety)
            final nameMatches = template.name.toLowerCase().contains(searchLower);

            // Check description (with null safety)
            final descriptionMatches = template.description?.toLowerCase().contains(searchLower) ?? false;

            // Check tags (safely handle null or empty tags)
            final tagsMatch = template.tags?.isNotEmpty == true &&
                template.tags!.any((tag) => tag.toLowerCase().contains(searchLower));

            // Check category (with null safety)
            final categoryMatches = template.category?.toLowerCase().contains(searchLower) ?? false;

            return nameMatches || descriptionMatches || tagsMatch || categoryMatches;
          })
          .toList();

      AppLogger.info('Found ${localizedTemplates.length} matching localized templates');
      return localizedTemplates;

    } catch (e) {
      AppLogger.error('Error searching localized templates', e);
      return [];
    }
  }

  /// Get localized categories
  Future<List<String>> getLocalizedCategories({
    required String userLanguageCode,
  }) async {
    return await LocalizedTemplateItem.getLocalizedCategories(
      userLanguageCode: userLanguageCode,
      localizationService: _localizationService,
    );
  }

  /// Get localized tags
  Future<List<String>> getLocalizedTags({
    required String userLanguageCode,
  }) async {
    return await LocalizedTemplateItem.getLocalizedTags(
      userLanguageCode: userLanguageCode,
      localizationService: _localizationService,
    );
  }

  /// Get template count for each localized category
  Future<Map<String, int>> getLocalizedCategoryCounts({
    required String userLanguageCode,
  }) async {
    try {
      final categories = await getLocalizedCategories(userLanguageCode: userLanguageCode);
      final Map<String, int> counts = {};

      for (final category in categories) {
        final count = await getTemplateCountForLocalizedCategory(
          category: category,
          userLanguageCode: userLanguageCode,
        );
        counts[category] = count;
      }

      return counts;
    } catch (e) {
      AppLogger.error('Error getting localized category counts', e);
      return {};
    }
  }

  /// Get template count for specific localized category
  Future<int> getTemplateCountForLocalizedCategory({
    required String category,
    required String userLanguageCode,
  }) async {
    try {
      // For now, we'll count based on original category
      // In a full implementation, you'd need to query based on localized category
      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .where('category', isEqualTo: category)
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      AppLogger.error('Error getting template count for localized category', e);
      return 0;
    }
  }

  /// Get total template count
  Future<int> getTotalTemplateCount() async {
    try {
      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      AppLogger.error('Error getting total template count', e);
      return 0;
    }
  }

  /// Increment view count for a template
  Future<void> incrementViewCount(String templateId) async {
    try {
      await _firestore.collection('templates').doc(templateId).update({
        'viewCount': FieldValue.increment(1),
      });
    } catch (e) {
      AppLogger.error('Error incrementing view count', e);
    }
  }

  /// Increment download count for a template
  Future<void> incrementDownloadCount(String templateId) async {
    try {
      await _firestore.collection('templates').doc(templateId).update({
        'downloadCount': FieldValue.increment(1),
      });
    } catch (e) {
      AppLogger.error('Error incrementing download count', e);
    }
  }
}
