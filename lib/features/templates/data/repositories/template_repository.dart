import 'package:firebase_storage/firebase_storage.dart';
import '../../../../core/utils/logger.dart';

class TemplateRepository {
  final FirebaseStorage _storage;
  String? _lastItemName; // Track the last item for pagination
  bool _hasMoreTemplates = true; // Flag to indicate if more templates are available

  TemplateRepository({FirebaseStorage? storage})
      : _storage = storage ?? FirebaseStorage.instance;

  /// Reset pagination state
  void resetPagination() {
    _lastItemName = null;
    _hasMoreTemplates = true;
  }

  /// Check if more templates are available
  bool get hasMoreTemplates => _hasMoreTemplates;

  /// Fetches template images from Firebase Storage with pagination
  Future<List<String>> getTemplateImages({int limit = 5}) async {
    try {
      // If no more templates are available, return empty list
      if (!_hasMoreTemplates) {
        return [];
      }

      // Reference to the Templates folder in Firebase Storage
      final storageRef = _storage.ref().child('Templates');

      // Create a query with pagination
      ListResult result;
      if (_lastItemName != null) {
        // Get items after the last item
        AppLogger.info('Fetching templates after: $_lastItemName');
        result = await storageRef.list(ListOptions(
          maxResults: limit,
          pageToken: _lastItemName,
        ));
      } else {
        // Get first page of items
        AppLogger.info('Fetching first page of templates with limit: $limit');
        result = await storageRef.list(ListOptions(
          maxResults: limit,
        ));
      }

      // Update pagination state
      if (result.items.isEmpty || result.nextPageToken == null) {
        _hasMoreTemplates = false;
        AppLogger.info('No more templates available');
      } else {
        _lastItemName = result.nextPageToken;
        AppLogger.info('Next page token: $_lastItemName');
      }

      // Get download URLs for all items
      final List<String> imageUrls = [];

      for (final Reference ref in result.items) {
        final String downloadUrl = await ref.getDownloadURL();
        imageUrls.add(downloadUrl);
      }

      AppLogger.info('Fetched ${imageUrls.length} templates');
      return imageUrls;
    } catch (e) {
      AppLogger.error('Failed to fetch template images', e);
      return [];
    }
  }

  /// Gets a specific template image by name
  Future<String?> getTemplateImageByName(String imageName) async {
    try {
      final ref = _storage.ref().child('Templates/$imageName');
      return await ref.getDownloadURL();
    } catch (e) {
      AppLogger.error('Failed to fetch template image: $imageName', e);
      return null;
    }
  }
}
