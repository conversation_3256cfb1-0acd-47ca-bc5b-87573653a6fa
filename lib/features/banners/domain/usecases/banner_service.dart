import '../entities/banner_item.dart';
import '../../data/repositories/banner_repository.dart';

/// Service for managing banner-related operations
class BannerService {
  final BannerRepository _bannerRepository;

  // Cache for banners
  static List<BannerItem>? _cachedBanners;
  static DateTime? _cacheTimestamp;
  static const Duration _cacheExpiry = Duration(minutes: 30); // Cache for 30 minutes

  BannerService(this._bannerRepository);

  /// Reset pagination state
  void resetPagination() {
    _bannerRepository.resetPagination();
  }

  /// Clear banner cache
  void clearCache() {
    _cachedBanners = null;
    _cacheTimestamp = null;
  }

  /// Reset pagination and clear cache
  void resetPaginationAndCache() {
    resetPagination();
    clearCache();
  }

  /// Check if more banners are available
  bool get hasMoreBanners => _bannerRepository.hasMoreBanners;

  /// Get banners with pagination
  Future<List<BannerItem>> getBanners({int limit = 5}) async {
    // Check if we have valid cached data
    if (_cachedBanners != null && _cacheTimestamp != null) {
      final now = DateTime.now();
      final cacheAge = now.difference(_cacheTimestamp!);

      if (cacheAge < _cacheExpiry) {
        // Return cached data with pagination simulation
        final startIndex = _cachedBanners!.length - (_bannerRepository.hasMoreBanners ? limit : 0);
        final endIndex = startIndex + limit;

        if (startIndex < _cachedBanners!.length) {
          return _cachedBanners!.sublist(
            startIndex.clamp(0, _cachedBanners!.length),
            endIndex.clamp(0, _cachedBanners!.length),
          );
        }
      }
    }

    // Cache is invalid or empty, fetch from repository
    final List<String> imageUrls = await _bannerRepository.getBannerImages(limit: limit);
    final newBanners = imageUrls.map((url) => BannerItem.fromStorageUrl(url)).toList();

    // Update cache
    if (_cachedBanners == null) {
      _cachedBanners = newBanners;
    } else {
      _cachedBanners!.addAll(newBanners);
    }
    _cacheTimestamp = DateTime.now();

    return newBanners;
  }

  /// Get all available banners (legacy method, use getBanners with pagination instead)
  Future<List<BannerItem>> getAllBanners() async {
    // Reset pagination to ensure we get all banners from the beginning
    resetPagination();

    // Get first batch of banners
    List<BannerItem> allBanners = await getBanners(limit: 100);

    return allBanners;
  }

  /// Get active banners only
  Future<List<BannerItem>> getActiveBanners() async {
    final List<BannerItem> allBanners = await getAllBanners();

    // Filter out inactive banners
    return allBanners.where((banner) => banner.isActive).toList();
  }
}
