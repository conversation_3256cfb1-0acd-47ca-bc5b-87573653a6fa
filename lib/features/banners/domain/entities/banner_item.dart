import 'package:flutter/foundation.dart';

/// Represents a banner item to be displayed in the app
class BannerItem {
  final String id;
  final String imageUrl;
  final String? title;
  final String? subtitle;
  final String? actionUrl;
  final bool isActive;

  const BannerItem({
    required this.id,
    required this.imageUrl,
    this.title,
    this.subtitle,
    this.actionUrl,
    this.isActive = true,
  });

  /// Create a BannerItem from a Firebase Storage URL
  factory BannerItem.fromStorageUrl(String url) {
    // Extract the file name from the URL to use as ID
    final fileName = url.split('/').last.split('?').first;
    final id = fileName.split('.').first;
    
    return BannerItem(
      id: id,
      imageUrl: url,
      isActive: true,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is BannerItem &&
        other.id == id &&
        other.imageUrl == imageUrl &&
        other.title == title &&
        other.subtitle == subtitle &&
        other.actionUrl == actionUrl &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        imageUrl.hashCode ^
        title.hashCode ^
        subtitle.hashCode ^
        actionUrl.hashCode ^
        isActive.hashCode;
  }
}
