import 'package:firebase_storage/firebase_storage.dart';
import '../../../../core/utils/logger.dart';

/// Repository for fetching banner images from Firebase Storage
class BannerRepository {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  String? _lastItemName; // Track the last item for pagination
  bool _hasMoreBanners = true; // Flag to indicate if more banners are available

  /// Reset pagination state
  void resetPagination() {
    _lastItemName = null;
    _hasMoreBanners = true;
  }

  /// Check if more banners are available
  bool get hasMoreBanners => _hasMoreBanners;

  /// Get banner images from Firebase Storage with pagination
  Future<List<String>> getBannerImages({int limit = 5}) async {
    try {
      // If no more banners are available, return empty list
      if (!_hasMoreBanners) {
        return [];
      }

      AppLogger.info('Attempting to fetch banner images from "banners" folder with limit: $limit');

      // Reference to the banners folder in Firebase Storage
      final storageRef = _storage.ref().child('banners');

      // Create a query with pagination
      ListResult result;
      if (_lastItemName != null) {
        // Get items after the last item
        AppLogger.info('Fetching banners after: $_lastItemName');
        result = await storageRef.list(ListOptions(
          maxResults: limit,
          pageToken: _lastItemName,
        ));
      } else {
        // Get first page of items
        AppLogger.info('Fetching first page of banners');
        result = await storageRef.list(ListOptions(
          maxResults: limit,
        ));
      }

      // Update pagination state
      if (result.items.isEmpty || result.nextPageToken == null) {
        _hasMoreBanners = false;
        AppLogger.info('No more banners available');
      } else {
        _lastItemName = result.nextPageToken;
        AppLogger.info('Next page token: $_lastItemName');
      }

      AppLogger.info('Found ${result.items.length} items in banners folder');
      AppLogger.info('Found ${result.prefixes.length} prefixes in banners folder');

      // Get download URLs for all items
      final List<String> imageUrls = [];
      for (final Reference ref in result.items) {
        AppLogger.info('Processing banner: ${ref.name}');
        final String downloadUrl = await ref.getDownloadURL();
        imageUrls.add(downloadUrl);
      }

      AppLogger.info('Fetched ${imageUrls.length} banner images');
      return imageUrls;
    } catch (e) {
      AppLogger.error('Error fetching banner images', e);
      return [];
    }
  }
}
