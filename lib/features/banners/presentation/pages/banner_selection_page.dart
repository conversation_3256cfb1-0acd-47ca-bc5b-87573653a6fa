import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../domain/entities/banner_item.dart';
import '../../domain/usecases/banner_service.dart';

class BannerSelectionPage extends StatefulWidget {
  final List<BannerItem> banners;
  final BannerItem? selectedBanner;
  final Function(BannerItem) onBannerSelected;

  const BannerSelectionPage({
    super.key,
    required this.banners,
    this.selectedBanner,
    required this.onBannerSelected,
  });

  @override
  State<BannerSelectionPage> createState() => _BannerSelectionPageState();
}

class _BannerSelectionPageState extends State<BannerSelectionPage> {
  late List<BannerItem> _banners;
  bool _isLoadingMore = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _banners = List.from(widget.banners);

    // Add scroll listener to detect when user reaches the end
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  // Scroll listener to detect when user reaches the end
  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      _loadMoreBanners();
    }
  }

  // Load more banners
  Future<void> _loadMoreBanners() async {
    final bannerService = Provider.of<BannerService>(context, listen: false);

    // Check if we're already loading or if there are no more banners
    if (_isLoadingMore || !bannerService.hasMoreBanners) {
      return;
    }

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Load more banners
      final moreBanners = await bannerService.getBanners(limit: 5);

      // Filter out banners that we already have (avoid duplicates)
      final newBanners = moreBanners.where((newBanner) {
        // Check if this banner is already in our list
        return !_banners.any((existingBanner) => existingBanner.id == newBanner.id);
      }).toList();

      setState(() {
        _banners.addAll(newBanners);
        _isLoadingMore = false;
      });

      // If we didn't get any new banners but the service thinks there are more,
      // try loading more (this handles the case where all loaded banners were duplicates)
      if (newBanners.isEmpty && bannerService.hasMoreBanners) {
        // Use Future.microtask to avoid calling setState during build
        Future.microtask(() => _loadMoreBanners());
      }
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load more banners')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Select Banner',
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: _banners.isEmpty
            ? Center(
                child: Text(
                  'No banners available',
                  style: TextStyle(
                    color: isPremium ? AppTheme.premiumGold : null,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              )
            : ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16.0),
                itemCount: _banners.length + (_isLoadingMore ? 1 : 0),
                itemBuilder: (context, index) {
                  // Show loading indicator at the end
                  if (index == _banners.length) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: CircularProgressIndicator(
                          color: isPremium ? AppTheme.premiumGold : null,
                        ),
                      ),
                    );
                  }

                  final banner = _banners[index];
                  final isSelected = widget.selectedBanner?.id == banner.id;

                  return GestureDetector(
                    onTap: () {
                      widget.onBannerSelected(banner);
                      Navigator.pop(context);
                    },
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: isSelected
                            ? Border.all(
                                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                width: 3,
                              )
                            : isPremium
                                ? Border.all(
                                    color: AppTheme.premiumGold.withAlpha(77), // 0.3 opacity
                                    width: 1,
                                  )
                                : null,
                        boxShadow: [
                          BoxShadow(
                            color: isPremium
                                ? AppTheme.premiumGold.withAlpha(51) // 0.2 opacity
                                : Colors.black.withAlpha(26), // 0.1 opacity
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                        color: isPremium ? AppTheme.premiumDarkGrey.withAlpha(100) : null,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Banner image
                            AspectRatio(
                              aspectRatio: 16 / 5, // Wide banner aspect ratio
                              child: CachedNetworkImage(
                                imageUrl: banner.imageUrl,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => Container(
                                  color: isPremium ? AppTheme.premiumDarkGrey : Colors.grey[200],
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      color: isPremium ? AppTheme.premiumGold : null,
                                    ),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  color: isPremium ? AppTheme.premiumDarkGrey : Colors.grey[200],
                                  child: Icon(
                                    Icons.error,
                                    color: isPremium ? AppTheme.premiumGold : null,
                                  ),
                                ),
                              ),
                            ),

                            // Banner info
                            Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      'Banner ID: ${banner.id}',
                                      style: isPremium
                                          ? TextStyle(
                                              color: AppTheme.premiumGold,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            )
                                          : AppTheme.bodyMedium,
                                    ),
                                  ),
                                  if (isSelected)
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                        borderRadius: BorderRadius.circular(12),
                                        boxShadow: isPremium
                                            ? [
                                                BoxShadow(
                                                  color: AppTheme.premiumGold.withAlpha(77),
                                                  blurRadius: 4,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ]
                                            : null,
                                      ),
                                      child: Text(
                                        'Selected',
                                        style: TextStyle(
                                          color: isPremium ? AppTheme.premiumBlack : Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
      ),
    );
  }
}
