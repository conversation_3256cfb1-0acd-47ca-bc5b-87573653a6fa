// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD7IfwGLzey9A1exYhu0wQYnA_QRxC_FhA',
    appId: '1:1076744621181:web:91df5076c6564ccdaf3514',
    messagingSenderId: '1076744621181',
    projectId: 'quickposters-app',
    authDomain: 'quickposters-app.firebaseapp.com',
    storageBucket: 'quickposters-app.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCMvfnUNvkiwsEisCc65vYgZxQuhZYl1K4',
    appId: '1:1076744621181:android:4fe49c52078354a1af3514',
    messagingSenderId: '1076744621181',
    projectId: 'quickposters-app',
    storageBucket: 'quickposters-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA-qlD9fpc71KBG3kdqRqd_E5MLbzCsA-s',
    appId: '1:1076744621181:ios:e2ef52aa77201e3aaf3514',
    messagingSenderId: '1076744621181',
    projectId: 'quickposters-app',
    storageBucket: 'quickposters-app.firebasestorage.app',
    iosBundleId: 'com.example.quickposters',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyA-qlD9fpc71KBG3kdqRqd_E5MLbzCsA-s',
    appId: '1:1076744621181:ios:e2ef52aa77201e3aaf3514',
    messagingSenderId: '1076744621181',
    projectId: 'quickposters-app',
    storageBucket: 'quickposters-app.firebasestorage.app',
    iosBundleId: 'com.example.quickposters',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyD7IfwGLzey9A1exYhu0wQYnA_QRxC_FhA',
    appId: '1:1076744621181:web:0b8f113f4c37f1e7af3514',
    messagingSenderId: '1076744621181',
    projectId: 'quickposters-app',
    authDomain: 'quickposters-app.firebaseapp.com',
    storageBucket: 'quickposters-app.firebasestorage.app',
  );
}
