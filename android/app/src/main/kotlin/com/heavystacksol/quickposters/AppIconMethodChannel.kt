package com.heavystacksol.quickposters

import android.content.ComponentName
import android.content.pm.PackageManager
import android.util.Log
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

/**
 * Method channel handler for changing app icons
 */
class AppIconMethodChannel(private val mainActivity: MainActivity) {
    private val TAG = "AppIconMethodChannel"
    private val CHANNEL = "com.heavystacksol.quickposters/app_icon"

    /**
     * Setup the method channel
     */
    fun configureChannel(flutterEngine: FlutterEngine) {
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "changeIcon" -> {
                    val iconType = call.argument<String>("iconType")
                    if (iconType != null) {
                        changeIcon(iconType, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Icon type is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    /**
     * Change the app icon based on the icon type
     */
    private fun changeIcon(iconType: String, result: MethodChannel.Result) {
        try {
            val packageManager = mainActivity.packageManager
            val packageName = mainActivity.packageName

            // Disable all activity-alias components first
            disableComponent(packageManager, packageName, "RegularIcon")
            disableComponent(packageManager, packageName, "PremiumIcon")

            // Enable the appropriate activity-alias based on icon type
            when (iconType) {
                "premium" -> {
                    enableComponent(packageManager, packageName, "PremiumIcon")
                    Log.i(TAG, "Changed icon to premium")
                }
                else -> {
                    enableComponent(packageManager, packageName, "RegularIcon")
                    Log.i(TAG, "Changed icon to regular")
                }
            }

            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error changing app icon", e)
            result.error("ICON_CHANGE_FAILED", e.message, null)
        }
    }

    /**
     * Enable a component
     */
    private fun enableComponent(packageManager: PackageManager, packageName: String, componentName: String) {
        val component = ComponentName(packageName, "$packageName.$componentName")
        packageManager.setComponentEnabledSetting(
            component,
            PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
            PackageManager.DONT_KILL_APP
        )
    }

    /**
     * Disable a component
     */
    private fun disableComponent(packageManager: PackageManager, packageName: String, componentName: String) {
        val component = ComponentName(packageName, "$packageName.$componentName")
        packageManager.setComponentEnabledSetting(
            component,
            PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
            PackageManager.DONT_KILL_APP
        )
    }
}
