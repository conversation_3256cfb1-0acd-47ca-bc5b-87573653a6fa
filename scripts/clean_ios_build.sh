#!/bin/bash

# Clean iOS build script for QuickPosters
# This script cleans the iOS build to ensure icon changes are properly applied

echo "🧹 Cleaning iOS build for QuickPosters..."

# Navigate to the project root
cd "$(dirname "$0")/.."

# Clean Flutter build
echo "🔄 Cleaning Flutter build..."
flutter clean

# Remove iOS build artifacts
echo "🗑️ Removing iOS build artifacts..."
rm -rf ios/build/
rm -rf ios/.symlinks/
rm -rf ios/Flutter/Flutter.framework
rm -rf ios/Flutter/Flutter.podspec
rm -rf ios/Pods/
rm -rf ios/Podfile.lock

# Clean Xcode derived data (if available)
if command -v xcodebuild &> /dev/null; then
    echo "🔄 Cleaning Xcode derived data..."
    xcodebuild clean -workspace ios/Runner.xcworkspace -scheme Runner
fi

# Get Flutter dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

# Install iOS dependencies
echo "🍎 Installing iOS dependencies..."
cd ios
pod install --repo-update
cd ..

echo "✅ iOS build cleaned successfully!"
echo ""
echo "Next steps:"
echo "1. Open ios/Runner.xcworkspace in Xcode"
echo "2. Verify that the icon sets (regular.appiconset, premium.appiconset) are properly referenced"
echo "3. Build and run the app"
echo "4. Test icon switching functionality"
