#!/usr/bin/env python3

import os
import json
from PIL import Image
import sys

def verify_icon_set(icon_set_path, icon_set_name):
    """Verify that an icon set has all required files and correct sizes."""
    print(f"\n🔍 Verifying {icon_set_name} icon set...")
    
    contents_json_path = os.path.join(icon_set_path, "Contents.json")
    
    if not os.path.exists(contents_json_path):
        print(f"❌ Contents.json not found in {icon_set_path}")
        return False
    
    try:
        with open(contents_json_path, 'r') as f:
            contents = json.load(f)
    except Exception as e:
        print(f"❌ Error reading Contents.json: {e}")
        return False
    
    images = contents.get('images', [])
    if not images:
        print(f"❌ No images defined in Contents.json")
        return False
    
    print(f"📱 Found {len(images)} image definitions")
    
    missing_files = []
    invalid_sizes = []
    
    for image in images:
        filename = image.get('filename')
        if not filename:
            continue
            
        file_path = os.path.join(icon_set_path, filename)
        
        if not os.path.exists(file_path):
            missing_files.append(filename)
            continue
        
        try:
            with Image.open(file_path) as img:
                width, height = img.size
                
                # Extract expected size from filename (e.g., "180.png" -> 180)
                expected_size = int(filename.split('.')[0])
                
                if width != expected_size or height != expected_size:
                    invalid_sizes.append(f"{filename} (expected {expected_size}x{expected_size}, got {width}x{height})")
                    
        except Exception as e:
            print(f"⚠️ Error checking {filename}: {e}")
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    
    if invalid_sizes:
        print(f"❌ Invalid sizes: {', '.join(invalid_sizes)}")
        return False
    
    print(f"✅ {icon_set_name} icon set is valid!")
    return True

def main():
    """Main function to verify all icon sets."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    assets_path = os.path.join(project_root, "ios", "Runner", "Assets.xcassets")
    
    if not os.path.exists(assets_path):
        print(f"❌ Assets.xcassets not found at {assets_path}")
        sys.exit(1)
    
    print("🔍 Verifying iOS app icon sets...")
    
    icon_sets = [
        ("AppIcon.appiconset", "Default AppIcon"),
        ("regular.appiconset", "Regular"),
        ("premium.appiconset", "Premium")
    ]
    
    all_valid = True
    
    for icon_set_dir, icon_set_name in icon_sets:
        icon_set_path = os.path.join(assets_path, icon_set_dir)
        
        if not os.path.exists(icon_set_path):
            print(f"⚠️ {icon_set_name} icon set not found at {icon_set_path}")
            continue
        
        if not verify_icon_set(icon_set_path, icon_set_name):
            all_valid = False
    
    if all_valid:
        print("\n✅ All icon sets are valid!")
        print("\nNext steps:")
        print("1. Clean and rebuild the iOS project")
        print("2. Test icon switching in the app")
        print("3. Check device logs for any icon-related errors")
    else:
        print("\n❌ Some icon sets have issues. Please fix them before testing.")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Verification cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
