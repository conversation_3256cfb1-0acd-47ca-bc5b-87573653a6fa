//
//  Generated file. Do not edit.
//

// clang-format off

#import "GeneratedPluginRegistrant.h"

#if __has_include(<flutter_dynamic_icon/FLTDynamicIconPlugin.h>)
#import <flutter_dynamic_icon/FLTDynamicIconPlugin.h>
#else
@import flutter_dynamic_icon;
#endif

@implementation GeneratedPluginRegistrant

+ (void)registerWithRegistry:(NSObject<FlutterPluginRegistry>*)registry {
  [FLTDynamicIconPlugin registerWithRegistrar:[registry registrarForPlugin:@"FLTDynamicIconPlugin"]];
}

@end
