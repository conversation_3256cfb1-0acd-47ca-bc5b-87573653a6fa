# QuickPosters - Flutter App

## 📢 Push Notifications Setup

### Two Notification Methods Available:

#### 1. **User Segment (Recommended for 100% reach)**
- **Location**: Firebase Console → Messaging → New notification → User segment
- **Reaches**: ALL users (no topic subscription required)
- **Uses**: Firebase Analytics audiences for targeting
- **Best for**: Critical announcements and updates

**How to send via User Segment:**
1. Open [Firebase Console](https://console.firebase.google.com/project/quickposters-app/messaging)
2. Go to Messaging → New notification
3. Select "User segment" as target
4. Choose audience (e.g., "All users")
5. Compose and send notification

#### 2. **Topic-Based (Admin Panel)**
- **Location**: Admin Dashboard → Push Notifications
- **Reaches**: Only users subscribed to topics
- **Uses**: FCM HTTP v1 API with OAuth 2.0
- **Best for**: Targeted campaigns and user segments

### Firebase Analytics Integration

The app automatically logs user events and sets user properties for better segmentation:

- **User Properties**: `user_type`, `is_premium`, `platform`, `profile_complete`
- **Events**: `login_success`, `sign_up_success`, `template_view`, `notification_received`, etc.

### Topic Subscriptions

Users are automatically subscribed to relevant topics after authentication:

#### 🔵 **User Type-Based Topics**
- `all_users` - All authenticated users
- `businessman` - Business users
- `politician` - Political users  
- `individual_user` - General public/non-business users
- `premium_users` - Premium subscribers
- `free_users` - Free tier users

#### 🟢 **Language-Based Topics**
- `lang_marathi` - Users preferring Marathi
- `lang_hindi` - Users preferring Hindi
- `lang_english` - Users preferring English (default)

#### 🔴 **Feature-Based Topics**
- `new_templates` - Template update notifications
- `offers` - Discounts and promotions
- `app_updates` - App version updates

#### 🟠 **Engagement-Based Topics**
- `active_users` - Recently active users
- `inactive_users` - Users who haven't used the app recently

#### 🟣 **Region-Based Topics** (Future Use)
- `region_india` - India-specific content
- `region_maharashtra` - Maharashtra-specific content

#### 📱 **Legacy Support Topics**
- `business_users`, `businessman_users` - Legacy business topics
- `political_users`, `politician_users` - Legacy political topics
- `regular_users` - Legacy regular user topic

### Firebase Setup Requirements

1. **Service Account**: Download JSON from Firebase Console → Project Settings → Service Accounts
2. **File Placement**: Save as `assets/firebase_service_account.json`
3. **API Enablement**: Enable "Firebase Cloud Messaging API" in Google Cloud Console
4. **Project ID**: Verify matches `quickposters-app`

### Testing Notifications

1. **Firebase Console Method**: Use User Segment for guaranteed delivery to all users
2. **Admin Panel Method**: Use for targeted topic-based notifications to specific user segments

### Smart Subscription System

The app automatically subscribes users to relevant topics based on their situation:

- **All Users**: Subscribed to `all_users` immediately when app opens (even without login)
- **Authenticated Users**: Additional subscriptions based on user type, premium status, and language
- **Dynamic Updates**: Subscriptions automatically update when user status changes (e.g., premium upgrade)

### Subscription Logic

- **User Type**: Only subscribe to relevant user type topic (`businessman`, `politician`, or `individual_user`)
- **Premium Status**: Subscribe to either `premium_users` or `free_users` based on actual status
- **Language**: Subscribe only to user's preferred language topic
- **Features**: All authenticated users get `new_templates`, `offers`, `app_updates`, and `active_users`

## 🚀 Getting Started

1. Clone the repository
2. Run `flutter pub get`
3. Add your Firebase configuration files
4. Add service account JSON to assets
5. Run `flutter run`

## 📱 Features

- Phone number authentication
- Template-based poster creation
- Premium subscription system
- Admin dashboard with notification management
- Firebase Analytics integration
- Push notifications (Topic-based + User Segment)

## 🔧 Dependencies

- Firebase Core, Auth, Firestore, Storage, Messaging, Analytics
- Flutter BLoC for state management
- Provider for dependency injection
- Image picker and editing capabilities
- Local notifications support
