# Firebase Cloud Messaging (FCM) HTTP v1 API Setup Guide

## 🚀 Overview

This guide will help you set up push notifications using the modern FCM HTTP v1 API with OAuth 2.0 authentication, replacing the deprecated legacy server key approach.

## ✅ Prerequisites

- Firebase project set up
- Flutter app configured with Firebase
- Admin access to Firebase Console

## 📋 Step-by-Step Setup

### 1. Enable Firebase Cloud Messaging API

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (`quickposters-app`)
3. Navigate to **APIs & Services** > **Library**
4. Search for "Firebase Cloud Messaging API"
5. Click **Enable**

### 2. Create Service Account

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to **Project Settings** > **Service Accounts**
4. Click **Generate new private key**
5. Download the JSON file

### 3. Configure Service Account Credentials

#### Option A: Using Assets (Recommended for Development)

1. Rename the downloaded JSON file to `firebase_service_account.json`
2. Place it in the `assets/` directory of your Flutter project
3. The file is already configured in `pubspec.yaml`

#### Option B: Using Environment Variables (Recommended for Production)

1. Store the service account credentials as environment variables
2. Modify `_loadServiceAccountCredentials()` in `AdminNotificationService` to load from environment variables

### 4. Update Project Configuration

1. Open `lib/features/admin/domain/services/admin_notification_service.dart`
2. Update the `_projectId` constant with your actual Firebase project ID:
   ```dart
   static const String _projectId = 'your-actual-project-id';
   ```

### 5. Security Considerations

⚠️ **Important Security Notes:**

- **Never commit service account credentials to version control**
- Add `assets/firebase_service_account.json` to your `.gitignore`
- For production, use environment variables or secure secret management
- Consider using Firebase App Check for additional security

### 6. Test the Implementation

1. Build and run your app
2. Go to Admin Dashboard > Push Notifications
3. Create a test notification
4. Send it to verify the setup works

## 🔧 Implementation Details

### FCM HTTP v1 API Benefits

- ✅ **Secure**: Uses OAuth 2.0 tokens instead of static keys
- ✅ **Future-proof**: Actively supported by Google
- ✅ **Better error handling**: More detailed error responses
- ✅ **Enhanced features**: Support for newer FCM features

### API Endpoints

- **Legacy (Deprecated)**: `https://fcm.googleapis.com/fcm/send`
- **HTTP v1 (Current)**: `https://fcm.googleapis.com/v1/projects/{project-id}/messages:send`

### Authentication Flow

1. Load service account credentials
2. Create OAuth 2.0 client
3. Generate access token
4. Use token in Authorization header
5. Send notification via HTTP v1 API

## 🛠️ Troubleshooting

### Common Issues

1. **"Service account credentials not found"**
   - Ensure `firebase_service_account.json` is in the `assets/` folder
   - Check that the file is properly formatted JSON

2. **"Failed to get access token"**
   - Verify the service account has the correct permissions
   - Ensure Firebase Cloud Messaging API is enabled

3. **"FCM request failed: 403"**
   - Check that the service account has `Firebase Cloud Messaging Admin` role
   - Verify the project ID is correct

4. **"Invalid token"**
   - The access token may have expired (they're valid for 1 hour)
   - The service will automatically refresh tokens

### Debug Steps

1. Check Firebase Console logs
2. Verify service account permissions
3. Test with a simple topic message first
4. Use Firebase Console to send test messages

## 📱 Client-Side Setup

Ensure your Flutter app is properly configured for FCM:

1. **Android**: `google-services.json` in `android/app/`
2. **iOS**: `GoogleService-Info.plist` in `ios/Runner/`
3. **Permissions**: Notification permissions requested
4. **Topics**: Users subscribed to appropriate topics

## 🔄 Migration from Legacy API

If you were using the legacy server key:

1. ✅ Remove all references to `Authorization: key=SERVER_KEY`
2. ✅ Replace with OAuth 2.0 token-based authentication
3. ✅ Update endpoint URLs to HTTP v1 format
4. ✅ Update payload structure for HTTP v1 API

## 📚 Additional Resources

- [FCM HTTP v1 API Documentation](https://firebase.google.com/docs/cloud-messaging/http-server-ref)
- [Service Account Authentication](https://cloud.google.com/docs/authentication/production)
- [Firebase Admin SDK Setup](https://firebase.google.com/docs/admin/setup)

## 🎯 Next Steps

1. Set up your service account credentials
2. Test the notification system
3. Implement proper error handling
4. Set up monitoring and analytics
5. Consider implementing notification scheduling with Cloud Functions

---

**Note**: This implementation provides a secure, modern approach to FCM that will continue to work beyond the legacy API deprecation date. 