{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e54a12dff8e3c46f1e24ee54d2df5f95", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892005d2921633428b21c2808d64855e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e07b7d83183290430d2daa6d7fe642eb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98997e66c0e98e1db88e18272b5fafa165", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e07b7d83183290430d2daa6d7fe642eb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8402fb7425dde778441c192bedf25e0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983fbf0fa66ec523637eb2fef8ab64836a", "guid": "bfdfe7dc352907fc980b868725387e98e300db694d3b145abd9c294e1038b740", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820644b576627e894d304f0dfc061d185", "guid": "bfdfe7dc352907fc980b868725387e98a301e35bfdf42e1d0f48773e23f17d48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869606c2520ebdcd9aabf748d0a82e7c3", "guid": "bfdfe7dc352907fc980b868725387e984ab2b9bbbe9a8130483f9456ed924724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888a9ddc15b80212edc535b6418af3371", "guid": "bfdfe7dc352907fc980b868725387e98e28f5e5ae1a99f59d3bd23a41ebd3b11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981974e3248e0261a415c6bf36be0cfafd", "guid": "bfdfe7dc352907fc980b868725387e982945877bc29f76221792cd877b495477", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869b0ff79917c77361f8f831346a212b9", "guid": "bfdfe7dc352907fc980b868725387e9802a15522752ba158127e90850c5689ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889f4a9e3fd73c856853377239a88b8f5", "guid": "bfdfe7dc352907fc980b868725387e988ac7b92ed245bb7f57afadc1d15ab81d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808aa0937021121ba300583cc1fd6784a", "guid": "bfdfe7dc352907fc980b868725387e987376ad66d35bf74dbc10b6b234d6f846", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b04d7e64ec65556c0d495a54c19925", "guid": "bfdfe7dc352907fc980b868725387e98d211f82cea3e51ae7e67996167739f3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3d6d2c93f7ceec704b9d3821dbfac10", "guid": "bfdfe7dc352907fc980b868725387e9886eb2d857b58bd7fa504a7cda86c874f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b494f386cec3a26e4c8546ba27999320", "guid": "bfdfe7dc352907fc980b868725387e98915cec4941ac715fea7a911676f65163", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2c1b6065e58071e2ceac464411cac74", "guid": "bfdfe7dc352907fc980b868725387e98262c0259af0f6c346cc9cb8babb4b979", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a90a45fc6afa03b6854589c2e1e74c4", "guid": "bfdfe7dc352907fc980b868725387e98410534e5af45d620a747f5cfbd78ddcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849377b6a4b5e74661265cce244b72205", "guid": "bfdfe7dc352907fc980b868725387e98651fbc75b4b514498295808f9c6f605f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98709a4ba4d11b5df355eded857df06e02", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ca625a77ed8b35a87b647d7fb6e7ce83", "guid": "bfdfe7dc352907fc980b868725387e98e77d116665331a698a3e2488ec60092b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845c90e5ae7b8bcec1ea1d80b2c5c576c", "guid": "bfdfe7dc352907fc980b868725387e9817a3b302c22214f1083d8f4f4e6ab94c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98363a86b390e47f387d416b0a0211c343", "guid": "bfdfe7dc352907fc980b868725387e984d185a538fcb4399ee02393f75f183cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e83471aaaf706cfcb7ee5ef64246d72", "guid": "bfdfe7dc352907fc980b868725387e98d914f5595f1733db4d2146259be6348d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5c9118cfe978a82d77962af0b0576dd", "guid": "bfdfe7dc352907fc980b868725387e9860556e7deb57b1d2a33a35dc743f0db3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986290064d5262c188e6710e7a228dc056", "guid": "bfdfe7dc352907fc980b868725387e98147af6cfcb1e7dff07a6b3d52b86dd5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98924778a99d82c8c5b5e9bfb17ef1b803", "guid": "bfdfe7dc352907fc980b868725387e9883f2667d9aa7bcfe57a381f508b13401"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbcce0fdc8dc10b8585275bab9956e1f", "guid": "bfdfe7dc352907fc980b868725387e986b4b4b4ca89969c4e5bb6e4641c8f5ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861b148f74780baed6f97917a8673dd49", "guid": "bfdfe7dc352907fc980b868725387e982dfcda8280b4d8f9dafc4da4c1c903da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c4a4ad7422bf6244dc68de9123d4aaf", "guid": "bfdfe7dc352907fc980b868725387e982d545dca737430ce62934ec475ec0bab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98307c18175af84bd3a4ba73fc488ca291", "guid": "bfdfe7dc352907fc980b868725387e986b7c2f8760ffaf440ea375915ddbb9ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d81899579a2eefb133dfa15236a9319d", "guid": "bfdfe7dc352907fc980b868725387e98cd1b0435ecbbdeb695a196a93a31c6d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e6f2a8b4bdc038ab0b80565d86e8572", "guid": "bfdfe7dc352907fc980b868725387e98b600c49374b67d4052bf25a049e908e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f82aa82071427db32014cf54ae4a1b8", "guid": "bfdfe7dc352907fc980b868725387e9828c90a6dedccd826c21fde68ed61cb59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882d1ae0ea6da9120f97e9f4248f24f10", "guid": "bfdfe7dc352907fc980b868725387e987800c6c5fdb08a437483882098ce650f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984061260ed82ca186041b7062aa254cad", "guid": "bfdfe7dc352907fc980b868725387e98b8b05ad2d76ec051ac24e35f85b93361"}], "guid": "bfdfe7dc352907fc980b868725387e986ab5620dc55715e8e8f6fcd49a2ef83a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e984fe03a62b0f3efa2b48d3a3971811d17"}], "guid": "bfdfe7dc352907fc980b868725387e98319a14c9389993bee5d96d155795db97", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987cd2512697fd8475ad5d01e85d7d220e", "targetReference": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9"}], "guid": "bfdfe7dc352907fc980b868725387e9868992c43aa4117f939ab7493574523f6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982a930221dc4925ae3ad26ac05af9179d", "name": "in_app_purchase_storekit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b48307e2bc58dc7155fc2e80bc197afb", "name": "in_app_purchase_storekit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}