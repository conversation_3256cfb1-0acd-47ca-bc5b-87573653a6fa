{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982f74181819a2b3c5ae77b82c5c3d3283", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983d8678508209d3283c42186719c0130b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d620d4393b68c44c93b502a52ae3c82f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988ffebfbd1e4e5c5c1d9dde6ac7b4e72e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d620d4393b68c44c93b502a52ae3c82f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98504ac131f13b9f545645c52963007014", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982dabffa6de619320a4f5f7a2245cd409", "guid": "bfdfe7dc352907fc980b868725387e981ae19c6a0b14aec5c4ce0ee0e3b29f20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98629c5cf94897582ee645e8462d0b99aa", "guid": "bfdfe7dc352907fc980b868725387e98a9595c244750d819cf9b3084dec7e4ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98834d063c7b996806be486f48b306e39e", "guid": "bfdfe7dc352907fc980b868725387e9892c6f1c002cd06fb8a0332882eecdf0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988738b41d557b823032ab2e65ab9cb4cf", "guid": "bfdfe7dc352907fc980b868725387e982dd6b8cfc58bda45195f68f6ed20e0b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890dbe0150f1031506b67d97a4d54fe3f", "guid": "bfdfe7dc352907fc980b868725387e98da6dd571f55b54d53dcbf5966867ec2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a594ece03fb13e6fe826421fe63e20", "guid": "bfdfe7dc352907fc980b868725387e9838c49c72fb96d85c97c29fb792fa83f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b70e328a9c4be8204bb752c4903fd0", "guid": "bfdfe7dc352907fc980b868725387e9825e609ee72589fc0bd859e90e6699155", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a30b24874eeeffc67ccc0b39b11013a", "guid": "bfdfe7dc352907fc980b868725387e98d2fca4fd86d6496f7b73b5c772fdf532", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857839e3548112380129972711dd05477", "guid": "bfdfe7dc352907fc980b868725387e98d7222f94cc84efa43ac6719b68d64200", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c75e3968f3bf343085c137d88671f0c", "guid": "bfdfe7dc352907fc980b868725387e98e876b295d9246a40abd1878701eef332", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbb43ba505b4adb97221bfaa9ceaa59a", "guid": "bfdfe7dc352907fc980b868725387e98457bc83b75500073013966f36162dfdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be76e8478e0d0db32d1bab2652e75f26", "guid": "bfdfe7dc352907fc980b868725387e98850705253d8b0ced1b39ca35f17ad3f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efc1dd15a67c17433df24cbd65b3cdd0", "guid": "bfdfe7dc352907fc980b868725387e98fdfa3601693c7bb25f09e7f2c24d00e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845438d2aadca074b5a3d6b6e4857bf84", "guid": "bfdfe7dc352907fc980b868725387e988d10ea275c548471c82d7ee3ec3892a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984100de13b9062109a8ac4bb1206f4d15", "guid": "bfdfe7dc352907fc980b868725387e98833138fef30976a11c880818314fc46a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888a663de461c947774fbf552fc1b461e", "guid": "bfdfe7dc352907fc980b868725387e986efce9e13acd90348e500774b79d4386", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9ca12e7d3dc4edf01ada77283139db7", "guid": "bfdfe7dc352907fc980b868725387e98aa1c7645aceaa760139dad825de4b1a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f347709f54a11c43daf1568c088ab025", "guid": "bfdfe7dc352907fc980b868725387e98eb4c03b1b096ab2d59b9f5a4a1f27396", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cdd7ac20a78432eb66cd3c2bbf804f5", "guid": "bfdfe7dc352907fc980b868725387e98c50b8b791961642b3c2c635a01cf0280", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d04ca01ac46cd5a8360d0dbb2b0b06c1", "guid": "bfdfe7dc352907fc980b868725387e984a3a109984e05059171471253ac8c0cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f5ab97738e3261ff51508eab1116316", "guid": "bfdfe7dc352907fc980b868725387e98440cfee2b07644fb50fddc8f9569f1b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c1f9c69a3268b9665c9203b3e3fc82a", "guid": "bfdfe7dc352907fc980b868725387e98508bdcbd69e0a295dca7c67472a73b8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e836e1f21519e181d5b31dbec20613f7", "guid": "bfdfe7dc352907fc980b868725387e985e629290ade8dd8557d7529b08be25a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98789a8798f1171927d4bc5581ae49d47a", "guid": "bfdfe7dc352907fc980b868725387e9893b1bfbe78d66e8d30d7f822ed8adced", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827a290ec96223c5816b7afaff1a1aece", "guid": "bfdfe7dc352907fc980b868725387e98f690beacbf96f9411ae6265463969366", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6d787ff1fcb0bb0c5ed5b1ff7f6c174", "guid": "bfdfe7dc352907fc980b868725387e98e74e9f23110efbf7fb7c531042e5faf7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d87d43aa5c67fda33e9775ad9e6e7bd2", "guid": "bfdfe7dc352907fc980b868725387e981c5d105bb6ee49069c190842ddac68e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98200bc9a75c51d37f734f8140957f6ea8", "guid": "bfdfe7dc352907fc980b868725387e98dca8bbcde42035045e7d75d1f00a7fbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d8e07c9c140a0e4b7f15051cce366a", "guid": "bfdfe7dc352907fc980b868725387e984fe06938e7f920ef3ae3fe36813f08bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98399d2fe2dd522fb009dd46494f6d89e9", "guid": "bfdfe7dc352907fc980b868725387e98cf4daccc7023bff88e834cc93db64de8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9842e207a4fd8b5c895408202327373754", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f74eb7e50dcf6701be7bcb990fae34bf", "guid": "bfdfe7dc352907fc980b868725387e98bfc27b4323e20afc74e2b35b4b6ca523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880c7ab4a59a63315aa98820444aa6b6a", "guid": "bfdfe7dc352907fc980b868725387e986d6d6fe27975f1eaac82c7fa85d6eb23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dceda74913d17af109daf6617be283d7", "guid": "bfdfe7dc352907fc980b868725387e9830a9b460735b6d01eb5ada725230ca9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d064a9eed00be77060d0932135b9e7", "guid": "bfdfe7dc352907fc980b868725387e9847ca3467d135179238b75a5781e3994d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c3deb5b31acdc9d2fed281a54f5185a", "guid": "bfdfe7dc352907fc980b868725387e9818425ff199df22ea4c2f6dee50913bda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddce03616ef97ae630800702a2b47687", "guid": "bfdfe7dc352907fc980b868725387e983f3a9bbe432a66b9a274dd3ca4eddb94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829d8bb750236f55026f45327b4ea3aa1", "guid": "bfdfe7dc352907fc980b868725387e9890e23b040378559df0b47fb6396668bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5c522ec862013b9062b8dea802cecab", "guid": "bfdfe7dc352907fc980b868725387e9817dabbfd8fe6285514a56a22a28686c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833f0073558c2955a971bc2829f370490", "guid": "bfdfe7dc352907fc980b868725387e981a632152a13815efaa7073a99ce0626f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066862c565f166ccef711a8873ee8683", "guid": "bfdfe7dc352907fc980b868725387e98364a19a8ef3e4bcc78f309dccffd424e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc26a8ba2b21a125467f624449004a3e", "guid": "bfdfe7dc352907fc980b868725387e983c02d2cf3be714c4aa5a2523698a534c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b816749065b48722e57b81d8dcd6cb93", "guid": "bfdfe7dc352907fc980b868725387e9803cdc7c6338bfc8318cf81320a139f79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a37c16d35bc7802bbf8cfca2583ffe0e", "guid": "bfdfe7dc352907fc980b868725387e988328032e6d0bec4e400be4c4e780c61b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f038f7b48569365882b52b139017aa82", "guid": "bfdfe7dc352907fc980b868725387e982603b604526a9976fc7a81922f22f03e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb00404508338e943d3e3f6503b1f916", "guid": "bfdfe7dc352907fc980b868725387e989e380237cc7021739f7b4d2480b225d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6685553dc10ab0289aba29935ea606e", "guid": "bfdfe7dc352907fc980b868725387e98a2d403504c37111621865889faf67816"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c06200ccf55ff6945b37e6f3b67bdfbb", "guid": "bfdfe7dc352907fc980b868725387e98d93804772e63eb8d71fb5c9b5e198b5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b0ad57d46222181907d2df021d7fbb2", "guid": "bfdfe7dc352907fc980b868725387e988ff2829a463640ea587dc2836d772dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa303313c761a1fbf524e90a5030a928", "guid": "bfdfe7dc352907fc980b868725387e98d3cbd47bb36cf7ce6cd8620cf4948e95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd75d2bc067c0f0c8afed5247859731", "guid": "bfdfe7dc352907fc980b868725387e983731b560a4ca42cb0456f93d3b838b84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0b41d742e00917e03c23ffd3efdc3ec", "guid": "bfdfe7dc352907fc980b868725387e981266dba979ff55cd8059cd31b3608cad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0319438971dd5e4962a34cb66f08c76", "guid": "bfdfe7dc352907fc980b868725387e984520f5caf05bfb37a840865517d116ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29880c792589e196cf579a630968fa0", "guid": "bfdfe7dc352907fc980b868725387e984042838875dde39911f5f0a95bcdb6ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df3d7735d7abe3892e2bfdb56a21244c", "guid": "bfdfe7dc352907fc980b868725387e985587a6157ec57a4e315bc885cb59583b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e71d0ddf738ec33a64112a2910f2308", "guid": "bfdfe7dc352907fc980b868725387e98140ac18700f98415518d85c929e22ea0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d7bdcc29597d95fa0e8a80e3d30eaba", "guid": "bfdfe7dc352907fc980b868725387e9851721e78e36fbd55ad7055a70f584447"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98669f9e1afa793dfad4df25f5d5836a0c", "guid": "bfdfe7dc352907fc980b868725387e983a614dee22d1a210992fcd73c15d9ef8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf278b32feb8d489c57afe697d751b3", "guid": "bfdfe7dc352907fc980b868725387e986e11c14cac2ff2e10db016ace396041c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae9abf2df880889e068248d4e5d7c555", "guid": "bfdfe7dc352907fc980b868725387e98c10e5f840bf62c8730bd90cb6dcb9668"}], "guid": "bfdfe7dc352907fc980b868725387e985e821d478a328d006523b3baf8bd158c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98d38d707860e0ccfc8e52e073779e999e"}], "guid": "bfdfe7dc352907fc980b868725387e98819ddfec3cfde1e11e72a03425a4c5b3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982914a0667ca05766ae6733923632fdb7", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98052b148642965a4a65f04557e459a394", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}