{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bfe624193cb16a58c5f0e15d422cf449", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988e7c471806e85d90450352ca0e8635ae", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98428194e2cfa2c4909494881c678c0296", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c9f05831b38c390e67de5241642afa0d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98428194e2cfa2c4909494881c678c0296", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c577a3bc93c715e8301b76ac4afe83aa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a4ba9d68e68d982bc567e1f0061e70fc", "guid": "bfdfe7dc352907fc980b868725387e985045a99b6393db965e41403f79739bf1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98332c5dece6fbb1f58c9ed4d18699cf65", "guid": "bfdfe7dc352907fc980b868725387e984f8f438d19cd16e802875d96c66bcf37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835dc7574040e333eaad8af8057c74055", "guid": "bfdfe7dc352907fc980b868725387e980836302a9b4878f1aaeaa95c509cc510", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ede302402658532024d71cb5fb0c3cac", "guid": "bfdfe7dc352907fc980b868725387e986a663c5a7a42807dc9a32a28f47264a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0104e2016c4f9c91b5948e52e306388", "guid": "bfdfe7dc352907fc980b868725387e9808fd10a5685c5678bef9de28ee13bad7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d09a04ddfeb9fa90f79c47849086d7", "guid": "bfdfe7dc352907fc980b868725387e98c7099b223644055fce75db6ad5664cbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989466cda6c5e1f47df400577062ff8010", "guid": "bfdfe7dc352907fc980b868725387e9835f33532fa89d7de281270942673c710", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beadac23ab3060322517e44a00a377e0", "guid": "bfdfe7dc352907fc980b868725387e98281ec8c08eb544d7b2a2ced74ca4c022", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980897549df0e3aba4871bc39ae3a07125", "guid": "bfdfe7dc352907fc980b868725387e983fa5ef2ebea82ef0e00140da1b25b064", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb7039aa5d9cb624e42845db7bb65262", "guid": "bfdfe7dc352907fc980b868725387e98c7da935d7a254bdbf3cff6a557647df8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff655f79d6aa106180c0caa8553305b", "guid": "bfdfe7dc352907fc980b868725387e98b7d48620067aa175de3031263a53b334", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872986e1584fa29d0a5863ae3a7ef63a6", "guid": "bfdfe7dc352907fc980b868725387e98a93d0353f34082e39f060c5a4804489b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9e9bb4ff7c15c6ca0b039b9f8e9fd42", "guid": "bfdfe7dc352907fc980b868725387e983f7847cfe4b9d56611d34fcab9a71ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5b0949103ec32075420baf4831a005", "guid": "bfdfe7dc352907fc980b868725387e981d853a240aa95b51ca8eeae23d8da1ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6b429a79315ba6ad0abbacfecb410a5", "guid": "bfdfe7dc352907fc980b868725387e9862c4259e1a94f4eeb2216f1d7ab6def2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98432b307076e6c877fadc56df689609f0", "guid": "bfdfe7dc352907fc980b868725387e98faefc026d6734462f8f6d9a92b62f7d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a28caa957bd41b2597b24db2904af7a1", "guid": "bfdfe7dc352907fc980b868725387e98e974fdf1887a451d1e5e5d63982e2894", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3eef14b9f0d5e6250efb2ba482516a9", "guid": "bfdfe7dc352907fc980b868725387e983f97a7b9c2b2b23d083844aa0d74b8b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bd90d0c0fa0f94edf20151d8a7fef9a", "guid": "bfdfe7dc352907fc980b868725387e9825ac29e30627b745f0b5b78bd48e9c13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b946a68f6c3be39169bcb406d2dc0e65", "guid": "bfdfe7dc352907fc980b868725387e98ebdf89de0ab1eac5641f9d0728c0461e", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804fdcee80d737479c2c2aad20bbd9aed", "guid": "bfdfe7dc352907fc980b868725387e9809f4a11bfb186f50a30438f9e8b510af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccbe1941b314ec2dc71cb685716020a7", "guid": "bfdfe7dc352907fc980b868725387e98f06215b46442632478a5161b1b7ed9c8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982fe995e04162873ada7a24dde702ab13", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf4be2bbcf6131080140c17a60f0a688", "guid": "bfdfe7dc352907fc980b868725387e989bce6a153078887fb3982f1607de7b6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98327f81962564ca3a52cfa576eb9610c2", "guid": "bfdfe7dc352907fc980b868725387e9803d501f4685eccd5947627ee1183ead7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868e809fe1332225d6659eff849790ec5", "guid": "bfdfe7dc352907fc980b868725387e982559f3192c075bef7c91a97f7fd20905"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d18a6fbba4dbe99b2959202bee544cc", "guid": "bfdfe7dc352907fc980b868725387e9877a910f8dc8b1d24561e052cb2e34940"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcedba9745cde664ed377db408c366c7", "guid": "bfdfe7dc352907fc980b868725387e98f9703cc366f5408bb91c3aa6585d273c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d6bd7cd23093dacee4cff785510ce3a", "guid": "bfdfe7dc352907fc980b868725387e985f51b584beb940071281eea04e298894"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98038423c0f019155905f67af09ba8c1e2", "guid": "bfdfe7dc352907fc980b868725387e9868e5873e2c2c126314f52f628e32e0cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e92a2c82964f39626c94d78610aa82a", "guid": "bfdfe7dc352907fc980b868725387e98d242c59e8b52775ab238e3d8d4d9557a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838c04003fd8113a98005788ddb63b8b1", "guid": "bfdfe7dc352907fc980b868725387e981c44e45c646e8dda067b4e0129ae83eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d2d9154b74c3063fbee73719cd73a8", "guid": "bfdfe7dc352907fc980b868725387e98d414287b577f96cdfc37577807e5ec4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875c20ea838e112c18044f8c77879d175", "guid": "bfdfe7dc352907fc980b868725387e981d888baf56641ec309d574dbb9d87bda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e3a375d59c823aefa708333d4c0927", "guid": "bfdfe7dc352907fc980b868725387e98f105f5cfed51e443262e36d5f5c1313e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1efb25c12aa0bd211c5d3aab5517eb1", "guid": "bfdfe7dc352907fc980b868725387e981a004dfc0dae7bbbb24f801b55fe54b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98061b6b0329b66e11d49a64cc8dee3f1c", "guid": "bfdfe7dc352907fc980b868725387e98c27963b2bc818e66f7a2eedbbd9bcc9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836ec77e5958acfd874332fd55e6baff9", "guid": "bfdfe7dc352907fc980b868725387e9896e707d8f5b69365605dd893c1fe9c78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53063d00579b2fde689bb613f2e971e", "guid": "bfdfe7dc352907fc980b868725387e984894adc85e063cd737fe631f1c2af4f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988632f64351b314d11d5a086893e2e148", "guid": "bfdfe7dc352907fc980b868725387e981937c9c63ab6f278913ae54902924cc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f74a30e6077d4086ad5b27dd7034dbdd", "guid": "bfdfe7dc352907fc980b868725387e980c1fe23767432acd5d5b5165a9db3c51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842233550c7979582ea7e03f919e2c0e5", "guid": "bfdfe7dc352907fc980b868725387e9890074351c65a31a510e135e7472e6607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986315c5cecbf09deec235546c8a519afd", "guid": "bfdfe7dc352907fc980b868725387e98cd5453c7de9127924f178db8a669ae26"}], "guid": "bfdfe7dc352907fc980b868725387e9892a4e42dc9032978c0a483ac010e9d9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e986e2c126e058f493b7010a6c256143441"}], "guid": "bfdfe7dc352907fc980b868725387e98a8fbd32509797c1916dced6b50ce2c87", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ae1b8c480efcd8e871e4d52da38391cf", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98681ad28bb075169809447796439ec09a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}