{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2e647fc3a37ce0105d4a09bf29832c3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983db8f716620dd851c8383b0d982f5780", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9e035300c48fdcbc62697850e5a272e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98680583fee0a7a43c958ad0fb3808181f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9e035300c48fdcbc62697850e5a272e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981b704aa5e865434309859ff08a5378c3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986ce1bce4b2dc685628f570ad725239f0", "guid": "bfdfe7dc352907fc980b868725387e9810edad110b8205e8b3f3419e3151633a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986663ac3c649f66a441027477f6a07244", "guid": "bfdfe7dc352907fc980b868725387e98daa0d664effa1da8aa9c5dcf7a902606", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8e457911615c656d94f7fb746824214", "guid": "bfdfe7dc352907fc980b868725387e9832391c95c42d6e3016b706c078ee59c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2836e58894171267c4f197ebee5cb18", "guid": "bfdfe7dc352907fc980b868725387e9855eef84b964c7b6955d6f06c3f34990a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987202a5a407c5ec17e6766c28b4a02800", "guid": "bfdfe7dc352907fc980b868725387e9844476d7185ded412426434392f40bab5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a28224613c9d59294981b9f772b019d", "guid": "bfdfe7dc352907fc980b868725387e98f233f2794b0c2c1f6b39b0fde0b28d8c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98be82df05beb707561eafdc178d5b65cd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a2f7b6384c5f6450bffc5e96c7bc4931", "guid": "bfdfe7dc352907fc980b868725387e98666d95f6a984b67fb60fdee138d7c20a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba4010bab4d6f2a5e3330e967ba9e34", "guid": "bfdfe7dc352907fc980b868725387e98f24fb09467b52caafdb447de1418b4d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987354bd735f11068b93486f7729e3e3ae", "guid": "bfdfe7dc352907fc980b868725387e98951f8edb87cf0455a8b62740e05f7c1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984419dd5d6fbf9bd78ba59b7e47dee020", "guid": "bfdfe7dc352907fc980b868725387e98586c34c238f35d78b919041d3cdd131f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825f9f730c1aa308ac0cd3de7d3fb844c", "guid": "bfdfe7dc352907fc980b868725387e98606b82167c24d5aebbe8378f830dd421"}], "guid": "bfdfe7dc352907fc980b868725387e98ff2558e63f444a82f15551b4036fdd4c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98a8fe85d0bf9ab2d3900815a2c8841d43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e98cef0f12a0eefa47e4c8be95031bb6aeb"}], "guid": "bfdfe7dc352907fc980b868725387e9814d4d00f47c97e349a41cb4535a7a50f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f4d5f5fabc9a9e5fe44971a718232ba0", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e989a20195a56ffa27439787acd56315e62", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}