{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f2778b6eb7fbfbfd307691ea7aa087b8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988863404c53569f8a318ef6a76cb04c91", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc12f08eede6fcd5257c371bd39cdb1d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9838730d9e896b817a65e3aaba4bf0948a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc12f08eede6fcd5257c371bd39cdb1d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f52da678da192a5d4e990701c85c91cc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a59ae2c0b512aaa812a985b073af53c0", "guid": "bfdfe7dc352907fc980b868725387e9812ff73b79a532445f82a69d0296fcefb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b52a2f504654125ed48d8b4bcb3e84e", "guid": "bfdfe7dc352907fc980b868725387e98d3bdc4a04740dd573726cf41ef0d1ccf", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c2770143dfab9b2898fb3381093a05ac", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fcf736d909fed436231bebed2b27cc2e", "guid": "bfdfe7dc352907fc980b868725387e98d79503999dec45e5fe2fd77c6fc2a4d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98291dd1561434d8830f0b4255933893ea", "guid": "bfdfe7dc352907fc980b868725387e98a588b78c431ded6300a3e957ab268b5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b55ec89875ff24a8bc78da62f94afb8f", "guid": "bfdfe7dc352907fc980b868725387e985859d962e46536836c16c159a8df9289"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd52298f4db44b00fd8edf11b4608f0", "guid": "bfdfe7dc352907fc980b868725387e98e8153aaadc5b2763bc90a9fdc8f08253"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b5088d2542cbf8d51a1dae052644d36", "guid": "bfdfe7dc352907fc980b868725387e984be0c5e5dc9ae49d77fa35047afbc932"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dfd804d8b5aa4faa75614f15f295e75", "guid": "bfdfe7dc352907fc980b868725387e98280e85a21704876ae5cbe8c991ae46f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0c6d7dac698cf8a233fcef63bf196ba", "guid": "bfdfe7dc352907fc980b868725387e984fb22d12ab2bca4f18079d5db5dfb011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caa7143309ceced1c55c945cd4d8ed8f", "guid": "bfdfe7dc352907fc980b868725387e988c019808ebd302974cfb89ba95d1c7db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b13b91319f1ec08a9b1bdd0cb5596289", "guid": "bfdfe7dc352907fc980b868725387e9801f3a2672da8e2dfa75b98e3ef0f37a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aee63a71c2bd591cd61c67415f44f1e", "guid": "bfdfe7dc352907fc980b868725387e980dadfa10ec0d716c8bc67c287879b53b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a7a78062442f31ce555da67ffa251a", "guid": "bfdfe7dc352907fc980b868725387e988e1c75f242a72b7242d0ef1738448a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c331837b6f34106a043d12e3c45063a0", "guid": "bfdfe7dc352907fc980b868725387e985c4583d8f34fbf795f30cf58f644a638"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889f0d0e5b174f0d884af56eb485fd048", "guid": "bfdfe7dc352907fc980b868725387e9840762a47f5d0c16022d35123e77b2975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986014a411d4db06499eb6d2a32102a520", "guid": "bfdfe7dc352907fc980b868725387e98cb77681293b534b73c94c78ab89a38f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984434c7c71b6197dadc5be6f279b4ab14", "guid": "bfdfe7dc352907fc980b868725387e98da2e1955412092d8a19301093ef0491c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828252d6e3857737740434f52f6ed0618", "guid": "bfdfe7dc352907fc980b868725387e983874ff4a1a3ba1881f1944cf22363d78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b705376b5b05830badf35ce05f42c0b", "guid": "bfdfe7dc352907fc980b868725387e98f2184062be99570059a5ef41199d21d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98300ab9d29ce6761bf9cf0ef9284b29ef", "guid": "bfdfe7dc352907fc980b868725387e98e3879530b262536be8f34e7209cb5fa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820d122e23f3f0edaf9122839a006a4bb", "guid": "bfdfe7dc352907fc980b868725387e984b92cddab17e46ae27b0c5b01d964bcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6897d43f1ba41c1ff2ef997b28096f", "guid": "bfdfe7dc352907fc980b868725387e987d44979b77ecc8e9f111a2e51c1a6a33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cda2ede8de163a030bb0b4593a917532", "guid": "bfdfe7dc352907fc980b868725387e983266ad26bfbf09f90ecfbb112d6bb0c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8527db5e57c8d9fa974a08848713195", "guid": "bfdfe7dc352907fc980b868725387e9805ef39ce0147b9a42ae3a827c67fe4d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddec1f8ed5fe6934b7a483b8a08f3f80", "guid": "bfdfe7dc352907fc980b868725387e98840535a7dca27d48035430313d17c7f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821a66b146f29dc3cfd39703cccaa98de", "guid": "bfdfe7dc352907fc980b868725387e985cc0c758847edfeaa196b33334170ad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98617cc92ba49e340e9a17eff7145cab8a", "guid": "bfdfe7dc352907fc980b868725387e98b1e9304e58231dccc0c08ff76f375f49"}], "guid": "bfdfe7dc352907fc980b868725387e98708b2f3fa9e2c16901bb62d8787d63c1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98ad2395bbe935d2c3ff79656d2b2c1521"}], "guid": "bfdfe7dc352907fc980b868725387e98dd6a4cb9d797293a21acc432407b551f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985e87dd04033931371f2a205f6c3cf6c6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}