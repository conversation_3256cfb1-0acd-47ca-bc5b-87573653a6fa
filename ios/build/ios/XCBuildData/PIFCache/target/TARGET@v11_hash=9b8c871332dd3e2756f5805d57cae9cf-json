{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98472ec9eb5e9489e25d385656bd7b6b7d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986fd1e6d701cee2019c4ffcc8be24662e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981415f48b2f18b7cc63bc992292780530", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fbbcd9f52ffc8d7270fd0d309918d48", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981415f48b2f18b7cc63bc992292780530", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f18034db5e36d2954d13efe24565cae", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9879e1d6f76f2f17d28d856145450907cb", "guid": "bfdfe7dc352907fc980b868725387e9871d91a42be601b71d307300b8355ee1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab4789f0fc92667b8fc58e3205c0c4c2", "guid": "bfdfe7dc352907fc980b868725387e983b665e94a4cfaa5b6a0aca002e7e59fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c0304bf1ccec0b4f3e03514a3e06788", "guid": "bfdfe7dc352907fc980b868725387e9806e4e0f07e830dcf1c064d4fde7372c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc26bd364e71e0abc5d311e3a5d45e64", "guid": "bfdfe7dc352907fc980b868725387e98396b98354e97c6b1358117f08a4e306a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827932fb6145cf02ee2839ae91dbd32dc", "guid": "bfdfe7dc352907fc980b868725387e987ac25a11fa40f178db5d50bff061db77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985345c37b772f13d082d77085610c0685", "guid": "bfdfe7dc352907fc980b868725387e98f3b1973278086302771042df4e00ea41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6051899515427edff2f6dbb2a5b54cc", "guid": "bfdfe7dc352907fc980b868725387e98756f5b397ad053077de8f46d7da8fab2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8b4e9c1d69a1efc4f329192f76cfb06", "guid": "bfdfe7dc352907fc980b868725387e98b1c7b574f15f7fc9aa11534e7d2b7562", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca11bb50cc44b30e7a4cec5bb82315ce", "guid": "bfdfe7dc352907fc980b868725387e9834540f115b38beb81f79aaec362acee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dde42a2db608f3229e0eb5bd02c6fad", "guid": "bfdfe7dc352907fc980b868725387e987738e43885705e2ca3d3f952a852d1e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800953821baa9612ab2214d98dc81a9a4", "guid": "bfdfe7dc352907fc980b868725387e98039ec9cf2443d2dfcaa6bfd00745a082", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bbc2831bb292ac0c8a25a7c937d7edd", "guid": "bfdfe7dc352907fc980b868725387e980b651aa249a332add6784e1a51326269", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f6b7f9e892c6cc145a12fe947e07a9d", "guid": "bfdfe7dc352907fc980b868725387e988badb0d31d2706b6e4f9747d22135a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d3b0a7f02fd73eadaf0107464f1b7d4", "guid": "bfdfe7dc352907fc980b868725387e983155fd9f6a3b8bca94c3c79ab7d6f172", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811916a41e85b9fb2a4d89f65aeedc8c8", "guid": "bfdfe7dc352907fc980b868725387e98900d260dc13755009dc5e9a87b094ce7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec0847dc468be83464ddc1fc3eff7c7d", "guid": "bfdfe7dc352907fc980b868725387e9828b6a38764c56d071ba48fdbb255f533"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbf7c6305fa35ad2af58a000a8c89833", "guid": "bfdfe7dc352907fc980b868725387e98f05d93bb93fdddbb0e4e7929359c57b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98768488c58a80284a95de993ccba734ab", "guid": "bfdfe7dc352907fc980b868725387e983a4581eb617a5ad7d83892d698d594b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831777c6741ed343915593045b029ada4", "guid": "bfdfe7dc352907fc980b868725387e9849ccab35060d3b04350d5b2ec96a6be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae861eb96f2f8d032225458cea58f25", "guid": "bfdfe7dc352907fc980b868725387e98ee4dfe3e5170a4d901e0ea6c5751579f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ff836324a89c45b9ac9e4e3156b8e95", "guid": "bfdfe7dc352907fc980b868725387e9885d6d8d78881bf3885d75b32e70b1277"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2b0c78eb7d5296734b8e9164d43bf8f", "guid": "bfdfe7dc352907fc980b868725387e98764cfede42317a5d2c6a5172733c7e97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818102658f299a5e76cca613abe6a1d35", "guid": "bfdfe7dc352907fc980b868725387e98ce2868d00d05d2d2211051232eb2df1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b2320424a2b304eb38b73e5692e9360", "guid": "bfdfe7dc352907fc980b868725387e98cd6526b0c21c31085bd5878a26ea1a52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987be3a7b6a20d7d2a589244bdcc0bc51d", "guid": "bfdfe7dc352907fc980b868725387e9889255affc0e671e70bb08e4e4f3f6e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e18613c57699fa927abfec8837b713df", "guid": "bfdfe7dc352907fc980b868725387e9815dca42c43fccc27a7b13ec4db07705a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e578e4355e4a545ebdb45771828218fd", "guid": "bfdfe7dc352907fc980b868725387e9879c2596fd3a27cc64d264f803ac04a3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831b72112c9f73e2172ab78b59652f426", "guid": "bfdfe7dc352907fc980b868725387e98f42c7113b7322f4073dcac9b6e96d7d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985607c3624e42a7a78b0cc79bad5bb8c2", "guid": "bfdfe7dc352907fc980b868725387e9812f35353a4585fb67a3e91a122e4961f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987262066f6f61413a7c7e21b9ea9f8d18", "guid": "bfdfe7dc352907fc980b868725387e98ede8ff609a807a6a6f7f3501653a1530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98501d122e0eae17eb2a63eb4f59b2c20a", "guid": "bfdfe7dc352907fc980b868725387e981f72ce5bfe0f0920fcf3a20705e8519e"}], "guid": "bfdfe7dc352907fc980b868725387e989a8bdd7db64c936feeb9fdccde76a124", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cdd0b61dd1801e1c457569ec6b047c16", "guid": "bfdfe7dc352907fc980b868725387e98f2d8328aa3716bcf58165cb6aabdd9eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988938589ec86be53b02a73ae8cab1ed9d", "guid": "bfdfe7dc352907fc980b868725387e980a6842fc579249ca27117522fcb32520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b861f896bd01f19a2ba0faff3fd6687", "guid": "bfdfe7dc352907fc980b868725387e98c5bcc8cfdebaa2ea4c37d0e8cf801bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981656a01dd62d914896398fabac4253dc", "guid": "bfdfe7dc352907fc980b868725387e980d69a15c5090c41cfe80991e6b959490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98893696bde4d1b1e5663daa5666a6d889", "guid": "bfdfe7dc352907fc980b868725387e987c40da3cb9a8bee11591bb9fcfbf9747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e95c77236031fedf2779213cee559214", "guid": "bfdfe7dc352907fc980b868725387e981e211ce9b5a82c38a89df7432af5ae11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e49e48eb07a330a8b89a99c875c001", "guid": "bfdfe7dc352907fc980b868725387e983e0ead0ddd47065d3ee3316a6a456558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849e3559183ddc3750a194417ca0649f9", "guid": "bfdfe7dc352907fc980b868725387e98a10b53c251fead24e2bc80ffabaca7e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984458aa6bced187618d30fd5bf5d42989", "guid": "bfdfe7dc352907fc980b868725387e98908b25b6de0d398e2db2ab88c7de1b95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b05ee086077b5a8487a935309fc8f0e8", "guid": "bfdfe7dc352907fc980b868725387e981532a1ac14391b4b492680fe682b0957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814b3ff76ec370a6aafa141471f646e23", "guid": "bfdfe7dc352907fc980b868725387e9818ac9bb25ea11222d5e6d5d5016eeec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e6e15190f6bc2ea52d906faff136c2a", "guid": "bfdfe7dc352907fc980b868725387e98aace04b8dc34a35748654fa1fa5d33a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e053d6db3f89ccf3a53fb3a9bb1e441", "guid": "bfdfe7dc352907fc980b868725387e980435a41cdea476bf7b11f4bfcd411ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98873b15a691bd5c23a40784953a85bd62", "guid": "bfdfe7dc352907fc980b868725387e984695a5858a5c3c95172aebe1fe52346f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98781013cdb42d14e13eedeab80e744e2b", "guid": "bfdfe7dc352907fc980b868725387e98fad322e08034ceb08d597725afeba077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aecf5f3cb0da9bea9ba4670675ad248", "guid": "bfdfe7dc352907fc980b868725387e98b739033624abbc444cdbd9c09bdf3e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98446108e49b6f2a9360baa8e06afa337a", "guid": "bfdfe7dc352907fc980b868725387e9840a011651d32a8a7d65ff37efb37da99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be6e4f6a6a4147a7faf2dfac7c948771", "guid": "bfdfe7dc352907fc980b868725387e98a2eb62cb148ada087de53721959663a9"}], "guid": "bfdfe7dc352907fc980b868725387e980f6ad4bb86e2126a469e3678db6ab533", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e989ad225b4241890eee92fd5876f740106"}], "guid": "bfdfe7dc352907fc980b868725387e980c126ec4e291ef8d9fbbbb38989292f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9808b47889de662cd4865776eda02c0c2b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98ecaebc2b66f6675fbaa388164aa6c8dd", "name": "FirebaseAppCheck", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9857de7acecfe5aa305e96dd28add8de37", "name": "FirebaseAppCheck.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}