{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986de099d3fd6d4885ad849efb392ee4b3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9813ffe698d2c5b1da6dc7765d18f67612", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98434b94913997156aecc2f155e9660e8c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cdce48e6c4a902cfbcbc6ea20776cf7c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98434b94913997156aecc2f155e9660e8c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9844d1393adc38c8edcc677475d9adc0f2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98435bc5b4011b9c78961e81e9da2d1eec", "guid": "bfdfe7dc352907fc980b868725387e982f57bf49987b942c005e8f4d9cb1c6fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e28f862ac26928aab9a4b860a267fb6", "guid": "bfdfe7dc352907fc980b868725387e98f2c3c6b5a141fb7f8b6f91272e19530d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835c41812ad184d68b79999d9bf33ce34", "guid": "bfdfe7dc352907fc980b868725387e98e63d8f545d8a391a3798019747d1e084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1c23dd70253302d4d7e3e2b7b0793e", "guid": "bfdfe7dc352907fc980b868725387e988cf11a4149674ced0cd0922835ddaace"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855296c83c40b210ad769ac2b1dcc2718", "guid": "bfdfe7dc352907fc980b868725387e985a3c7f92ce6349aca3d976d80c0c7c1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51150058e07c1cd80a2f64cb8dcaccb", "guid": "bfdfe7dc352907fc980b868725387e9825da0fdbf5040cc497d4a08c2e36eb11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841557d9b50aa14bcba69e594e3157c51", "guid": "bfdfe7dc352907fc980b868725387e98e2fc9a1ae2d736509178b1071d5db47c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebea3186dc7ee562a10b797989ef11db", "guid": "bfdfe7dc352907fc980b868725387e98ff258c1cb6d5044ab37a43e3e343c1d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e975d824c9ff9543710df240ec4efb7", "guid": "bfdfe7dc352907fc980b868725387e98427f7160815bcc82fe1dc78c38dc07c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1dce30b47e240e16fd35cb9d41d8b85", "guid": "bfdfe7dc352907fc980b868725387e983c713436993130f1885596b79d5ea79a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988974453f688995690ee486ccd09a20aa", "guid": "bfdfe7dc352907fc980b868725387e986ed23b6b404029b15e422c2765dfe5d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988597443188dcc7034e9f19e171c6e1fd", "guid": "bfdfe7dc352907fc980b868725387e98f711a9c7f398d556af0abe6a569815b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b3618da2d200fdfa0bee9e4f4bea26", "guid": "bfdfe7dc352907fc980b868725387e98999a1245a811abb869f46fd2a0330eab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834009630bd9c150001ef9842d127adb4", "guid": "bfdfe7dc352907fc980b868725387e98f492a5c1c2a452b9dd6442b209f7a194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c51770fee9a8402a3258a03ea2a7b93", "guid": "bfdfe7dc352907fc980b868725387e98d0d6ec96843c6123690636b1ad7d40de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2ada19410a331e742f85a506a6d6e15", "guid": "bfdfe7dc352907fc980b868725387e989992f2ad168db011ec8615f690c82ec2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9148864d400ca26324bb38dacfce228", "guid": "bfdfe7dc352907fc980b868725387e98b8e7ad059d40332f3a827be346a58edb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3c6859bc14b557bfac4b2561ec054b4", "guid": "bfdfe7dc352907fc980b868725387e983d78560ef108e20c0a12bf84982bc695"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98632d2ae85d325d19acec7411acdf3a46", "guid": "bfdfe7dc352907fc980b868725387e986102c7cd2ab1775df785534e770650df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f1faff8f63a273f1708fae7f06ea2ee", "guid": "bfdfe7dc352907fc980b868725387e98b011a6045c94ce9f19baa56ddc86eeca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984615df236f36d39ed9279fda203bf78d", "guid": "bfdfe7dc352907fc980b868725387e984261c5a4971a1bfa99caf287baaafdf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98417985604813d140499f1f1fdb794d47", "guid": "bfdfe7dc352907fc980b868725387e98b588f83e058b76c1468a07574bbce33e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98419ea3d0663e1e5195aef699c6429990", "guid": "bfdfe7dc352907fc980b868725387e9803b66bbabde81116b9fe54a326d61cfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98797522e8edc579f1f23184152172349c", "guid": "bfdfe7dc352907fc980b868725387e98d0f3e53e69429fd6334c753f13b4b401"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889fb63b08a67f8f2c40f8e46dae293dd", "guid": "bfdfe7dc352907fc980b868725387e983d16e01bd0d5d3af935fd83cbab0b6bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2be9cab544a33ae1df5a6aa0e61b28e", "guid": "bfdfe7dc352907fc980b868725387e986c02ffd475ba43accf4417dd724b8034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bebfb49f9020e9f3df2e87394dcf67be", "guid": "bfdfe7dc352907fc980b868725387e984f31f7ee877420333efc41e698099b60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98debc7f6adcb74d7b176545577a179a01", "guid": "bfdfe7dc352907fc980b868725387e98fade31c46327fda05de6900233c2cd91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d1dc7521e92c11ca157a5478b15fa2d", "guid": "bfdfe7dc352907fc980b868725387e9825a19c908c7a339638270579ba24b105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983037f56b405daccb9295f0ae54b8472c", "guid": "bfdfe7dc352907fc980b868725387e98977cd6bbfa1378bf82df5b394325d0d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987672b914bfd727e59dfaa1ab78bc2bd1", "guid": "bfdfe7dc352907fc980b868725387e9884a0d6ad5f6d1c4596f0d65a8fe4c7b7"}], "guid": "bfdfe7dc352907fc980b868725387e98b68f257182dc3d9dd89a9963e063e067", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9821ad42ddaf3a0e6a1c3ea01db0e8ba6d", "guid": "bfdfe7dc352907fc980b868725387e98a6c09cd922c0d25f3426550d6315a826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f1e2ce4a386d7c645e2a295be28167b", "guid": "bfdfe7dc352907fc980b868725387e98f9a6f32c2e895c0c2437770671bfec3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98894c6cea6a03edc610efe884e3349d3f", "guid": "bfdfe7dc352907fc980b868725387e98729d870d46cd42731abe65cb43c66c89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac999a541cf3f73d66fb6ea2ce5b5c75", "guid": "bfdfe7dc352907fc980b868725387e98b38ac1e2a1f9304b44b00395f48b8b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee9584c18d758e3284bc69b967254ec6", "guid": "bfdfe7dc352907fc980b868725387e98f61c5b7c42ffb379749b7373792e7122"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ef4e8fd4131668a99a3165cc508a6c0", "guid": "bfdfe7dc352907fc980b868725387e985c7f8d69b37d9a280a82ae032d00a5dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f072a80ee26f4b7c541276a09a35fa4", "guid": "bfdfe7dc352907fc980b868725387e9856c34d3e5bf8e8900da90fcb26dbfbc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e44941aa5e7e3d80533b5fa383e48a3", "guid": "bfdfe7dc352907fc980b868725387e98b6721ffd6c39c447bfb61bb88690f1bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850bd5a1c7c5926a93aaa3039b8985fb5", "guid": "bfdfe7dc352907fc980b868725387e98d04338a9459ed6e3223ed31669d02ea5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983847372971ad5fbef0e6d8efa82c643f", "guid": "bfdfe7dc352907fc980b868725387e98baa5b45549c8b8b0047a656fd3a0df4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2f9537ac0c2fce853805936d67e03b9", "guid": "bfdfe7dc352907fc980b868725387e98a8086ffd7355dde536a407a1ab092d57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f86771198c34973393ce5a003ff60cbe", "guid": "bfdfe7dc352907fc980b868725387e98aa5f9f80a51f1fe193efcca9c616c75a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c115886a4b8007813d19b11eb50bea80", "guid": "bfdfe7dc352907fc980b868725387e98d93c31fe49f7d8de1a07bc295f67cd8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982318053648e3c7cac6f909025a8fb7b6", "guid": "bfdfe7dc352907fc980b868725387e98f1562e522b14f812b4ece1e0d8ec9cab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98244b8dba0cf8887dd22a09a2de0f9ff6", "guid": "bfdfe7dc352907fc980b868725387e981355f252be070741659dc30c88b319dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193b72d0fa6b4a8f80003ee0cb8e2abb", "guid": "bfdfe7dc352907fc980b868725387e989748ec30d65247517c99e42f74991844"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a40721d9b39a33d01a93f4ec64c878b", "guid": "bfdfe7dc352907fc980b868725387e987ff461600075748a381d1ceed609fae5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f09264436ca008e457e30d4e15eb41c7", "guid": "bfdfe7dc352907fc980b868725387e9832e2246b9e19a37434cf112b4bfde5fa"}], "guid": "bfdfe7dc352907fc980b868725387e98330b18090a43c5b379a7cc7adc7eb6b5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e988e49ce1fb396b13e76b280232fffc4f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e980e08eb676453fc84a8e3357354999253"}], "guid": "bfdfe7dc352907fc980b868725387e98a5950ec54e6a21509d3bfd1c9928c8ac", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c703c6e58d554254fcc977ce12a2f1f3", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98550405b4e74e077614205f647e556b97", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}