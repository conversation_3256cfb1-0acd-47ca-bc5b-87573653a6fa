{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884ea49d8022e3ba09dbd51dabbf1fe9f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e3c6a5cf4d508ed4f31cccf42d6bca06", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98320fb9d681e7df6b7d8d1c0c8108e8f8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98037facaf45e252c4316867649e2d36d8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98320fb9d681e7df6b7d8d1c0c8108e8f8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98972a8fa26fd78fc1a563f203f0ff94ec", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98421b55aa095906679b6a967646e8fff3", "guid": "bfdfe7dc352907fc980b868725387e98ca4349e640cce4e4a73d229a091f15fc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9869c53006c3a116312cab282b0f15dce3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bb064cf1d99cd3e7617af77b4fac1817", "guid": "bfdfe7dc352907fc980b868725387e98a391ad0904149ecb139891c7b54c883b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982645ea16621657b2598a24dc751b03da", "guid": "bfdfe7dc352907fc980b868725387e98a5045577cad2dfc78169bcc2ae3ef221"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf5ccd591c5abfeae43a5897f262f44b", "guid": "bfdfe7dc352907fc980b868725387e98b6ba7d40dd4d3a4a5470c9e1b78dd1ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf2f22844619cf6cdfd71f6690c34ae2", "guid": "bfdfe7dc352907fc980b868725387e981673e7da056cee7ac6f00c6d0222d312"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980840e12650db762fbcccbc7e3d662cab", "guid": "bfdfe7dc352907fc980b868725387e98ba7eead34f813d5bb13637b10784c9f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2083f62b31e41c0ccc8cc66b9a780ab", "guid": "bfdfe7dc352907fc980b868725387e9897ad3f19570598ed2249ac1e0021f828"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e4dc70bd000a37275f68577d206a1a0", "guid": "bfdfe7dc352907fc980b868725387e98da3c219bfcf4c8a381f54924f0a369c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98721a7b75cb6767eca17d7cc2e796c9a5", "guid": "bfdfe7dc352907fc980b868725387e9883405822b49195e552109574329dde0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b085edaa82c168b601da24a76bde603", "guid": "bfdfe7dc352907fc980b868725387e98326ab7d337115da956aca099d60abcdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827b447ac7c752d123ff2e3fa1cecabcc", "guid": "bfdfe7dc352907fc980b868725387e982f70d7c89ba9331a8669a37d9fa44bd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98031c7f5a38ef829e8f527474017eb0c2", "guid": "bfdfe7dc352907fc980b868725387e985ceca9f4f69dcba15119d1745032cd20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f76db9a5a68d318f1e1dc8b9c0766df", "guid": "bfdfe7dc352907fc980b868725387e98d860666909bdeee6981e17838e906555"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9ef4a064cf96917ff071d916f621eb4", "guid": "bfdfe7dc352907fc980b868725387e9819c3fa0c63666b7930713c7d2ee30756"}], "guid": "bfdfe7dc352907fc980b868725387e9821467a0de881eb3decc3c89a2d5ef587", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9879c03a4749c2c0da8cd9bf6e0bf1a1f5"}], "guid": "bfdfe7dc352907fc980b868725387e9878fa3101cde2a85991c6faae0a5ed59a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981ef265777febec6ed69fdc733ed8716e", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98b53b7f85a0db275c6405040783b8a59a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}