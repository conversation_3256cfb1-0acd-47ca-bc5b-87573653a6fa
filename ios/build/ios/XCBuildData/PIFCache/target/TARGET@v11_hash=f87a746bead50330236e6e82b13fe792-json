{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bbcf30b8b7af098b431e5e53809ac3b0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892005d2921633428b21c2808d64855e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3d092b6ca5b92fcee5a7b0d23bdfc97", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98997e66c0e98e1db88e18272b5fafa165", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3d092b6ca5b92fcee5a7b0d23bdfc97", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8402fb7425dde778441c192bedf25e0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9840ad7a990682c26440e92d2c437ab0f1", "guid": "bfdfe7dc352907fc980b868725387e98840f2cb5a8bb568f85eda089854c36f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a62539f31e16a978b9addcd31d1b9f9", "guid": "bfdfe7dc352907fc980b868725387e98a328d6dd07fcbe1ad63fee8751f10805", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823ff026c99ebef33b9af0d89d9f25f44", "guid": "bfdfe7dc352907fc980b868725387e980f81fbae340faede937c11b0eff67dc0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803dda4012975038040e983ce6701b626", "guid": "bfdfe7dc352907fc980b868725387e9899225311e80daafbb859b37066d6bf72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be10fd2f32a8a436f4818e91029de44", "guid": "bfdfe7dc352907fc980b868725387e98a1671e45b43a06a62971c140ea7920e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e0134673d4ce176d072e182b99bf025", "guid": "bfdfe7dc352907fc980b868725387e985fd51099f923de7d0d2206f0cea22d01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98165eca938bdc4c40726634b1e7e5f4ae", "guid": "bfdfe7dc352907fc980b868725387e9804bea153a07938276893392056d81632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98943cb3e5870845d81bcff43b4a6ee2ea", "guid": "bfdfe7dc352907fc980b868725387e9898994fbe985597d58ce2451daa176ba1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c65fa7356828767f0930563aec46c258", "guid": "bfdfe7dc352907fc980b868725387e9840724d12b97878b20e7044c7b9303bd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dff28531fa11ba2ce53b136e25908a5", "guid": "bfdfe7dc352907fc980b868725387e9805a932bf05a95683fbcd949a9f8f90a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bb63fa754d3e38ebaad707541185b08", "guid": "bfdfe7dc352907fc980b868725387e9892a7e07d664ced969cef7900c3855ad8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98951cac80d9f9e8f372c6a2f2bb133b47", "guid": "bfdfe7dc352907fc980b868725387e982d2dd945da02598d3e5d05ddf8a79b9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497c09b92f7504316cbcd5153de8f790", "guid": "bfdfe7dc352907fc980b868725387e98410534e5af45d620a747f5cfbd78ddcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5977aa944c767c50d751e712b0e28b4", "guid": "bfdfe7dc352907fc980b868725387e98cccb883363b12deaee284c77a174821f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98709a4ba4d11b5df355eded857df06e02", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d87e389dfe5c258a1c751ffa5519c38c", "guid": "bfdfe7dc352907fc980b868725387e9888d9c8fcf6fa1bbd0fef5d42c64ab854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d06ed77beaf631a9ec142603635ce3e", "guid": "bfdfe7dc352907fc980b868725387e9893559c9b6a9d108fd5f28bebcb06204c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df84a8a5a14df96ef0d2ca8ad484e568", "guid": "bfdfe7dc352907fc980b868725387e98d98117b20d5ce553ef8b79f1d3d9b9a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb1b05e27733861893d592be0d737ade", "guid": "bfdfe7dc352907fc980b868725387e984b8dcf6b013bd8bb403de25000d39e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae25b4d4e71ee12d8584cae5799a3da7", "guid": "bfdfe7dc352907fc980b868725387e98c8b9c682f92c40b011560499256340ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864fc6b40ffb60a3dced0c8b168685369", "guid": "bfdfe7dc352907fc980b868725387e98ced936ba76e2ceaabb9d809dd29f58eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98091b4584b65d745ece08b2d178c2f82b", "guid": "bfdfe7dc352907fc980b868725387e983d98cbd1a0f285a7c3f7df3c12e386a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbd9e688588d8c994b4761c91919dbe4", "guid": "bfdfe7dc352907fc980b868725387e98fdce5d50b66744183db4cba2c9343367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d31ad6fa2540b3fa31dc25f8e5cc55ce", "guid": "bfdfe7dc352907fc980b868725387e98bfac42df031233ddc399ed0b5dec74d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838cdc2de53a1890ecc8d63e92842627a", "guid": "bfdfe7dc352907fc980b868725387e98c8f335891267a93027bba19c627a7ddd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e3e628cce38c4cd289800e8d847ac2", "guid": "bfdfe7dc352907fc980b868725387e986b7c2f8760ffaf440ea375915ddbb9ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980664473d311617029c5e455ed70e5fe2", "guid": "bfdfe7dc352907fc980b868725387e980132291d93c9059b576e31d0f9323ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb0740f61f8fbb6ac5b37bf73b700fd5", "guid": "bfdfe7dc352907fc980b868725387e98e3104eee7e8adc0dd65862a03aeda603"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98701daca88b81e9d8ff89c92063a35b5b", "guid": "bfdfe7dc352907fc980b868725387e98ade4f9157267d54cf1771516fd6cff2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804ce68666d6c2bc2ddfbc3ee3f552c72", "guid": "bfdfe7dc352907fc980b868725387e984115d6702d8d74534eb92d1d1bafeecc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2d19e683ddca701862694d5a4f191ac", "guid": "bfdfe7dc352907fc980b868725387e98d8ee9cfbb79d7afccc621e5311751121"}], "guid": "bfdfe7dc352907fc980b868725387e986ab5620dc55715e8e8f6fcd49a2ef83a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e984fe03a62b0f3efa2b48d3a3971811d17"}], "guid": "bfdfe7dc352907fc980b868725387e98319a14c9389993bee5d96d155795db97", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987cd2512697fd8475ad5d01e85d7d220e", "targetReference": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9"}], "guid": "bfdfe7dc352907fc980b868725387e9868992c43aa4117f939ab7493574523f6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982a930221dc4925ae3ad26ac05af9179d", "name": "in_app_purchase_storekit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b48307e2bc58dc7155fc2e80bc197afb", "name": "in_app_purchase_storekit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}