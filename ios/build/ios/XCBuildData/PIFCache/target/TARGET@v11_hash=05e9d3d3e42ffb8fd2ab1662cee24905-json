{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98946b1f46c6c90af6570640138163a62b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ce6cfd4b366d3cf0382f5ad9412ecea4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98329cc58f7bdd1bdbd060e44a82fe1b90", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c38fa2841f60741fc8e078f0f2267ef4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98329cc58f7bdd1bdbd060e44a82fe1b90", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e23c8734e49524d23f8f2bad5e5dbddf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98109a0a32aa26115b93b9f63246fdf9c1", "guid": "bfdfe7dc352907fc980b868725387e98438c821f9e78118fbccdba46245c5baf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b18675228259b45c3f33428af44cc72", "guid": "bfdfe7dc352907fc980b868725387e98078fa32f4dada31978a605a7e0a55fc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de2afdd5b787a10ce70b5f66ff610cb6", "guid": "bfdfe7dc352907fc980b868725387e98f469045fa0f9094fe4e3358e21149a10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c4fdca02f2b0fa0635bfcc56d8a93b7", "guid": "bfdfe7dc352907fc980b868725387e9832fb119beaa4b97076d04c672ba45f69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983209df7a331cd635b6d4c96e693188d4", "guid": "bfdfe7dc352907fc980b868725387e98772aaecdf920a5ce95ba537f4880593c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980528666b8c9eed310701d09503887612", "guid": "bfdfe7dc352907fc980b868725387e9849030c66e5d4876e4a2c39014fae619f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817148a923328876cd0d2e82c7eabeb13", "guid": "bfdfe7dc352907fc980b868725387e98bab69c0248b0259900b3558e27fa4f45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7ef0eafb99db397c69abde425017d3f", "guid": "bfdfe7dc352907fc980b868725387e98d392cf6b4e602aa49a7b973906e3a739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29df08a065e667212877fcad156f80a", "guid": "bfdfe7dc352907fc980b868725387e98b847a8841271afb625f3f6201c49fbb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e680ef7b64d0acbdd904b459493da7", "guid": "bfdfe7dc352907fc980b868725387e98f997f0e100c79bf3b85a2ca22bfe7e09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856eda78925ba45da4f4842f40168ba50", "guid": "bfdfe7dc352907fc980b868725387e984a83083caad7950d66fa7992f76358c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998e0ba9841386566fe0389ac86bff79", "guid": "bfdfe7dc352907fc980b868725387e984e7f998117b2c84fc429ab87b6ce93f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98124fbf46ad4689e1c50becb580eff3a0", "guid": "bfdfe7dc352907fc980b868725387e98a427518a69db49ac5f4d4304f164cf73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5ebb25cb80ab583f760b6079518c363", "guid": "bfdfe7dc352907fc980b868725387e98cf0940bf141e422465b4a464dc65981e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a175ac0672372a61fac85dd308c2c2b", "guid": "bfdfe7dc352907fc980b868725387e98e1c0e9eeda2e888e04dc33172c9daa57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b314f3a065f29f8980f1be34136d3563", "guid": "bfdfe7dc352907fc980b868725387e98dd2c9e9831f44d50e2a061a959505782"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98058a964042e03cc9b1d47c91634ac310", "guid": "bfdfe7dc352907fc980b868725387e986b06b340dd9697dca4c3be1aee511004", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c6a1c3e5058323b6e42c0a460d4457d", "guid": "bfdfe7dc352907fc980b868725387e984475199137b8d3e1b67a7227cbde6191", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c3c38366a842afd6c49a5896100f03", "guid": "bfdfe7dc352907fc980b868725387e98dea0e2141fefdb18cfe2809005808a45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980835def1a0a9eb5cfbee0c4f23393d24", "guid": "bfdfe7dc352907fc980b868725387e989a2c9c176d111a200173a801912e0cd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e34a590e086138e7c759b4c7ad6a423c", "guid": "bfdfe7dc352907fc980b868725387e985541e13b18b7dc7f5a8591b09494a035"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b0c3588dd7b575a1034d7ddc505f0d8", "guid": "bfdfe7dc352907fc980b868725387e989b60d319420604f8873a4b8e106bdda0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ac15778e9cdffcc7dd86c9e649acefa", "guid": "bfdfe7dc352907fc980b868725387e987cd582f0e8958a6e6336a28fe2b3c4a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e2c353a045eea260983a4b7fa35976", "guid": "bfdfe7dc352907fc980b868725387e98bd19c91db224d868de59b35828f4a1f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a3b5b479ec5427139e2ebe35569c1c8", "guid": "bfdfe7dc352907fc980b868725387e98b03c9543f16837b7fe3c27e2fcac6a08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a764960af3c4162948cdb141d0f7d1b4", "guid": "bfdfe7dc352907fc980b868725387e981d3a18eed9de127c6a567d9e085a0529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890dd1b8abbc857a766ae1cc0e3e0eac9", "guid": "bfdfe7dc352907fc980b868725387e984dae7ed18d3e302db5c691f8e41912f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d853c45c99a6cc7237abcabfe58cd454", "guid": "bfdfe7dc352907fc980b868725387e984fb4fd12af3d76cc0948a45db3777c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840e822a7ab15d1780bee384aea53039d", "guid": "bfdfe7dc352907fc980b868725387e98dee0920ba90fca098cbbfeaf5b1e844a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdda2113019c612f5b170b14b836d46e", "guid": "bfdfe7dc352907fc980b868725387e98ec16d5fbc5ee890aba7ee7151cee3037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985450f9db90df179158d0250f7a27d7ff", "guid": "bfdfe7dc352907fc980b868725387e984b53e8c94529720d2c1e875460fc2f40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c71e988bbdd8c8f0dd6472202748745", "guid": "bfdfe7dc352907fc980b868725387e987c647ac6e2527cb9bdc9a06f51c4b10f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d01385b6275c26911c13795ac10262", "guid": "bfdfe7dc352907fc980b868725387e987751f97b6d3107b73da88b501aee14dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a339925ffb9930a4080424abba6635e", "guid": "bfdfe7dc352907fc980b868725387e980f1417a0bf741af40ca2e6e07b57b66f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ff19a6b50888a57e4324a2b25d383af", "guid": "bfdfe7dc352907fc980b868725387e982b0715684a16af46654024e855cee8b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb9957e9fe8b76b302a6aac1ad5065bc", "guid": "bfdfe7dc352907fc980b868725387e980a0a7d7225836261a552bfa2913aeee1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df38b04597e8c7ff1b6d9acece21ac7c", "guid": "bfdfe7dc352907fc980b868725387e98775317f5bc8f6f844eb63dbc5a5cb7b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad0b98a257203f08f09c22bccda839e6", "guid": "bfdfe7dc352907fc980b868725387e98b38d58fb64775e72d94b25a1b63ec6b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfa7e5e76acc779f88909a1ddbf9508b", "guid": "bfdfe7dc352907fc980b868725387e98800d6438440e381e0c46044b4db86288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af09f5e5858597a1cf241317f432d536", "guid": "bfdfe7dc352907fc980b868725387e9895adcaa11b85c948ff154da44acc26ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fbddc166ccb45c48f93af6546336e37", "guid": "bfdfe7dc352907fc980b868725387e98e72dd95a53a83a4e7620298c7439606c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98440182671c3bd14487b709857c04c824", "guid": "bfdfe7dc352907fc980b868725387e98c09fe8044a76eb987f1b6a612d32ffae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca77811027bb2838f63763c7b7bc05ca", "guid": "bfdfe7dc352907fc980b868725387e982022a98d2ee48851ed7ab4ce2885d657"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866b910e5a910103e38d6cc36b81fd7e6", "guid": "bfdfe7dc352907fc980b868725387e98530bc7e7a13a1299ca7c0d1e7082c138"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ffc87e21707d1c135dc06cd1d99fd30", "guid": "bfdfe7dc352907fc980b868725387e9805e2505fef3996c22f6fa121913cf06c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856e853d192ba25bbef065e0cb34f5c2c", "guid": "bfdfe7dc352907fc980b868725387e989cd729e02ab88f2318d5aed0bfa3a911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810be3074c49cccbf4af369d092b8bb7b", "guid": "bfdfe7dc352907fc980b868725387e98426e97b62f70c7ccf1cd96aaff304588"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a4e893486de830d2486055375177eae", "guid": "bfdfe7dc352907fc980b868725387e98a7b3f00ff61c7fb9df4ae683616cb771"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890578beee8857ba4a1a6f7f24f525f99", "guid": "bfdfe7dc352907fc980b868725387e986320aab748f3707656513bbf1bf0a988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af57ff9a465eb71650843bfe44ac17d5", "guid": "bfdfe7dc352907fc980b868725387e986c332f36bc0fe1cd216d9b40127df6d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989137af2708c2702ef58612ca6806f181", "guid": "bfdfe7dc352907fc980b868725387e981e1dce83b776b69ee4f760a855368944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f71dad62f9f14a5f4edd0db3d0ded1d3", "guid": "bfdfe7dc352907fc980b868725387e980f2e98a4c9f394c93f5b8b719fcdec61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7789eae953788ecd62e3e0545e25e15", "guid": "bfdfe7dc352907fc980b868725387e9872da628d499066e27ddf89405d39b499"}], "guid": "bfdfe7dc352907fc980b868725387e982230d27f4ab53888a340d8f66be5c205", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d430650e369966812f1ddd47f5f3cef3", "guid": "bfdfe7dc352907fc980b868725387e984834d0ece22df386f0db2c537d65edba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee2bf8fa23bbf28ef0bb9342f55762b", "guid": "bfdfe7dc352907fc980b868725387e981bd5aed2534ad138e8c508c07f584aa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deca99ae1d4c69f333ded5eeff1e46b8", "guid": "bfdfe7dc352907fc980b868725387e98e5100ecfc04abba70652b2b7f95c29ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a418e8002cf07e3859a88f0e40536ba5", "guid": "bfdfe7dc352907fc980b868725387e98e3d80e7d6f9c10079983abe7f1447e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddf6050d7c3d720c5eee0a3229277b6f", "guid": "bfdfe7dc352907fc980b868725387e9820cc504221ff915cedb8a1ab58ef5685"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a0ed735a9d2786e9b4acc1708053bff", "guid": "bfdfe7dc352907fc980b868725387e9834893cd1361608358c268a239321f1e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867089995b5f36729846e3f120d948217", "guid": "bfdfe7dc352907fc980b868725387e98e3603525b2deee78c24bbb312a1971ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e61dbdab17c0691f8fa2db0219955530", "guid": "bfdfe7dc352907fc980b868725387e98c648b3c0df8465194736735fbab5a1b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d7195f26cfc577834fc374f22916543", "guid": "bfdfe7dc352907fc980b868725387e983756f307bf89f7b7e89a40f39df8607c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c03db74a03e22bd5ee88032bb8f5340", "guid": "bfdfe7dc352907fc980b868725387e982a7358304176b52f5886ba929d6f3d36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879c240c4ae5ee0da41f5e7eb980de82c", "guid": "bfdfe7dc352907fc980b868725387e98030be3eab4a39a35854547a19b4c32f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab5396fd079d11f38570e9893f8c25a8", "guid": "bfdfe7dc352907fc980b868725387e98182f47e6b592f207a4d06ccc44baad00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4bc3820b0ffdda5f589cb6a9eeeab8", "guid": "bfdfe7dc352907fc980b868725387e98a6af85d81bdc02a7d1f8caae287b07d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c75aba537b5cff92b7bed370e158f76f", "guid": "bfdfe7dc352907fc980b868725387e98436a0f009bf8c6a2f35080d9f8a6b89f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f9331a9f6e3a4d95459fc0d20daf89e", "guid": "bfdfe7dc352907fc980b868725387e98c8d2a517e364df1e5bea2172e8551ef5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4eb16b4861ed7bbff2c64ee29b2627d", "guid": "bfdfe7dc352907fc980b868725387e98f83876a0f1800a770c3a7fd0768f7445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae9885d85f53319616dc57eb2036ab0e", "guid": "bfdfe7dc352907fc980b868725387e9830250e8b1974d739ad106c4e85eb7231"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b353677af5853cab2c22ca17a5a74d5f", "guid": "bfdfe7dc352907fc980b868725387e98c93e78c3e1acdbe4415b19738ff8bdc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a13d7a6b8ff33d538ca1d372077d1d81", "guid": "bfdfe7dc352907fc980b868725387e98ee34095ade95b16db6c715361b539f02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887b7b76c6d0c23cf01305bfe87f9e57f", "guid": "bfdfe7dc352907fc980b868725387e989b9eb9d33536bab4e9f1af370bab8133"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ac6a980d6e1b979a548f64970a5342d", "guid": "bfdfe7dc352907fc980b868725387e989b0532caffe7501bf37ecc8293939c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac8cf8ac855a5c3c95bd0ca44aa8cad8", "guid": "bfdfe7dc352907fc980b868725387e98cc6a0276eb10d238ebe022dfb767a45a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846bedda9148a5b381249653fe4469827", "guid": "bfdfe7dc352907fc980b868725387e98e591f93b1e8796b4c5d4709ac394aefa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a4df5899866a71c0f96add77d90a123", "guid": "bfdfe7dc352907fc980b868725387e98e4fd68de5ec7cae43a45c82542b5dcdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a73f918300ae70ecfac9bc3ce63c73a", "guid": "bfdfe7dc352907fc980b868725387e9852ba7e2f9a1631485f669d26f99241c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98703ffb73a212b4572e3157f28af3fb2f", "guid": "bfdfe7dc352907fc980b868725387e986b1dba210b720ca5055b357332a0db33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803083fe03b257d2f3d7c20df2d944f4a", "guid": "bfdfe7dc352907fc980b868725387e98485565a30b6ebf8db12b16da6f19e3fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ce88640de9cc2417524fb9c2991aa78", "guid": "bfdfe7dc352907fc980b868725387e988686209d7f92ef7ef663dfebebffe120"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7b8790eaa0e6a2ad537befe43b93146", "guid": "bfdfe7dc352907fc980b868725387e980d2180914bb595797d7e9ecfcec477bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98535444a3e18fe372e5db663d4b8c7bc2", "guid": "bfdfe7dc352907fc980b868725387e9856fd4df1ff7241b04fb14d027c111da4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f487a8c34c9118883a64a78500faad5", "guid": "bfdfe7dc352907fc980b868725387e98653c399a7ccd8e72b20f7a91d86569e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdb9b1142bc5c65af2b290e4eae668b0", "guid": "bfdfe7dc352907fc980b868725387e98162afc1bb28c776936fbcf862a869be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d74be4fd63507560b7ae22afa6efea9", "guid": "bfdfe7dc352907fc980b868725387e98f08e2528b5c8f27ff1749c1a2c8764d0"}], "guid": "bfdfe7dc352907fc980b868725387e98b4ea9d9fd0544edef7d9488d0c25276a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e989cd53714e6b485e60972ef91d7770ebe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8de0ab70f2c0fbe5d9ba1a1011119", "guid": "bfdfe7dc352907fc980b868725387e98a19b4070d66a795db92d23a7ebb6b905"}], "guid": "bfdfe7dc352907fc980b868725387e98b72a9126e4fe9fd2e8f37bd70a168046", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e6fe966a7cd4217ffa79af671ec10e01", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98ff5674a9f3eb8262392cafe0b1d7a340", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}