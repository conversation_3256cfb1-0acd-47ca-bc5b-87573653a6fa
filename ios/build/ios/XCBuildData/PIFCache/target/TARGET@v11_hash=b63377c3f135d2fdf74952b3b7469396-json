{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989e39a14dd58c508e2245277eaeccf85a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c2cc7086e10d5816761fe3e2a77b362c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c2cc7086e10d5816761fe3e2a77b362c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98171fd5b8e9cff1cd6b4129fc50f4b2a5", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ebd6c4e237b975a0154d9368b00cccdb", "guid": "bfdfe7dc352907fc980b868725387e98421ae3c3425f12162bb95608984e2dab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9373f0000c34f80739f6d597a64d6b7", "guid": "bfdfe7dc352907fc980b868725387e98b1129dd415ba4f06f295293352794aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989654508a7baf4e1d6de984d8a90baf98", "guid": "bfdfe7dc352907fc980b868725387e98c4d6312cb67f31d7f6f6831dd281e8b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c57bd26896fb85db2c220c1a33f500d", "guid": "bfdfe7dc352907fc980b868725387e98f04a5c9a9cc81bfcfa6aaed0b00b6c3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f905bfff041982da2c8292b679a5d12", "guid": "bfdfe7dc352907fc980b868725387e9843b79fcddec6fa700bb0007202549f98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a01a215eddf28df64400706a5f093af1", "guid": "bfdfe7dc352907fc980b868725387e98c352e5d7dbfbcf376cece1780e77fb1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842d8305f6e5ecbc0d5e29b4cd7e283ce", "guid": "bfdfe7dc352907fc980b868725387e980e3bf0955a353ef83519ce8948a63fc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867d4ee737636a0b70312ed45b431665c", "guid": "bfdfe7dc352907fc980b868725387e982fba501f5a0df4030b4a47f567f7966d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833b5ff41f371012681474ede3f6f6612", "guid": "bfdfe7dc352907fc980b868725387e985f0b6bcef15399d61fa1794fdb818de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983217761a2610e55bdff851ac38fe2f9c", "guid": "bfdfe7dc352907fc980b868725387e98abea775179281a469ff2dd1142cb783c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878ef85ef7a70fc72168aafbcb20873eb", "guid": "bfdfe7dc352907fc980b868725387e988990c98018a3c452a437cf64f7a7b3fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822c46b99b2d4ca424fefe760888a853b", "guid": "bfdfe7dc352907fc980b868725387e98b63102342437207e7dcb551a084a95bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816c59bd75582b1ff1598909cc5aeeeb3", "guid": "bfdfe7dc352907fc980b868725387e98630bd2be35e33853378d0faf1591cb1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980825c65b1c0e0e334225a4abf3c34bd1", "guid": "bfdfe7dc352907fc980b868725387e9864d155bcb5d32ec1f6dcd38e047c4c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801f4bd3c46bf6e055eb8ed2513bf537f", "guid": "bfdfe7dc352907fc980b868725387e98a43972be6129f885eea405f17f739dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834e3dfb3f1f5398a75c976904acce8ad", "guid": "bfdfe7dc352907fc980b868725387e98c448602647b7c43f19fc7f2b0a19882c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df07c31dcc553f88646f8b613f91579e", "guid": "bfdfe7dc352907fc980b868725387e98dfaef89466e79b28a06fbf0d65f3fbaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98865d812d1edb2330428d12f4e7d96248", "guid": "bfdfe7dc352907fc980b868725387e9838606c9f15f987693f19420c77bf380a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7016843b7ee97bae96a1dc92691546f", "guid": "bfdfe7dc352907fc980b868725387e988957ceb19237fd9d69b8a1d6b4cf5425"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d8bd10d351ab90a0a6beb10e9418560", "guid": "bfdfe7dc352907fc980b868725387e98e15821a1a930d75a5f3933f083e375fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863a45f56166c800a0d4a9528dd9d084e", "guid": "bfdfe7dc352907fc980b868725387e9834028e19953e0b1ed245f3913ae35704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987323ccda80a0a2605e05d5fd2179f0f9", "guid": "bfdfe7dc352907fc980b868725387e9834aef25fb16e03a76b8bbf16d245c607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98768baaa89803083f138fc9e8545e5648", "guid": "bfdfe7dc352907fc980b868725387e985546f740b510dd60f5bc27de71daf43a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acbdfcd1169d6a230fa64ef6692a95bd", "guid": "bfdfe7dc352907fc980b868725387e9837f0f173397389d1816969e4e270046a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6be8d639808f28af5978ba019b45885", "guid": "bfdfe7dc352907fc980b868725387e98610dd3c4324e4d24978b8cd7e06bebc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2657c6ce5f26a585a7ac30600028a64", "guid": "bfdfe7dc352907fc980b868725387e9847c83ba16e50506728ebf97d34be80b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e9ee0bc1896beada2f96fea2ae26b34", "guid": "bfdfe7dc352907fc980b868725387e9894d77dc19091448c9de21434a0e30f0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fe37f90dc67d09daa69a9f38f222bc2", "guid": "bfdfe7dc352907fc980b868725387e983180877e88a88e10d1a87c3cbf76b203"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3fb2edc28e3c02db02a07929cc0c90e", "guid": "bfdfe7dc352907fc980b868725387e98a0247398fbdb89c3988b46155d18c067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832deef56f8193a459577f87e58036059", "guid": "bfdfe7dc352907fc980b868725387e989a4819ab88a56141908e0669a1910c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890844baaafc519ee3bad2b46708af936", "guid": "bfdfe7dc352907fc980b868725387e982e29b240ad071385cb99642f4151739c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987060a38aa58e1a579fe8c2da6e3b26d2", "guid": "bfdfe7dc352907fc980b868725387e988c2c14f49e3b26c79b06e7b8b6101921"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847412e49951e82dc85e5d958975ea9a3", "guid": "bfdfe7dc352907fc980b868725387e98922e57193746f7f5fdd4494b90d735bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d47a62059dc84d10057e6cfb177235", "guid": "bfdfe7dc352907fc980b868725387e98ba3fb8295e51139e9cce240a1e715d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831b3bdc73f2286843f067e61e3c50b66", "guid": "bfdfe7dc352907fc980b868725387e9874987f8de6c5b4c86bf766cadd18dedb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98532d163f9770d4436f09d213ab36270f", "guid": "bfdfe7dc352907fc980b868725387e9853de38235b70c71345bc88a3f70fb6ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985292c36cd761c373c3e0e8c6fbdc3541", "guid": "bfdfe7dc352907fc980b868725387e98576d77e91596b9da0d88acba8efaf0c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d22e3ef51a77c8c4f06e2f5ae797828", "guid": "bfdfe7dc352907fc980b868725387e989f90dd5960fdec2d95b170b382f26e6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98225e27c03956f2757e058b14a8ba6d49", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab0673fc37a1da9e889b1312cb6b6103", "guid": "bfdfe7dc352907fc980b868725387e98f907c5a490d4761e6ce9b01cae1c968a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98895b0fc55ccf954007b2cc322c94d115", "guid": "bfdfe7dc352907fc980b868725387e980d3613196654dfcf6a896937bb373434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cc3f6128e644c851a1f01f6e58ce2fd", "guid": "bfdfe7dc352907fc980b868725387e9818cf0c36a9406704d29f6d45ba79c104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98072349f4698795a6e3110ba29f0cc4a1", "guid": "bfdfe7dc352907fc980b868725387e98d09bffc616c2cc7ff369d082d9774c93"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}