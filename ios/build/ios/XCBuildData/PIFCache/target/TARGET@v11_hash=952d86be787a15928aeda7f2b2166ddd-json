{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd13af76b1dc43808fa9d92caab5563c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839c2f902f5df5720aef3e02cdb5e420d", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839c2f902f5df5720aef3e02cdb5e420d", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98406ba315feb772ef54826cef8dda81a1", "guid": "bfdfe7dc352907fc980b868725387e98a27a49b00be0ac981e5db9f07832563d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849c4dc659f8cfcdfcd9bf9eac330b0d3", "guid": "bfdfe7dc352907fc980b868725387e989878394bd4334bc5b75f69e19db62bf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f129db7ad96c8d47b229f7a6284ce04a", "guid": "bfdfe7dc352907fc980b868725387e98e1897e3d8aed17671192779f85b1c248", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ad1ef9cd19215b0ca354031a37a61fc", "guid": "bfdfe7dc352907fc980b868725387e981450c482f5c290824d437888a184ae8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888432068f100515e4737bf30d2c422a8", "guid": "bfdfe7dc352907fc980b868725387e98e4fc9878a7a9815c87ead588b617e13f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f355fa94c6a78ca96fa63882d8aa219d", "guid": "bfdfe7dc352907fc980b868725387e98931f24651cd163bcb560cc6bf0293fcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fad43ed9bcd1e129d265188b4e637cb", "guid": "bfdfe7dc352907fc980b868725387e98d4cfb9e1cbc63fdd6505f85f9a29df9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ce6479f8b21be3191df1831eb5f2180", "guid": "bfdfe7dc352907fc980b868725387e98603cbbc70b8dca9ffe9ae016055178e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c6065287cbdcdd791f48e6304592c7", "guid": "bfdfe7dc352907fc980b868725387e98de1a9153e9195626d2e18082bddd5583", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b6ebe09c64f745f77d2ed22b0d3f7e1", "guid": "bfdfe7dc352907fc980b868725387e984a1d400fa247fcd1999957a8682fc831", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193f4ed80c133f1387ad8d23b5a6cdf4", "guid": "bfdfe7dc352907fc980b868725387e98d32709d3a2dbac2c57e7e408179d0866", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989909bb473639f852d441ee23df97595b", "guid": "bfdfe7dc352907fc980b868725387e9852b51035de7e7268120a09bd61060c70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98312befbd3daa3705a98e82375d4d18a5", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdce76e83b612975e62abccbed003366", "guid": "bfdfe7dc352907fc980b868725387e9851124daa4e024f512c6435eeca4166ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa2e7d31320f7f0cd563c19f5b5e338", "guid": "bfdfe7dc352907fc980b868725387e98d1ab4894e5f8a787a882b5c8c35da9b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0bf6947b3c1373e95a2639681d7a9c2", "guid": "bfdfe7dc352907fc980b868725387e980ef321af2f9f2c834e49de13f3d55bc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4c0a3912c0cf5341a8bfab939fbc87f", "guid": "bfdfe7dc352907fc980b868725387e98f0d31a429c82f17b0991aeffa8fa048c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98184d8959d7370f84bfcc4acfe5ae48fa", "guid": "bfdfe7dc352907fc980b868725387e98a42a0c118dedfd60efdbb4d16492c7b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985265a927cd91329324838bcb9fda4cb9", "guid": "bfdfe7dc352907fc980b868725387e98b3c686ca16e5bce7ed24eabc68c27a7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8c2460fbc0bdb4feaae52cd0e343a87", "guid": "bfdfe7dc352907fc980b868725387e9828accb1b5b23f919db719dddafe64428", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e928bd2c08158be04153c658782367a9", "guid": "bfdfe7dc352907fc980b868725387e981a70531bb3ca84e549cc6ba35f299fa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a044bc19df5c86b29409dacb20e8221", "guid": "bfdfe7dc352907fc980b868725387e98d126da608ed212ace70c11a62a0c1637", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891b5990e4d1f54c514301ed99a80c4ac", "guid": "bfdfe7dc352907fc980b868725387e98482d9659cbec4dc229fbd14d4097dbb0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9899dcea4b2c99c5549c1c7c7d7fb6009d", "guid": "bfdfe7dc352907fc980b868725387e989514a07acecd425069c301ac83629a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6e60d77025cdbe02272ceff0e55d852", "guid": "bfdfe7dc352907fc980b868725387e98c5aeecdf68b8bd150764901c66af09a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b9153c2fcad8ebbd4ad567b3cad75bc", "guid": "bfdfe7dc352907fc980b868725387e98845832e3ddc9b9efa82804181189873f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c11ab73333d85e238431268a6d55b575", "guid": "bfdfe7dc352907fc980b868725387e988422c8d41a531ab0e5feda22b2729450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb6d9ef4eb1d1b3d7be8ad4b9a83ddf4", "guid": "bfdfe7dc352907fc980b868725387e98032b5432a99c409a15c23484d253896f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fdafc1eb1c20d8df438e29b8b959653", "guid": "bfdfe7dc352907fc980b868725387e988ec79e04b31e29a98b9058ffe01c7fe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3b8631c88ebbff8ad5ee89d64d69aef", "guid": "bfdfe7dc352907fc980b868725387e980e70a7a6c573b1b7ccd3e1d5ab5d07e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edab2916d9e5c60a5dbb7abc8bbee4b3", "guid": "bfdfe7dc352907fc980b868725387e983e056fb9529e4b65a3f33b7e5569c272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98626da34306a96d105ff5360218266644", "guid": "bfdfe7dc352907fc980b868725387e980341c01be7632895d55fd1dac2b54ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986296604bd7870fdbd86433fdd2a5f643", "guid": "bfdfe7dc352907fc980b868725387e9844b9c045f1529acb68ed027e0a48b614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c1089d4bb76f3ce0c4cf7802331b752", "guid": "bfdfe7dc352907fc980b868725387e98b1b6e64bf6811487ed4c6e1384077226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daa095d9d3e37424aff64d81dca65495", "guid": "bfdfe7dc352907fc980b868725387e981f2b6f47d148085767ead35147c92b0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f154a77c42d4bad322c4c18006c275d4", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bf280b0cc2191b363d480382b60408d", "guid": "bfdfe7dc352907fc980b868725387e98ff4558f6e06cbb7fbd15dd015ca18854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9cd1b0b6efc24aae1a46fda77d4e117", "guid": "bfdfe7dc352907fc980b868725387e983caf79b54a68f27872c153b09b7289cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98859491cb4dacbb3b23019e7a221624a4", "guid": "bfdfe7dc352907fc980b868725387e983550715f5dc47c6e779aeb12290a7c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98563e9e825c55cfa658c227ddc95f6ed7", "guid": "bfdfe7dc352907fc980b868725387e985800f051739a623bb35f7cc22cbdb0d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1dbcca9142aa34470524d84db48114", "guid": "bfdfe7dc352907fc980b868725387e98f2203b5d78f4ac52bb8f2618d0202e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f22ed368491549a50fc6624e80dee25", "guid": "bfdfe7dc352907fc980b868725387e983616275298339a71f8ce5405edcae646"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842c2035920414844f38872ce0c74a6f9", "guid": "bfdfe7dc352907fc980b868725387e9859b2f7d951b31f73b36500eee5613569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137722496bf648e67dd35dfeacff0f38", "guid": "bfdfe7dc352907fc980b868725387e9800fc7c6d98b1d598e7f0946391c24f5b"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}