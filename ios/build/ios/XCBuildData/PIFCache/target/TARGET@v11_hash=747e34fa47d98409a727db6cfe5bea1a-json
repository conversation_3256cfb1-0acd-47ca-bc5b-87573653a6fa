{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2f8eaddfebbd9f0d9ca584ea3da51e6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a969b1ea0512f079a77fb3970d413e4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a969b1ea0512f079a77fb3970d413e4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a643a19e845cd01bcc1b4d47515214b5", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b1895e4940ea6f1243df64e0c30672", "guid": "bfdfe7dc352907fc980b868725387e98a7c9946cd9b8478eba3bb8081bad13af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c2d205defc6bd656c9b5db34155b01c", "guid": "bfdfe7dc352907fc980b868725387e987160619f7e74535effae5807c0131553", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987389df71e95720f3d86e417243810373", "guid": "bfdfe7dc352907fc980b868725387e985bb6075fa28a6107a818457c07801dfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875bfb323ea10fe64b54a2d2cf793d341", "guid": "bfdfe7dc352907fc980b868725387e98caf323b97dc28222b35abd8996f3c812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98152735dd9c3d9857fc1e7ea9e03b020c", "guid": "bfdfe7dc352907fc980b868725387e985d4fec79514d5497b07a0562fe80d6f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db65d00330174dc96dac3d81e53a2159", "guid": "bfdfe7dc352907fc980b868725387e986ec505071cb7c35d6ef5f07e8dd39b42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983af06971be2e4121cea5a76cc05c8498", "guid": "bfdfe7dc352907fc980b868725387e98a135f8c35d528cd7b7f746e8e379f04d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812b45c85c3bd865cb4de900cff5cbbb8", "guid": "bfdfe7dc352907fc980b868725387e9824a530e23a38b76097a9c8fb77d4f779", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872c214403f66d554ee105c197a432f4f", "guid": "bfdfe7dc352907fc980b868725387e984a130f8e6018e7bf90d06fcdb0686119", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a33f731599c7b4755640c41585fd3c1", "guid": "bfdfe7dc352907fc980b868725387e982f2dff334fd8d14193ab3de91eb57488", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980751ea8af02089293d23604708f1f585", "guid": "bfdfe7dc352907fc980b868725387e989e55deab9190470da5cd73125c73664d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5babab59eb18967bfbac7a19176a27", "guid": "bfdfe7dc352907fc980b868725387e9883f4e0d16204967f7f2ea435092dab3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891a4c5b8a6a543b1ed4e63644f6d1bd1", "guid": "bfdfe7dc352907fc980b868725387e9836603255017083941b67ed28165b4d0c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d43c1b1fd815169c43103533850a9faf", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed9efd3a9fd6c1344f3ff2e9c482464", "guid": "bfdfe7dc352907fc980b868725387e98c9b82de250a8b806d712239a43746b20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98239588ca52ef022ebeb726ff821c8a07", "guid": "bfdfe7dc352907fc980b868725387e983be28730b7a59890c3ded9773f2204fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c85a8555b652c200aba5658b443fa91", "guid": "bfdfe7dc352907fc980b868725387e98b34eada4e943386f3a2e262aad9a223c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d55944b112a26848c7cfe13807ce189b", "guid": "bfdfe7dc352907fc980b868725387e9878c036a1a80c8c340dd518839a66e199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f3a0ec3883895df1aab0eca2f57cc3", "guid": "bfdfe7dc352907fc980b868725387e985051ecb2fdde934402e1fab55129e0ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dd1ed721229989bced9aa06a7337056", "guid": "bfdfe7dc352907fc980b868725387e985fde8070fb89e2fbd3a6defdbd59fca9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb9a27dbfbc8df99da35ded48b5a58a7", "guid": "bfdfe7dc352907fc980b868725387e985506181dd7d0a177818c2a073e2f8a3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2dc86ef64d8aa8cbb3e30a54dfbd177", "guid": "bfdfe7dc352907fc980b868725387e98f0e80d7f819541d6e546dda14061d339"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d584848a2ac82810ce7e2d8a1cbc0d0", "guid": "bfdfe7dc352907fc980b868725387e9867a13716edf39eadad6b7ed994408273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98474c0f52050e5802b7027a83611183d4", "guid": "bfdfe7dc352907fc980b868725387e98124d25b42780da3607982738727ef9c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e731a7afb92d9fd8482fdb33c5a01b5b", "guid": "bfdfe7dc352907fc980b868725387e98370896374df3d7c01b508e6fc79c5c60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd776954963594ecc90d3befaad04f5", "guid": "bfdfe7dc352907fc980b868725387e985fbdd1bb57428cecf91292ac16f6b517"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}