{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980cbbda2fa0db7657e625a613147255f6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980cb581621e1dc4a48921f3bbbb3d6121", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98592c8be62e15e87ca2cfdfa7ac8fb9fd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985236a33d19932bafcf39ec78e05e9d4c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98592c8be62e15e87ca2cfdfa7ac8fb9fd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98de1abf1888c617922eaf308d830496f8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b88d3fc2d3bfec1572c147703af83e91", "guid": "bfdfe7dc352907fc980b868725387e98cdfd5fc00a9d51859211ce305a2def7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821cc68830eec65eabbbd08f7d382f982", "guid": "bfdfe7dc352907fc980b868725387e9851bb33751490e982c317c710154bb7ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dec440068b6d380babbd7b5896164ad", "guid": "bfdfe7dc352907fc980b868725387e98a960e0f8aa636c01914d2f77a70ab1ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98151bd7cdcd30afa96c6b1ce23b3b3a01", "guid": "bfdfe7dc352907fc980b868725387e986a63b23afdc0b671a043658d3124f12d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f23d0979a164d198fe27fd56abeceeb3", "guid": "bfdfe7dc352907fc980b868725387e9848068645af713ae41d84a4fa7431fa27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98416d9272ba655004424a35d1ea47b8cb", "guid": "bfdfe7dc352907fc980b868725387e9838f94d9b2fd0fad438214e49e2116c80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ac35297a678892bb5411fea8564684", "guid": "bfdfe7dc352907fc980b868725387e9855740c439223361e967c37ed248a8697", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd070665086a310d186ff51a461ba428", "guid": "bfdfe7dc352907fc980b868725387e98ba8ffae276017999249b9f7c9fc37c29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882cc9b35873318b6298d505760e485f9", "guid": "bfdfe7dc352907fc980b868725387e988dab2d5ff8384f46a12724796ee888d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98832ad6d305c01fdc426761fbb3024968", "guid": "bfdfe7dc352907fc980b868725387e9828e42602cf4daacab2d7377848250211", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98817670c623ca0810f660c7897f936af7", "guid": "bfdfe7dc352907fc980b868725387e9860a562030294dab4b520f9959eae6803", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b9c661df4b13f0d98b3862841122207", "guid": "bfdfe7dc352907fc980b868725387e982807f159aa2878465064d3250c1877d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8f71838680fd9eba517179df4ea601b", "guid": "bfdfe7dc352907fc980b868725387e98aa658d05fdfca135d5e852578f90b451", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d8a48a0721935b1289ff9a6e3aac945", "guid": "bfdfe7dc352907fc980b868725387e9806f97348d60f55ebb2fc22648f22245b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987117fb7db2d6cfaecf95a77c34eebc71", "guid": "bfdfe7dc352907fc980b868725387e98b886e14c22c7ed6dab574482ef8577c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846a0a3f6600c5cc0d1c2bffcf10fa26f", "guid": "bfdfe7dc352907fc980b868725387e980cdccd328bfbc61aebf5f9d4f7e5a5e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842d0913188da88163e02c06a8e5c99c4", "guid": "bfdfe7dc352907fc980b868725387e98914a6e73e3f8213eb0f1b41ccb8c9f4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98149791f99623c2dde41fe2bd28e42c35", "guid": "bfdfe7dc352907fc980b868725387e982cc8f6484ca7a44180b0fa60f2186b33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a57c863c7bd7536e01d6a5ed33e1676", "guid": "bfdfe7dc352907fc980b868725387e98d99b07bb1e56e77ca9f12111e638a91a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888d7669ea19e5e8f410ae010ccdd8870", "guid": "bfdfe7dc352907fc980b868725387e9822e37bb5c89a743119e5bd859f85f175", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989108000b5fb239e2d08575b815f3a754", "guid": "bfdfe7dc352907fc980b868725387e985b5736ee241e73cf34493e915065d1b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810c83d17987d8448f522098ad6a74997", "guid": "bfdfe7dc352907fc980b868725387e98a49ed25ac8c79fd074b80af50289c4e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b40c59cb417218b1bb95ae314c26c964", "guid": "bfdfe7dc352907fc980b868725387e98896d81d5c7a8c93965b48ef3bbe67c2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877cd4ed48fc748d1b4010d2a5de7ff1d", "guid": "bfdfe7dc352907fc980b868725387e98b0d24d85a8c3abbb8d3f89651efab46c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd4e2db0181410e15c26f77e1d6c3b1", "guid": "bfdfe7dc352907fc980b868725387e9802a192ee8bc29b3ce49307f82b6d5bc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dc7599795154c8866d490be48abaada", "guid": "bfdfe7dc352907fc980b868725387e988f91d6a87c169b4d272a2ce5f9601caa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d447296378412d5a01443d954c4b4d65", "guid": "bfdfe7dc352907fc980b868725387e98fc51f969fcda3dae869fd9244c7edb30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e79b04adb8a546b04c4293a2cc6cf654", "guid": "bfdfe7dc352907fc980b868725387e98f1923949baeb291e0d995344223d5806"}], "guid": "bfdfe7dc352907fc980b868725387e984a3b11d7d509271f4142e2b70e0b531a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9883206387eb5e03eb0be13206b022a441", "guid": "bfdfe7dc352907fc980b868725387e98b0f414617cebbe925850c193641944b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801b651e74f3ec08a9256c987a1ce85ec", "guid": "bfdfe7dc352907fc980b868725387e9819ad0f13d4d457c14874eeae059faf27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8c1d2b558b357097b0ca3b04867d8d", "guid": "bfdfe7dc352907fc980b868725387e98aed0db2b1141f7d27a66edee5c1f961d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98086fb4509108c7257232620f1f29a825", "guid": "bfdfe7dc352907fc980b868725387e987c39a4b79cfdede4e3d34b9c05d205cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857e6744660e292072f5b537bd6859427", "guid": "bfdfe7dc352907fc980b868725387e98fb42d0dd2b74d391a316f4e5a8368422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98649f323c4c800c0e9b08cd91c10b7f84", "guid": "bfdfe7dc352907fc980b868725387e98e4a5d414b96ad03c746318609bc1de79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8aaf507321852b1d8f26bb7ad38a50", "guid": "bfdfe7dc352907fc980b868725387e98b44b2da1bc6703baae7e31075aa3e960"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c48933d00992b22fbc68f7c748af7e48", "guid": "bfdfe7dc352907fc980b868725387e983f3786a61b244c4db6d072aa3f40dcd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a37b155ee928cca1ad5c4ecf0653daa", "guid": "bfdfe7dc352907fc980b868725387e980758f2469129e4eb4c0f83292c2ffefb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd576cdd49b760c3c2c5e303b8b03be2", "guid": "bfdfe7dc352907fc980b868725387e9828177a9c5f55ea66a4e1c1042f630f67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143ebd0630af22ce3d471ffa163eaaa2", "guid": "bfdfe7dc352907fc980b868725387e98c18bfdb9fcc2ce32b781e71564751366"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984da4b249864dd905cfbbb5c4fd7e3a5c", "guid": "bfdfe7dc352907fc980b868725387e9808d9ef402c1c477db1ba47166659329c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a04cbba1468ca85be51fabd60d45cb9", "guid": "bfdfe7dc352907fc980b868725387e98be16cff75a5a5f363ea6ea204c4cc36b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884763dd83961d971f4db6467bb83810c", "guid": "bfdfe7dc352907fc980b868725387e98866d99481d7b2c430d4fae304f81d766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98030b8212bd75a1160c298323173081d7", "guid": "bfdfe7dc352907fc980b868725387e98e327f1425f269c8ec63c130e977be9d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a834b3e8aad58ec69839e5505dd24f76", "guid": "bfdfe7dc352907fc980b868725387e98df18eae0a964cdb0c0ca8eb2a17e6fbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ce2ad1be0cc1386826ddf4831ceeac", "guid": "bfdfe7dc352907fc980b868725387e98a0cdff6578af8734a31c26ca8bf7a0ea"}], "guid": "bfdfe7dc352907fc980b868725387e9848ba9a1f9c0b7a74515914565a1c5540", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e986b90598891eb51ed3434586bcdfd78a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e987f978b813d8f00da6d6c19f6c556e8d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8de0ab70f2c0fbe5d9ba1a1011119", "guid": "bfdfe7dc352907fc980b868725387e98b10e4295a952e8bbdc6bc2999107352c"}], "guid": "bfdfe7dc352907fc980b868725387e98c3968c574b4a09ec5c45189c0dd7c1b2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988fb9516cdb649e6069c6032a8db96e22", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9855cade713d99a0181576450c822eda39", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}