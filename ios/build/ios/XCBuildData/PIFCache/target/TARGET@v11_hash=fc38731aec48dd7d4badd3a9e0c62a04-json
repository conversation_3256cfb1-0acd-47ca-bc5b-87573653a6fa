{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98059ae7a5a56ed5ceac445dfdf29966d2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983e40a912d5156fe837ae78b4ffdf3fcb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b58fb58716c9b9e0613f0793646f47e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9862fe826f7b8b1ed12579983a5784c6fa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b58fb58716c9b9e0613f0793646f47e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988d481722909075a1a3134707f84c8066", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9856b1ec74d0dc637cb5fad9e1d9fe6419", "guid": "bfdfe7dc352907fc980b868725387e98d866b86c656bb9520b49b3def5f7d7b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989631483f65f8996f199fe0c14832d07c", "guid": "bfdfe7dc352907fc980b868725387e981e6a983e2ac023336e2d4c86992a9272", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd79edcfb586fd317e728cee0ba7f0d9", "guid": "bfdfe7dc352907fc980b868725387e980d00358ed9a910840be127e6d55aaf48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4853d6b823b55ac3fbff82ded6853a1", "guid": "bfdfe7dc352907fc980b868725387e98ab7ac33a7f3ae2f5df4d42a4d838a540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3ddc88410268b692a057e27da0a08c3", "guid": "bfdfe7dc352907fc980b868725387e981ad9fe287879e27e10125ff118b700db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c5a7bc04a07cd78fc3d3766e96cd3d", "guid": "bfdfe7dc352907fc980b868725387e98e0a60d916ff0c870ed3c5385f4bfd3d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b94add6b4027f9df44b7fec822c8963", "guid": "bfdfe7dc352907fc980b868725387e98c5ad88046145d3b74e52c8d2272fcc48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f77c8deb3034b6df5d7696185674de87", "guid": "bfdfe7dc352907fc980b868725387e9844e2d4912e2bc0e4eedc5a3690f1d75d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa46801b18d544c18b405194ef510f59", "guid": "bfdfe7dc352907fc980b868725387e9829ddc69564f552a3fd4e14fec16f1638", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2911162e02452afedcac27b03e75f80", "guid": "bfdfe7dc352907fc980b868725387e983f8ec9fcad6b6bf5e033a6980505f581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79864f97fff0dbb9d3b5b92a674c42f", "guid": "bfdfe7dc352907fc980b868725387e984c92be3e21dfd23208ddc00370b25d41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98464510ff543dbf04997cc3c043a6faae", "guid": "bfdfe7dc352907fc980b868725387e98abce72950e69a4b9dc597a17f55e5d67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f0c96c9380e7d1aefa99e527267bd49", "guid": "bfdfe7dc352907fc980b868725387e9820b6f50e8ca743c6e385962e13e1f631"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98889afec00b9ea081f7e0f3e4f739371d", "guid": "bfdfe7dc352907fc980b868725387e985d3f6847d81ffb8b645c38559df51fb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e60d81ef80ef022eef9723e617504366", "guid": "bfdfe7dc352907fc980b868725387e98977a8a143035952bbebe7551665382f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bd66bf47d4ceeb53e3384972dd4cd49", "guid": "bfdfe7dc352907fc980b868725387e98707782170c4b25cb25d116d6849eab81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5b5fbf2f49d1cee68099e669b529e71", "guid": "bfdfe7dc352907fc980b868725387e98bd348cb09be1ce8947fc8c824dfdd8ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982165d916df9eba6f0fe36a4031d64853", "guid": "bfdfe7dc352907fc980b868725387e988d91d82db0553acd91a89b0f17121fe7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c89d4be911e63e213c039f1260172a2", "guid": "bfdfe7dc352907fc980b868725387e98b47312e7ef13845d162fade1945f231a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84b97c0d77b7925dfc2ecab44a0f50d", "guid": "bfdfe7dc352907fc980b868725387e9865fcb68209579bf57db675e1aa4d288f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a801e86256c6e2f393a8100f1a8c3f2f", "guid": "bfdfe7dc352907fc980b868725387e9873f0c0c7008a03bb5b6c42669b2e27c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a6a6744d3d31fbb60d309e115e175b5", "guid": "bfdfe7dc352907fc980b868725387e980e45f7f8cf36d61664e26d44808c5360"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983185096b62b7859293520e756fe806dc", "guid": "bfdfe7dc352907fc980b868725387e9832410894789b10c1c4bb5b43d0ab3b0f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d7dd73f58de676b3f0d28672639235b8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988339f5c0b9225e8d26689a07e4c259f6", "guid": "bfdfe7dc352907fc980b868725387e983d6804a884fc7af146bceca60c0a3dc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98988b97722a4ec554b0598a89c0da9080", "guid": "bfdfe7dc352907fc980b868725387e98abdaf92c4ed20124720ef8e905c18009"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb5714b366fc266e734bd2a47a7de1ce", "guid": "bfdfe7dc352907fc980b868725387e98dd0dc8aac94bb54be72728a0db057be5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3ad1c575d4767836fd792c2e97ccb1", "guid": "bfdfe7dc352907fc980b868725387e9805f38bc96a6e3d4c99bdc2f3eb566086"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f96802219969b694206ccb273182071d", "guid": "bfdfe7dc352907fc980b868725387e98e99f4762bf4a31e860e0f488968cd0dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989584ae1ac19213ea815d061a82bcb127", "guid": "bfdfe7dc352907fc980b868725387e98de249dafc5b42a570ef700aae885bff3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4ce728f74e20e380ecb7df42ba06b38", "guid": "bfdfe7dc352907fc980b868725387e981f5e9d82e74eb672a2033cfdefd565ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861927c2d6403c171d776c1b391a4cbdc", "guid": "bfdfe7dc352907fc980b868725387e984e6fd21d0dcd046bfd5b154b60c4753b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f0a925d19456bc068ac0a18c0ec203d", "guid": "bfdfe7dc352907fc980b868725387e98309119f855fd772e09519c63c0999d50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd93709fd246deea9098a64262f1459", "guid": "bfdfe7dc352907fc980b868725387e98055e8b37c51b685003775c083f1e8af5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a5482dd14de9adb9a6d1f2d7a73e5e", "guid": "bfdfe7dc352907fc980b868725387e989462451451eecef47aee162a23f86486"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4fc41f5baeb16012f6a13669db173ca", "guid": "bfdfe7dc352907fc980b868725387e983825e5507b6554e14a08084db0b05ac5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807700e186a4191577c18eec6eeafe56f", "guid": "bfdfe7dc352907fc980b868725387e98dd38c744e890a9725b4172ec8e0e06b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed08eebae2bd8edf3b852e96975eafe6", "guid": "bfdfe7dc352907fc980b868725387e98eaffff02bf341999f6648a7b6352db54"}], "guid": "bfdfe7dc352907fc980b868725387e9836777aa1041df5b946787b7c619b3725", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e980f2974982c96efa0d9f7855d2bbe13c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e51514501e0f0d62f7ef6096d26bdc0", "guid": "bfdfe7dc352907fc980b868725387e9826c5c4efd7eb0a1337692b1cd6c6ae28"}], "guid": "bfdfe7dc352907fc980b868725387e981a186d55951154d0c73348e2288b934e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98afdc3ba9ef27c2e7de48d0fca22548c9", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98423dd1f0ac93bf6aa48349c02b7d2c51", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}