name: quickposters
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Firebase
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.3
  cloud_firestore: ^5.6.7
  firebase_storage: ^12.4.5
  firebase_app_check: ^0.3.2+5
  firebase_messaging: ^15.1.6

  # State Management (BLoC)
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  provider: ^6.1.1

  # UI
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0

  # Image Handling
  image_picker: ^1.0.7
  photo_view: ^0.14.0
  uuid: ^4.3.3
  image_editor_plus: ^1.0.6
  matrix_gesture_detector: ^0.2.0-nullsafety.1
  vector_math: ^2.1.4
  # image_cropper: ^5.0.1 # Commented out due to compatibility issues

  # Localization
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  # Screenshot Prevention
  no_screenshot: ^0.3.1

  # Sharing and Saving
  share_plus: ^11.0.0
  screenshot: ^3.0.0
  path_provider: ^2.1.5
  url_launcher: ^6.3.1
  permission_handler: ^11.4.0
  webview_flutter: ^4.4.4

  # Local Storage
  shared_preferences: ^2.2.2
  flutter_colorpicker: ^1.1.0

  # Networking
  http: ^1.4.0
  flutter_dotenv: ^5.1.0

  # Local Notifications
  flutter_local_notifications: ^18.0.1

  # Google APIs Authentication for FCM HTTP v1
  googleapis_auth: ^1.6.0

  # Firebase Analytics for User Segments
  firebase_analytics: ^11.4.5

  # Dynamic App Icons
  flutter_dynamic_icon:
    path: local_packages/flutter_dynamic_icon

  # Phone number input and OTP pin entry
  intl_phone_field: ^3.2.0
  pinput: ^5.0.0

  # In-App Purchases (Apple Pay / Google Play)
  in_app_purchase: ^3.2.0
  in_app_purchase_storekit: ^0.3.17

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Localization support
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/firebase_service_account.json
    - .env
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
